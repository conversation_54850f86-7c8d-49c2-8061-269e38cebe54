{"version": 3, "file": "7154.1ab03d07151bbd0aad06.js?v=1ab03d07151bbd0aad06", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,UAAU;AAClF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;ACnGa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,oCAAoC,mBAAO,CAAC,KAAe;AAC3D;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D;AAC1D,gEAAgE,2BAA2B;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;ACnGa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oCAAoC,mBAAO,CAAC,KAAe;AAC3D,wBAAwB,mBAAO,CAAC,KAAmB;AACnD,qCAAqC,mBAAO,CAAC,KAAgB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,+BAA+B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,qBAAqB;AAC1F;AACA;AACA;AACA;AACA,CAAC,oCAAoC;AACrC,kBAAe;AACf;;;;;;;AC7Ga;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4CAA4C,mBAAO,CAAC,KAAuB;AAC3E,uBAAuB,mBAAO,CAAC,KAAkB;AACjD,oCAAoC,mBAAO,CAAC,KAAe;AAC3D,mBAAmB,mBAAO,CAAC,IAAuB;AAClD;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,gBAAgB;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAe;AACf;;;;;;;AClJa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB,GAAG,gBAAgB;AACnC,oCAAoC,mBAAO,CAAC,KAAe;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA,sFAAsF;AACtF;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD;AACpD;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA,uDAAuD,IAAI,eAAe,GAAG;AAC7E;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;;;;;;;ACvQa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,mBAAO,CAAC,KAAgB;AAC7C,mBAAmB,mBAAO,CAAC,KAA4B;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA,CAAC;AACD,kBAAe;AACf;;;;;;;ACzCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,wCAAwC,mBAAmB,KAAK;AACjE;;;;;;;ACpIa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB,GAAG,gBAAgB,GAAG,aAAa;AAC5D,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,sBAAsB,mBAAO,CAAC,KAAkB;AAChD,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,yBAAyB,mBAAO,CAAC,KAAgB;AACjD,gBAAgB,mBAAO,CAAC,KAAY;AACpC,mBAAO,CAAC,KAAmB;AAC3B,8BAA8B,mBAAO,CAAC,KAA6C;AACnF;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,UAAU,wCAAwC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD;;;;;;;AClLa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB,GAAG,oBAAoB,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,qBAAqB,GAAG,eAAe,GAAG,cAAc,GAAG,eAAe,GAAG,gBAAgB,GAAG,oBAAoB,GAAG,iBAAiB,GAAG,eAAe,GAAG,iBAAiB,GAAG,iBAAiB,GAAG,cAAc,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,kBAAkB,GAAG,iBAAiB,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,iBAAiB;AACrc,sBAAsB,mBAAO,CAAC,KAAkB;AAChD,oBAAoB,mBAAO,CAAC,KAA2B;AACvD,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA,2DAA2D,uCAAuC,oGAAoG,sCAAsC;AAC5O;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,sEAAsE,IAAI,iBAAiB,GAAG;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,GAAG;AAC/E;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE;AAClE;AACA,yDAAyD;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,sCAAsC;AAC1F;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D,uEAAuE,UAAU;AACjF,6DAA6D,qCAAqC;AAClG;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,gCAAgC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,GAAG;AAC/E;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;ACx7Ba;AACb;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,uCAAuC,mBAAO,CAAC,KAAkB;AACjE,wCAAwC,mBAAO,CAAC,IAAoB;AACpE,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,mBAAmB,mBAAO,CAAC,KAA0B;AACrD;AACA;AACA;AACA;AACA,MAAM;AACN,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,2DAA2D;AAC/E,wBAAwB,2DAA2D;AACnF,uBAAuB,mBAAmB;AAC1C;AACA;AACA;AACA,qBAAqB,2DAA2D;AAChF,qBAAqB,2DAA2D;AAChF,qBAAqB,2DAA2D;AAChF,0BAA0B,2DAA2D;AACrF,wBAAwB,2DAA2D;AACnF,wBAAwB,mBAAmB;AAC3C,2BAA2B,2DAA2D;AACtF,wBAAwB,2DAA2D;AACnF,sBAAsB,2DAA2D;AACjF,sBAAsB,2DAA2D;AACjF,wBAAwB,2DAA2D;AACnF,2BAA2B,2DAA2D;AACtF,4BAA4B,2DAA2D;AACvF,yBAAyB,2DAA2D;AACpF,yBAAyB,2DAA2D;AACpF,sBAAsB,2DAA2D;AACjF,uBAAuB,2DAA2D;AAClF,uBAAuB,2DAA2D;AAClF,0BAA0B,2DAA2D;AACrF,wBAAwB,2DAA2D;AACnF,2BAA2B,2DAA2D;AACtF,8BAA8B,2DAA2D;AACzF,4BAA4B,2DAA2D;AACvF,4BAA4B,2DAA2D;AACvF,CAAC;AACD;AACA;AACA,yBAAyB;AACzB,8BAA8B;AAC9B,yBAAyB;AACzB,8BAA8B;AAC9B,2BAA2B;AAC3B,8BAA8B;AAC9B,2BAA2B;AAC3B,8BAA8B;AAC9B,yBAAyB;AACzB,8BAA8B;AAC9B,yBAAyB;AACzB,8BAA8B;AAC9B,wBAAwB,oCAAoC;AAC5D,wBAAwB;AACxB,mDAAmD;AACnD,uBAAuB,oCAAoC;AAC3D,wBAAwB,oCAAoC;AAC5D,uBAAuB;AACvB,8BAA8B;AAC9B,sBAAsB;AACtB,8BAA8B;AAC9B,4BAA4B;AAC5B,8BAA8B;AAC9B,2BAA2B;AAC3B,8BAA8B;AAC9B,0BAA0B;AAC1B,8BAA8B;AAC9B,uBAAuB,oCAAoC;AAC3D,2BAA2B;AAC3B,8BAA8B;AAC9B,2BAA2B,gBAAgB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,gBAAgB;AACvC,yBAAyB,gBAAgB;AACzC,yBAAyB,gBAAgB;AACzC,yBAAyB,gBAAgB;AACzC,wBAAwB,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,eAAe;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,uCAAuC;AAC/D,wBAAwB,uCAAuC;AAC/D,wBAAwB,uCAAuC;AAC/D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,qCAAqC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,qCAAqC;AAChE,wBAAwB,qCAAqC;AAC7D,sBAAsB,qCAAqC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB,kBAAkB;AAClB,QAAQ,KAAK;AACb,QAAQ,KAAK;AACb;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,SAAS,GAAG,UAAU,IAAI;AAC7D,mCAAmC,SAAS,GAAG,UAAU,IAAI;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,KAAK;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,gBAAgB,GAAG,EAAE;AAClD;AACA;AACA,qCAAqC,EAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,GAAG;AACnC,uCAAuC,IAAI;AAC3C,2BAA2B,WAAW,IAAI,IAAI,cAAc;AAC5D,iCAAiC,YAAY,WAAW;AACxD,cAAc,YAAY,WAAW,WAAW,IAAI,IAAI;AACxD;AACA,iCAAiC,YAAY,UAAU;AACvD,cAAc,WAAW,UAAU;AACnC,uBAAuB,uBAAuB;AAC9C,uBAAuB,GAAG,cAAc,gBAAgB;AACxD,2BAA2B,GAAG,UAAU,GAAG;AAC3C,6CAA6C,EAAE;AAC/C;AACA,cAAc,cAAc,GAAG;AAC/B,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;AC3oBa;AACb;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,0CAA0C,4BAA4B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB,mBAAO,CAAC,KAAgB;AACjD,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,wBAAwB,mBAAO,CAAC,KAAoB;AACpD,qCAAqC,mBAAO,CAAC,KAAiB;AAC9D,mBAAmB,mBAAO,CAAC,KAAkC;AAC7D,gBAAgB,mBAAO,CAAC,KAAY;AACpC,mBAAmB,mBAAO,CAAC,KAA0B;AACrD,oBAAoB,mBAAO,CAAC,KAA2B;AACvD,mBAAmB,mBAAO,CAAC,IAA0B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,kBAAkB;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,kBAAkB;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,8CAA8C,mBAAmB;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,uBAAuB,qEAAqE;AAC/J;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2EAA2E,yCAAyC;AACpH;AACA;AACA;AACA,2EAA2E,sCAAsC;AACjH;AACA;AACA,qDAAqD,oCAAoC;AACzF,0DAA0D,gBAAgB;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,oCAAoC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E,wBAAwB;AACvG;AACA;AACA,kDAAkD,wBAAwB;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,iCAAiC,gCAAgC;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,gDAAgD,gCAAgC;AAClH;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,wBAAwB;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,8BAA8B;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,oBAAoB;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE,mCAAmC;AAC1G;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,yCAAyC;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,UAAU;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,0CAA0C;AAC/G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,UAAU;AAC9D,qDAAqD,WAAW;AAChE,KAAK;AACL;AACA;AACA,qDAAqD,8BAA8B;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,wDAAwD,YAAY;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,wDAAwD,kBAAkB;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,qCAAqC;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,iBAAiB;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,uBAAuB;AACrF,aAAa;AACb;AACA,0FAA0F,iBAAiB,MAAM,qCAAqC;AACtJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,iBAAiB;AAChE,+CAA+C,iBAAiB;AAChE;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,2BAA2B;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,uCAAuC;AACvF;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,wBAAwB;AAC1F;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,yCAAyC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,UAAU;AACnE;AACA;AACA,qDAAqD,4DAA4D;AACjH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,wBAAwB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,wBAAwB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,cAAc;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAe;AACf;;;;;;;AC/7Ba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,UAAU,GAAG,iBAAiB,GAAG,UAAU,GAAG,eAAe,GAAG,iBAAiB,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,aAAa,GAAG,gBAAgB;AAC3J,gBAAgB;AAChB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,4BAA4B;AAC5B,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,wBAAwB;AACxB,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/Tree/Factory.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/NodeFactory.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/ParseMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/ParseOptions.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/StackItem.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/StackItemFactory.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/TexConstants.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/base/BaseConfiguration.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/base/BaseItems.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/base/BaseMappings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/base/BaseMethods.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/lengths.js"], "sourcesContent": ["\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractFactory = void 0;\nvar AbstractFactory = (function () {\n    function AbstractFactory(nodes) {\n        var e_1, _a;\n        if (nodes === void 0) { nodes = null; }\n        this.defaultKind = 'unknown';\n        this.nodeMap = new Map();\n        this.node = {};\n        if (nodes === null) {\n            nodes = this.constructor.defaultNodes;\n        }\n        try {\n            for (var _b = __values(Object.keys(nodes)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var kind = _c.value;\n                this.setNodeClass(kind, nodes[kind]);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    }\n    AbstractFactory.prototype.create = function (kind) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        return (this.node[kind] || this.node[this.defaultKind]).apply(void 0, __spreadArray([], __read(args), false));\n    };\n    AbstractFactory.prototype.setNodeClass = function (kind, nodeClass) {\n        this.nodeMap.set(kind, nodeClass);\n        var THIS = this;\n        var KIND = this.nodeMap.get(kind);\n        this.node[kind] = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new (KIND.bind.apply(KIND, __spreadArray([void 0, THIS], __read(args), false)))();\n        };\n    };\n    AbstractFactory.prototype.getNodeClass = function (kind) {\n        return this.nodeMap.get(kind);\n    };\n    AbstractFactory.prototype.deleteNodeClass = function (kind) {\n        this.nodeMap.delete(kind);\n        delete this.node[kind];\n    };\n    AbstractFactory.prototype.nodeIsKind = function (node, kind) {\n        return (node instanceof this.getNodeClass(kind));\n    };\n    AbstractFactory.prototype.getKinds = function () {\n        return Array.from(this.nodeMap.keys());\n    };\n    AbstractFactory.defaultNodes = {};\n    return AbstractFactory;\n}());\nexports.AbstractFactory = AbstractFactory;\n//# sourceMappingURL=Factory.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.NodeFactory = void 0;\nvar NodeUtil_js_1 = __importDefault(require(\"./NodeUtil.js\"));\nvar NodeFactory = (function () {\n    function NodeFactory() {\n        this.mmlFactory = null;\n        this.factory = { 'node': NodeFactory.createNode,\n            'token': NodeFactory.createToken,\n            'text': NodeFactory.createText,\n            'error': NodeFactory.createError\n        };\n    }\n    NodeFactory.createNode = function (factory, kind, children, def, text) {\n        if (children === void 0) { children = []; }\n        if (def === void 0) { def = {}; }\n        var node = factory.mmlFactory.create(kind);\n        node.setChildren(children);\n        if (text) {\n            node.appendChild(text);\n        }\n        NodeUtil_js_1.default.setProperties(node, def);\n        return node;\n    };\n    NodeFactory.createToken = function (factory, kind, def, text) {\n        if (def === void 0) { def = {}; }\n        if (text === void 0) { text = ''; }\n        var textNode = factory.create('text', text);\n        return factory.create('node', kind, [], def, textNode);\n    };\n    NodeFactory.createText = function (factory, text) {\n        if (text == null) {\n            return null;\n        }\n        return factory.mmlFactory.create('text').setText(text);\n    };\n    NodeFactory.createError = function (factory, message) {\n        var text = factory.create('text', message);\n        var mtext = factory.create('node', 'mtext', [], {}, text);\n        var error = factory.create('node', 'merror', [mtext], { 'data-mjx-error': message });\n        return error;\n    };\n    NodeFactory.prototype.setMmlFactory = function (mmlFactory) {\n        this.mmlFactory = mmlFactory;\n    };\n    NodeFactory.prototype.set = function (kind, func) {\n        this.factory[kind] = func;\n    };\n    NodeFactory.prototype.setCreators = function (maps) {\n        for (var kind in maps) {\n            this.set(kind, maps[kind]);\n        }\n    };\n    NodeFactory.prototype.create = function (kind) {\n        var rest = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            rest[_i - 1] = arguments[_i];\n        }\n        var func = this.factory[kind] || this.factory['node'];\n        var node = func.apply(void 0, __spreadArray([this, rest[0]], __read(rest.slice(1)), false));\n        if (kind === 'node') {\n            this.configuration.addNode(rest[0], node);\n        }\n        return node;\n    };\n    NodeFactory.prototype.get = function (kind) {\n        return this.factory[kind];\n    };\n    return NodeFactory;\n}());\nexports.NodeFactory = NodeFactory;\n//# sourceMappingURL=NodeFactory.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar NodeUtil_js_1 = __importDefault(require(\"./NodeUtil.js\"));\nvar TexConstants_js_1 = require(\"./TexConstants.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"./ParseUtil.js\"));\nvar ParseMethods;\n(function (ParseMethods) {\n    function variable(parser, c) {\n        var def = ParseUtil_js_1.default.getFontDef(parser);\n        var env = parser.stack.env;\n        if (env.multiLetterIdentifiers && env.font !== '') {\n            c = parser.string.substr(parser.i - 1).match(env.multiLetterIdentifiers)[0];\n            parser.i += c.length - 1;\n            if (def.mathvariant === TexConstants_js_1.TexConstant.Variant.NORMAL && env.noAutoOP && c.length > 1) {\n                def.autoOP = false;\n            }\n        }\n        var node = parser.create('token', 'mi', def, c);\n        parser.Push(node);\n    }\n    ParseMethods.variable = variable;\n    function digit(parser, c) {\n        var mml;\n        var pattern = parser.configuration.options['digits'];\n        var n = parser.string.slice(parser.i - 1).match(pattern);\n        var def = ParseUtil_js_1.default.getFontDef(parser);\n        if (n) {\n            mml = parser.create('token', 'mn', def, n[0].replace(/[{}]/g, ''));\n            parser.i += n[0].length - 1;\n        }\n        else {\n            mml = parser.create('token', 'mo', def, c);\n        }\n        parser.Push(mml);\n    }\n    ParseMethods.digit = digit;\n    function controlSequence(parser, _c) {\n        var name = parser.GetCS();\n        parser.parse('macro', [parser, name]);\n    }\n    ParseMethods.controlSequence = controlSequence;\n    function mathchar0mi(parser, mchar) {\n        var def = mchar.attributes || { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC };\n        var node = parser.create('token', 'mi', def, mchar.char);\n        parser.Push(node);\n    }\n    ParseMethods.mathchar0mi = mathchar0mi;\n    function mathchar0mo(parser, mchar) {\n        var def = mchar.attributes || {};\n        def['stretchy'] = false;\n        var node = parser.create('token', 'mo', def, mchar.char);\n        NodeUtil_js_1.default.setProperty(node, 'fixStretchy', true);\n        parser.configuration.addNode('fixStretchy', node);\n        parser.Push(node);\n    }\n    ParseMethods.mathchar0mo = mathchar0mo;\n    function mathchar7(parser, mchar) {\n        var def = mchar.attributes || { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL };\n        if (parser.stack.env['font']) {\n            def['mathvariant'] = parser.stack.env['font'];\n        }\n        var node = parser.create('token', 'mi', def, mchar.char);\n        parser.Push(node);\n    }\n    ParseMethods.mathchar7 = mathchar7;\n    function delimiter(parser, delim) {\n        var def = delim.attributes || {};\n        def = Object.assign({ fence: false, stretchy: false }, def);\n        var node = parser.create('token', 'mo', def, delim.char);\n        parser.Push(node);\n    }\n    ParseMethods.delimiter = delimiter;\n    function environment(parser, env, func, args) {\n        var end = args[0];\n        var mml = parser.itemFactory.create('begin').setProperties({ name: env, end: end });\n        mml = func.apply(void 0, __spreadArray([parser, mml], __read(args.slice(1)), false));\n        parser.Push(mml);\n    }\n    ParseMethods.environment = environment;\n})(ParseMethods || (ParseMethods = {}));\nexports.default = ParseMethods;\n//# sourceMappingURL=ParseMethods.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar StackItemFactory_js_1 = __importDefault(require(\"./StackItemFactory.js\"));\nvar NodeFactory_js_1 = require(\"./NodeFactory.js\");\nvar NodeUtil_js_1 = __importDefault(require(\"./NodeUtil.js\"));\nvar Options_js_1 = require(\"../../util/Options.js\");\nvar ParseOptions = (function () {\n    function ParseOptions(configuration, options) {\n        if (options === void 0) { options = []; }\n        this.options = {};\n        this.packageData = new Map();\n        this.parsers = [];\n        this.root = null;\n        this.nodeLists = {};\n        this.error = false;\n        this.handlers = configuration.handlers;\n        this.nodeFactory = new NodeFactory_js_1.NodeFactory();\n        this.nodeFactory.configuration = this;\n        this.nodeFactory.setCreators(configuration.nodes);\n        this.itemFactory = new StackItemFactory_js_1.default(configuration.items);\n        this.itemFactory.configuration = this;\n        Options_js_1.defaultOptions.apply(void 0, __spreadArray([this.options], __read(options), false));\n        (0, Options_js_1.defaultOptions)(this.options, configuration.options);\n    }\n    ParseOptions.prototype.pushParser = function (parser) {\n        this.parsers.unshift(parser);\n    };\n    ParseOptions.prototype.popParser = function () {\n        this.parsers.shift();\n    };\n    Object.defineProperty(ParseOptions.prototype, \"parser\", {\n        get: function () {\n            return this.parsers[0];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ParseOptions.prototype.clear = function () {\n        this.parsers = [];\n        this.root = null;\n        this.nodeLists = {};\n        this.error = false;\n        this.tags.resetTag();\n    };\n    ParseOptions.prototype.addNode = function (property, node) {\n        var list = this.nodeLists[property];\n        if (!list) {\n            list = this.nodeLists[property] = [];\n        }\n        list.push(node);\n        if (node.kind !== property) {\n            var inlists = (NodeUtil_js_1.default.getProperty(node, 'in-lists') || '');\n            var lists = (inlists ? inlists.split(/,/) : []).concat(property).join(',');\n            NodeUtil_js_1.default.setProperty(node, 'in-lists', lists);\n        }\n    };\n    ParseOptions.prototype.getList = function (property) {\n        var e_1, _a;\n        var list = this.nodeLists[property] || [];\n        var result = [];\n        try {\n            for (var list_1 = __values(list), list_1_1 = list_1.next(); !list_1_1.done; list_1_1 = list_1.next()) {\n                var node = list_1_1.value;\n                if (this.inTree(node)) {\n                    result.push(node);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (list_1_1 && !list_1_1.done && (_a = list_1.return)) _a.call(list_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        this.nodeLists[property] = result;\n        return result;\n    };\n    ParseOptions.prototype.removeFromList = function (property, nodes) {\n        var e_2, _a;\n        var list = this.nodeLists[property] || [];\n        try {\n            for (var nodes_1 = __values(nodes), nodes_1_1 = nodes_1.next(); !nodes_1_1.done; nodes_1_1 = nodes_1.next()) {\n                var node = nodes_1_1.value;\n                var i = list.indexOf(node);\n                if (i >= 0) {\n                    list.splice(i, 1);\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (nodes_1_1 && !nodes_1_1.done && (_a = nodes_1.return)) _a.call(nodes_1);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    ParseOptions.prototype.inTree = function (node) {\n        while (node && node !== this.root) {\n            node = node.parent;\n        }\n        return !!node;\n    };\n    return ParseOptions;\n}());\nexports.default = ParseOptions;\n//# sourceMappingURL=ParseOptions.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BaseItem = exports.MmlStack = void 0;\nvar TexError_js_1 = __importDefault(require(\"./TexError.js\"));\nvar MmlStack = (function () {\n    function MmlStack(_nodes) {\n        this._nodes = _nodes;\n    }\n    Object.defineProperty(MmlStack.prototype, \"nodes\", {\n        get: function () {\n            return this._nodes;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlStack.prototype.Push = function () {\n        var _a;\n        var nodes = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            nodes[_i] = arguments[_i];\n        }\n        (_a = this._nodes).push.apply(_a, __spreadArray([], __read(nodes), false));\n    };\n    MmlStack.prototype.Pop = function () {\n        return this._nodes.pop();\n    };\n    Object.defineProperty(MmlStack.prototype, \"First\", {\n        get: function () {\n            return this._nodes[this.Size() - 1];\n        },\n        set: function (node) {\n            this._nodes[this.Size() - 1] = node;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlStack.prototype, \"Last\", {\n        get: function () {\n            return this._nodes[0];\n        },\n        set: function (node) {\n            this._nodes[0] = node;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlStack.prototype.Peek = function (n) {\n        if (n == null) {\n            n = 1;\n        }\n        return this._nodes.slice(this.Size() - n);\n    };\n    MmlStack.prototype.Size = function () {\n        return this._nodes.length;\n    };\n    MmlStack.prototype.Clear = function () {\n        this._nodes = [];\n    };\n    MmlStack.prototype.toMml = function (inferred, forceRow) {\n        if (inferred === void 0) { inferred = true; }\n        if (this._nodes.length === 1 && !forceRow) {\n            return this.First;\n        }\n        return this.create('node', inferred ? 'inferredMrow' : 'mrow', this._nodes, {});\n    };\n    MmlStack.prototype.create = function (kind) {\n        var _a;\n        var rest = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            rest[_i - 1] = arguments[_i];\n        }\n        return (_a = this.factory.configuration.nodeFactory).create.apply(_a, __spreadArray([kind], __read(rest), false));\n    };\n    return MmlStack;\n}());\nexports.MmlStack = MmlStack;\nvar BaseItem = (function (_super) {\n    __extends(BaseItem, _super);\n    function BaseItem(factory) {\n        var nodes = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            nodes[_i - 1] = arguments[_i];\n        }\n        var _this = _super.call(this, nodes) || this;\n        _this.factory = factory;\n        _this.global = {};\n        _this._properties = {};\n        if (_this.isOpen) {\n            _this._env = {};\n        }\n        return _this;\n    }\n    Object.defineProperty(BaseItem.prototype, \"kind\", {\n        get: function () {\n            return 'base';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseItem.prototype, \"env\", {\n        get: function () {\n            return this._env;\n        },\n        set: function (value) {\n            this._env = value;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseItem.prototype, \"copyEnv\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    BaseItem.prototype.getProperty = function (key) {\n        return this._properties[key];\n    };\n    BaseItem.prototype.setProperty = function (key, value) {\n        this._properties[key] = value;\n        return this;\n    };\n    Object.defineProperty(BaseItem.prototype, \"isOpen\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseItem.prototype, \"isClose\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseItem.prototype, \"isFinal\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    BaseItem.prototype.isKind = function (kind) {\n        return kind === this.kind;\n    };\n    BaseItem.prototype.checkItem = function (item) {\n        if (item.isKind('over') && this.isOpen) {\n            item.setProperty('num', this.toMml(false));\n            this.Clear();\n        }\n        if (item.isKind('cell') && this.isOpen) {\n            if (item.getProperty('linebreak')) {\n                return BaseItem.fail;\n            }\n            throw new TexError_js_1.default('Misplaced', 'Misplaced %1', item.getName());\n        }\n        if (item.isClose && this.getErrors(item.kind)) {\n            var _a = __read(this.getErrors(item.kind), 2), id = _a[0], message = _a[1];\n            throw new TexError_js_1.default(id, message, item.getName());\n        }\n        if (!item.isFinal) {\n            return BaseItem.success;\n        }\n        this.Push(item.First);\n        return BaseItem.fail;\n    };\n    BaseItem.prototype.clearEnv = function () {\n        var e_1, _a;\n        try {\n            for (var _b = __values(Object.keys(this.env)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var id = _c.value;\n                delete this.env[id];\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    BaseItem.prototype.setProperties = function (def) {\n        Object.assign(this._properties, def);\n        return this;\n    };\n    BaseItem.prototype.getName = function () {\n        return this.getProperty('name');\n    };\n    BaseItem.prototype.toString = function () {\n        return this.kind + '[' + this.nodes.join('; ') + ']';\n    };\n    BaseItem.prototype.getErrors = function (kind) {\n        var CLASS = this.constructor;\n        return (CLASS.errors || {})[kind] || BaseItem.errors[kind];\n    };\n    BaseItem.fail = [null, false];\n    BaseItem.success = [null, true];\n    BaseItem.errors = {\n        end: ['MissingBeginExtraEnd', 'Missing \\\\begin{%1} or extra \\\\end{%1}'],\n        close: ['ExtraCloseMissingOpen', 'Extra close brace or missing open brace'],\n        right: ['MissingLeftExtraRight', 'Missing \\\\left or extra \\\\right'],\n        middle: ['ExtraMiddle', 'Extra \\\\middle']\n    };\n    return BaseItem;\n}(MmlStack));\nexports.BaseItem = BaseItem;\n//# sourceMappingURL=StackItem.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar StackItem_js_1 = require(\"./StackItem.js\");\nvar Factory_js_1 = require(\"../../core/Tree/Factory.js\");\nvar DummyItem = (function (_super) {\n    __extends(DummyItem, _super);\n    function DummyItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return DummyItem;\n}(StackItem_js_1.BaseItem));\nvar StackItemFactory = (function (_super) {\n    __extends(StackItemFactory, _super);\n    function StackItemFactory() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.defaultKind = 'dummy';\n        _this.configuration = null;\n        return _this;\n    }\n    StackItemFactory.DefaultStackItems = (_a = {},\n        _a[DummyItem.prototype.kind] = DummyItem,\n        _a);\n    return StackItemFactory;\n}(Factory_js_1.AbstractFactory));\nexports.default = StackItemFactory;\n//# sourceMappingURL=StackItemFactory.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TexConstant = void 0;\nvar TexConstant;\n(function (TexConstant) {\n    TexConstant.Variant = {\n        NORMAL: 'normal',\n        BOLD: 'bold',\n        ITALIC: 'italic',\n        BOLDITALIC: 'bold-italic',\n        DOUBLESTRUCK: 'double-struck',\n        FRAKTUR: 'fraktur',\n        BOLDFRAKTUR: 'bold-fraktur',\n        SCRIPT: 'script',\n        BOLDSCRIPT: 'bold-script',\n        SANSSERIF: 'sans-serif',\n        BOLDSANSSERIF: 'bold-sans-serif',\n        SANSSERIFITALIC: 'sans-serif-italic',\n        SANSSERIFBOLDITALIC: 'sans-serif-bold-italic',\n        MONOSPACE: 'monospace',\n        INITIAL: 'inital',\n        TAILED: 'tailed',\n        LOOPED: 'looped',\n        STRETCHED: 'stretched',\n        CALLIGRAPHIC: '-tex-calligraphic',\n        BOLDCALLIGRAPHIC: '-tex-bold-calligraphic',\n        OLDSTYLE: '-tex-oldstyle',\n        BOLDOLDSTYLE: '-tex-bold-oldstyle',\n        MATHITALIC: '-tex-mathit'\n    };\n    TexConstant.Form = {\n        PREFIX: 'prefix',\n        INFIX: 'infix',\n        POSTFIX: 'postfix'\n    };\n    TexConstant.LineBreak = {\n        AUTO: 'auto',\n        NEWLINE: 'newline',\n        NOBREAK: 'nobreak',\n        GOODBREAK: 'goodbreak',\n        BADBREAK: 'badbreak'\n    };\n    TexConstant.LineBreakStyle = {\n        BEFORE: 'before',\n        AFTER: 'after',\n        DUPLICATE: 'duplicate',\n        INFIXLINBREAKSTYLE: 'infixlinebreakstyle'\n    };\n    TexConstant.IndentAlign = {\n        LEFT: 'left',\n        CENTER: 'center',\n        RIGHT: 'right',\n        AUTO: 'auto',\n        ID: 'id',\n        INDENTALIGN: 'indentalign'\n    };\n    TexConstant.IndentShift = {\n        INDENTSHIFT: 'indentshift'\n    };\n    TexConstant.LineThickness = {\n        THIN: 'thin',\n        MEDIUM: 'medium',\n        THICK: 'thick'\n    };\n    TexConstant.Notation = {\n        LONGDIV: 'longdiv',\n        ACTUARIAL: 'actuarial',\n        PHASORANGLE: 'phasorangle',\n        RADICAL: 'radical',\n        BOX: 'box',\n        ROUNDEDBOX: 'roundedbox',\n        CIRCLE: 'circle',\n        LEFT: 'left',\n        RIGHT: 'right',\n        TOP: 'top',\n        BOTTOM: 'bottom',\n        UPDIAGONALSTRIKE: 'updiagonalstrike',\n        DOWNDIAGONALSTRIKE: 'downdiagonalstrike',\n        VERTICALSTRIKE: 'verticalstrike',\n        HORIZONTALSTRIKE: 'horizontalstrike',\n        NORTHEASTARROW: 'northeastarrow',\n        MADRUWB: 'madruwb',\n        UPDIAGONALARROW: 'updiagonalarrow'\n    };\n    TexConstant.Align = {\n        TOP: 'top',\n        BOTTOM: 'bottom',\n        CENTER: 'center',\n        BASELINE: 'baseline',\n        AXIS: 'axis',\n        LEFT: 'left',\n        RIGHT: 'right'\n    };\n    TexConstant.Lines = {\n        NONE: 'none',\n        SOLID: 'solid',\n        DASHED: 'dashed'\n    };\n    TexConstant.Side = {\n        LEFT: 'left',\n        RIGHT: 'right',\n        LEFTOVERLAP: 'leftoverlap',\n        RIGHTOVERLAP: 'rightoverlap'\n    };\n    TexConstant.Width = {\n        AUTO: 'auto',\n        FIT: 'fit'\n    };\n    TexConstant.Actiontype = {\n        TOGGLE: 'toggle',\n        STATUSLINE: 'statusline',\n        TOOLTIP: 'tooltip',\n        INPUT: 'input'\n    };\n    TexConstant.Overflow = {\n        LINBREAK: 'linebreak',\n        SCROLL: 'scroll',\n        ELIDE: 'elide',\n        TRUNCATE: 'truncate',\n        SCALE: 'scale'\n    };\n    TexConstant.Unit = {\n        EM: 'em',\n        EX: 'ex',\n        PX: 'px',\n        IN: 'in',\n        CM: 'cm',\n        MM: 'mm',\n        PT: 'pt',\n        PC: 'pc'\n    };\n})(TexConstant = exports.TexConstant || (exports.TexConstant = {}));\n//# sourceMappingURL=TexConstants.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BaseConfiguration = exports.BaseTags = exports.Other = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar MapHandler_js_1 = require(\"../MapHandler.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar bitem = __importStar(require(\"./BaseItems.js\"));\nvar Tags_js_1 = require(\"../Tags.js\");\nrequire(\"./BaseMappings.js\");\nvar OperatorDictionary_js_1 = require(\"../../../core/MmlTree/OperatorDictionary.js\");\nnew SymbolMap_js_1.CharacterMap('remap', null, {\n    '-': '\\u2212',\n    '*': '\\u2217',\n    '`': '\\u2018'\n});\nfunction Other(parser, char) {\n    var font = parser.stack.env['font'];\n    var def = font ?\n        { mathvariant: parser.stack.env['font'] } : {};\n    var remap = MapHandler_js_1.MapHandler.getMap('remap').lookup(char);\n    var range = (0, OperatorDictionary_js_1.getRange)(char);\n    var type = (range ? range[3] : 'mo');\n    var mo = parser.create('token', type, def, (remap ? remap.char : char));\n    range[4] && mo.attributes.set('mathvariant', range[4]);\n    if (type === 'mo') {\n        NodeUtil_js_1.default.setProperty(mo, 'fixStretchy', true);\n        parser.configuration.addNode('fixStretchy', mo);\n    }\n    parser.Push(mo);\n}\nexports.Other = Other;\nfunction csUndefined(_parser, name) {\n    throw new TexError_js_1.default('UndefinedControlSequence', 'Undefined control sequence %1', '\\\\' + name);\n}\nfunction envUndefined(_parser, env) {\n    throw new TexError_js_1.default('UnknownEnv', 'Unknown environment \\'%1\\'', env);\n}\nfunction filterNonscript(_a) {\n    var e_1, _b;\n    var data = _a.data;\n    try {\n        for (var _c = __values(data.getList('nonscript')), _d = _c.next(); !_d.done; _d = _c.next()) {\n            var mml = _d.value;\n            if (mml.attributes.get('scriptlevel') > 0) {\n                var parent_1 = mml.parent;\n                parent_1.childNodes.splice(parent_1.childIndex(mml), 1);\n                data.removeFromList(mml.kind, [mml]);\n                if (mml.isKind('mrow')) {\n                    var mstyle = mml.childNodes[0];\n                    data.removeFromList('mstyle', [mstyle]);\n                    data.removeFromList('mspace', mstyle.childNodes[0].childNodes);\n                }\n            }\n            else if (mml.isKind('mrow')) {\n                mml.parent.replaceChild(mml.childNodes[0], mml);\n                data.removeFromList('mrow', [mml]);\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_d && !_d.done && (_b = _c.return)) _b.call(_c);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n}\nvar BaseTags = (function (_super) {\n    __extends(BaseTags, _super);\n    function BaseTags() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return BaseTags;\n}(Tags_js_1.AbstractTags));\nexports.BaseTags = BaseTags;\nexports.BaseConfiguration = Configuration_js_1.Configuration.create('base', {\n    handler: {\n        character: ['command', 'special', 'letter', 'digit'],\n        delimiter: ['delimiter'],\n        macro: ['delimiter', 'macros', 'mathchar0mi', 'mathchar0mo', 'mathchar7'],\n        environment: ['environment']\n    },\n    fallback: {\n        character: Other,\n        macro: csUndefined,\n        environment: envUndefined\n    },\n    items: (_a = {},\n        _a[bitem.StartItem.prototype.kind] = bitem.StartItem,\n        _a[bitem.StopItem.prototype.kind] = bitem.StopItem,\n        _a[bitem.OpenItem.prototype.kind] = bitem.OpenItem,\n        _a[bitem.CloseItem.prototype.kind] = bitem.CloseItem,\n        _a[bitem.PrimeItem.prototype.kind] = bitem.PrimeItem,\n        _a[bitem.SubsupItem.prototype.kind] = bitem.SubsupItem,\n        _a[bitem.OverItem.prototype.kind] = bitem.OverItem,\n        _a[bitem.LeftItem.prototype.kind] = bitem.LeftItem,\n        _a[bitem.Middle.prototype.kind] = bitem.Middle,\n        _a[bitem.RightItem.prototype.kind] = bitem.RightItem,\n        _a[bitem.BeginItem.prototype.kind] = bitem.BeginItem,\n        _a[bitem.EndItem.prototype.kind] = bitem.EndItem,\n        _a[bitem.StyleItem.prototype.kind] = bitem.StyleItem,\n        _a[bitem.PositionItem.prototype.kind] = bitem.PositionItem,\n        _a[bitem.CellItem.prototype.kind] = bitem.CellItem,\n        _a[bitem.MmlItem.prototype.kind] = bitem.MmlItem,\n        _a[bitem.FnItem.prototype.kind] = bitem.FnItem,\n        _a[bitem.NotItem.prototype.kind] = bitem.NotItem,\n        _a[bitem.NonscriptItem.prototype.kind] = bitem.NonscriptItem,\n        _a[bitem.DotsItem.prototype.kind] = bitem.DotsItem,\n        _a[bitem.ArrayItem.prototype.kind] = bitem.ArrayItem,\n        _a[bitem.EqnArrayItem.prototype.kind] = bitem.EqnArrayItem,\n        _a[bitem.EquationItem.prototype.kind] = bitem.EquationItem,\n        _a),\n    options: {\n        maxMacros: 1000,\n        baseURL: (typeof (document) === 'undefined' ||\n            document.getElementsByTagName('base').length === 0) ?\n            '' : String(document.location).replace(/#.*$/, '')\n    },\n    tags: {\n        base: BaseTags\n    },\n    postprocessors: [[filterNonscript, -4]]\n});\n//# sourceMappingURL=BaseConfiguration.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EquationItem = exports.EqnArrayItem = exports.ArrayItem = exports.DotsItem = exports.NonscriptItem = exports.NotItem = exports.FnItem = exports.MmlItem = exports.CellItem = exports.PositionItem = exports.StyleItem = exports.EndItem = exports.BeginItem = exports.RightItem = exports.Middle = exports.LeftItem = exports.OverItem = exports.SubsupItem = exports.PrimeItem = exports.CloseItem = exports.OpenItem = exports.StopItem = exports.StartItem = void 0;\nvar MapHandler_js_1 = require(\"../MapHandler.js\");\nvar Entities_js_1 = require(\"../../../util/Entities.js\");\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar StackItem_js_1 = require(\"../StackItem.js\");\nvar StartItem = (function (_super) {\n    __extends(StartItem, _super);\n    function StartItem(factory, global) {\n        var _this = _super.call(this, factory) || this;\n        _this.global = global;\n        return _this;\n    }\n    Object.defineProperty(StartItem.prototype, \"kind\", {\n        get: function () {\n            return 'start';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(StartItem.prototype, \"isOpen\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    StartItem.prototype.checkItem = function (item) {\n        if (item.isKind('stop')) {\n            var node = this.toMml();\n            if (!this.global.isInner) {\n                node = this.factory.configuration.tags.finalize(node, this.env);\n            }\n            return [[this.factory.create('mml', node)], true];\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    return StartItem;\n}(StackItem_js_1.BaseItem));\nexports.StartItem = StartItem;\nvar StopItem = (function (_super) {\n    __extends(StopItem, _super);\n    function StopItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(StopItem.prototype, \"kind\", {\n        get: function () {\n            return 'stop';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(StopItem.prototype, \"isClose\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return StopItem;\n}(StackItem_js_1.BaseItem));\nexports.StopItem = StopItem;\nvar OpenItem = (function (_super) {\n    __extends(OpenItem, _super);\n    function OpenItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(OpenItem.prototype, \"kind\", {\n        get: function () {\n            return 'open';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(OpenItem.prototype, \"isOpen\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OpenItem.prototype.checkItem = function (item) {\n        if (item.isKind('close')) {\n            var mml = this.toMml();\n            var node = this.create('node', 'TeXAtom', [mml]);\n            return [[this.factory.create('mml', node)], true];\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    OpenItem.errors = Object.assign(Object.create(StackItem_js_1.BaseItem.errors), {\n        'stop': ['ExtraOpenMissingClose',\n            'Extra open brace or missing close brace']\n    });\n    return OpenItem;\n}(StackItem_js_1.BaseItem));\nexports.OpenItem = OpenItem;\nvar CloseItem = (function (_super) {\n    __extends(CloseItem, _super);\n    function CloseItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(CloseItem.prototype, \"kind\", {\n        get: function () {\n            return 'close';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CloseItem.prototype, \"isClose\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return CloseItem;\n}(StackItem_js_1.BaseItem));\nexports.CloseItem = CloseItem;\nvar PrimeItem = (function (_super) {\n    __extends(PrimeItem, _super);\n    function PrimeItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(PrimeItem.prototype, \"kind\", {\n        get: function () {\n            return 'prime';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    PrimeItem.prototype.checkItem = function (item) {\n        var _a = __read(this.Peek(2), 2), top0 = _a[0], top1 = _a[1];\n        if (!NodeUtil_js_1.default.isType(top0, 'msubsup') || NodeUtil_js_1.default.isType(top0, 'msup')) {\n            var node = this.create('node', 'msup', [top0, top1]);\n            return [[node, item], true];\n        }\n        NodeUtil_js_1.default.setChild(top0, top0.sup, top1);\n        return [[top0, item], true];\n    };\n    return PrimeItem;\n}(StackItem_js_1.BaseItem));\nexports.PrimeItem = PrimeItem;\nvar SubsupItem = (function (_super) {\n    __extends(SubsupItem, _super);\n    function SubsupItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(SubsupItem.prototype, \"kind\", {\n        get: function () {\n            return 'subsup';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    SubsupItem.prototype.checkItem = function (item) {\n        if (item.isKind('open') || item.isKind('left')) {\n            return StackItem_js_1.BaseItem.success;\n        }\n        var top = this.First;\n        var position = this.getProperty('position');\n        if (item.isKind('mml')) {\n            if (this.getProperty('primes')) {\n                if (position !== 2) {\n                    NodeUtil_js_1.default.setChild(top, 2, this.getProperty('primes'));\n                }\n                else {\n                    NodeUtil_js_1.default.setProperty(this.getProperty('primes'), 'variantForm', true);\n                    var node = this.create('node', 'mrow', [this.getProperty('primes'), item.First]);\n                    item.First = node;\n                }\n            }\n            NodeUtil_js_1.default.setChild(top, position, item.First);\n            if (this.getProperty('movesupsub') != null) {\n                NodeUtil_js_1.default.setProperty(top, 'movesupsub', this.getProperty('movesupsub'));\n            }\n            var result = this.factory.create('mml', top);\n            return [[result], true];\n        }\n        if (_super.prototype.checkItem.call(this, item)[1]) {\n            var error = this.getErrors(['', 'sub', 'sup'][position]);\n            throw new (TexError_js_1.default.bind.apply(TexError_js_1.default, __spreadArray([void 0, error[0], error[1]], __read(error.splice(2)), false)))();\n        }\n        return null;\n    };\n    SubsupItem.errors = Object.assign(Object.create(StackItem_js_1.BaseItem.errors), {\n        'stop': ['MissingScript',\n            'Missing superscript or subscript argument'],\n        'sup': ['MissingOpenForSup',\n            'Missing open brace for superscript'],\n        'sub': ['MissingOpenForSub',\n            'Missing open brace for subscript']\n    });\n    return SubsupItem;\n}(StackItem_js_1.BaseItem));\nexports.SubsupItem = SubsupItem;\nvar OverItem = (function (_super) {\n    __extends(OverItem, _super);\n    function OverItem(factory) {\n        var _this = _super.call(this, factory) || this;\n        _this.setProperty('name', '\\\\over');\n        return _this;\n    }\n    Object.defineProperty(OverItem.prototype, \"kind\", {\n        get: function () {\n            return 'over';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(OverItem.prototype, \"isClose\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OverItem.prototype.checkItem = function (item) {\n        if (item.isKind('over')) {\n            throw new TexError_js_1.default('AmbiguousUseOf', 'Ambiguous use of %1', item.getName());\n        }\n        if (item.isClose) {\n            var mml = this.create('node', 'mfrac', [this.getProperty('num'), this.toMml(false)]);\n            if (this.getProperty('thickness') != null) {\n                NodeUtil_js_1.default.setAttribute(mml, 'linethickness', this.getProperty('thickness'));\n            }\n            if (this.getProperty('open') || this.getProperty('close')) {\n                NodeUtil_js_1.default.setProperty(mml, 'withDelims', true);\n                mml = ParseUtil_js_1.default.fixedFence(this.factory.configuration, this.getProperty('open'), mml, this.getProperty('close'));\n            }\n            return [[this.factory.create('mml', mml), item], true];\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    OverItem.prototype.toString = function () {\n        return 'over[' + this.getProperty('num') +\n            ' / ' + this.nodes.join('; ') + ']';\n    };\n    return OverItem;\n}(StackItem_js_1.BaseItem));\nexports.OverItem = OverItem;\nvar LeftItem = (function (_super) {\n    __extends(LeftItem, _super);\n    function LeftItem(factory, delim) {\n        var _this = _super.call(this, factory) || this;\n        _this.setProperty('delim', delim);\n        return _this;\n    }\n    Object.defineProperty(LeftItem.prototype, \"kind\", {\n        get: function () {\n            return 'left';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(LeftItem.prototype, \"isOpen\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    LeftItem.prototype.checkItem = function (item) {\n        if (item.isKind('right')) {\n            return [[this.factory.create('mml', ParseUtil_js_1.default.fenced(this.factory.configuration, this.getProperty('delim'), this.toMml(), item.getProperty('delim'), '', item.getProperty('color')))], true];\n        }\n        if (item.isKind('middle')) {\n            var def = { stretchy: true };\n            if (item.getProperty('color')) {\n                def.mathcolor = item.getProperty('color');\n            }\n            this.Push(this.create('node', 'TeXAtom', [], { texClass: MmlNode_js_1.TEXCLASS.CLOSE }), this.create('token', 'mo', def, item.getProperty('delim')), this.create('node', 'TeXAtom', [], { texClass: MmlNode_js_1.TEXCLASS.OPEN }));\n            this.env = {};\n            return [[this], true];\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    LeftItem.errors = Object.assign(Object.create(StackItem_js_1.BaseItem.errors), {\n        'stop': ['ExtraLeftMissingRight',\n            'Extra \\\\left or missing \\\\right']\n    });\n    return LeftItem;\n}(StackItem_js_1.BaseItem));\nexports.LeftItem = LeftItem;\nvar Middle = (function (_super) {\n    __extends(Middle, _super);\n    function Middle(factory, delim, color) {\n        var _this = _super.call(this, factory) || this;\n        _this.setProperty('delim', delim);\n        color && _this.setProperty('color', color);\n        return _this;\n    }\n    Object.defineProperty(Middle.prototype, \"kind\", {\n        get: function () {\n            return 'middle';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Middle.prototype, \"isClose\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Middle;\n}(StackItem_js_1.BaseItem));\nexports.Middle = Middle;\nvar RightItem = (function (_super) {\n    __extends(RightItem, _super);\n    function RightItem(factory, delim, color) {\n        var _this = _super.call(this, factory) || this;\n        _this.setProperty('delim', delim);\n        color && _this.setProperty('color', color);\n        return _this;\n    }\n    Object.defineProperty(RightItem.prototype, \"kind\", {\n        get: function () {\n            return 'right';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(RightItem.prototype, \"isClose\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return RightItem;\n}(StackItem_js_1.BaseItem));\nexports.RightItem = RightItem;\nvar BeginItem = (function (_super) {\n    __extends(BeginItem, _super);\n    function BeginItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(BeginItem.prototype, \"kind\", {\n        get: function () {\n            return 'begin';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BeginItem.prototype, \"isOpen\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    BeginItem.prototype.checkItem = function (item) {\n        if (item.isKind('end')) {\n            if (item.getName() !== this.getName()) {\n                throw new TexError_js_1.default('EnvBadEnd', '\\\\begin{%1} ended with \\\\end{%2}', this.getName(), item.getName());\n            }\n            if (!this.getProperty('end')) {\n                return [[this.factory.create('mml', this.toMml())], true];\n            }\n            return StackItem_js_1.BaseItem.fail;\n        }\n        if (item.isKind('stop')) {\n            throw new TexError_js_1.default('EnvMissingEnd', 'Missing \\\\end{%1}', this.getName());\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    return BeginItem;\n}(StackItem_js_1.BaseItem));\nexports.BeginItem = BeginItem;\nvar EndItem = (function (_super) {\n    __extends(EndItem, _super);\n    function EndItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(EndItem.prototype, \"kind\", {\n        get: function () {\n            return 'end';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(EndItem.prototype, \"isClose\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return EndItem;\n}(StackItem_js_1.BaseItem));\nexports.EndItem = EndItem;\nvar StyleItem = (function (_super) {\n    __extends(StyleItem, _super);\n    function StyleItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(StyleItem.prototype, \"kind\", {\n        get: function () {\n            return 'style';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    StyleItem.prototype.checkItem = function (item) {\n        if (!item.isClose) {\n            return _super.prototype.checkItem.call(this, item);\n        }\n        var mml = this.create('node', 'mstyle', this.nodes, this.getProperty('styles'));\n        return [[this.factory.create('mml', mml), item], true];\n    };\n    return StyleItem;\n}(StackItem_js_1.BaseItem));\nexports.StyleItem = StyleItem;\nvar PositionItem = (function (_super) {\n    __extends(PositionItem, _super);\n    function PositionItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(PositionItem.prototype, \"kind\", {\n        get: function () {\n            return 'position';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    PositionItem.prototype.checkItem = function (item) {\n        if (item.isClose) {\n            throw new TexError_js_1.default('MissingBoxFor', 'Missing box for %1', this.getName());\n        }\n        if (item.isFinal) {\n            var mml = item.toMml();\n            switch (this.getProperty('move')) {\n                case 'vertical':\n                    mml = this.create('node', 'mpadded', [mml], { height: this.getProperty('dh'),\n                        depth: this.getProperty('dd'),\n                        voffset: this.getProperty('dh') });\n                    return [[this.factory.create('mml', mml)], true];\n                case 'horizontal':\n                    return [[this.factory.create('mml', this.getProperty('left')), item,\n                            this.factory.create('mml', this.getProperty('right'))], true];\n            }\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    return PositionItem;\n}(StackItem_js_1.BaseItem));\nexports.PositionItem = PositionItem;\nvar CellItem = (function (_super) {\n    __extends(CellItem, _super);\n    function CellItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(CellItem.prototype, \"kind\", {\n        get: function () {\n            return 'cell';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CellItem.prototype, \"isClose\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return CellItem;\n}(StackItem_js_1.BaseItem));\nexports.CellItem = CellItem;\nvar MmlItem = (function (_super) {\n    __extends(MmlItem, _super);\n    function MmlItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlItem.prototype, \"isFinal\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlItem.prototype, \"kind\", {\n        get: function () {\n            return 'mml';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return MmlItem;\n}(StackItem_js_1.BaseItem));\nexports.MmlItem = MmlItem;\nvar FnItem = (function (_super) {\n    __extends(FnItem, _super);\n    function FnItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(FnItem.prototype, \"kind\", {\n        get: function () {\n            return 'fn';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    FnItem.prototype.checkItem = function (item) {\n        var top = this.First;\n        if (top) {\n            if (item.isOpen) {\n                return StackItem_js_1.BaseItem.success;\n            }\n            if (!item.isKind('fn')) {\n                var mml = item.First;\n                if (!item.isKind('mml') || !mml) {\n                    return [[top, item], true];\n                }\n                if ((NodeUtil_js_1.default.isType(mml, 'mstyle') && mml.childNodes.length &&\n                    NodeUtil_js_1.default.isType(mml.childNodes[0].childNodes[0], 'mspace')) ||\n                    NodeUtil_js_1.default.isType(mml, 'mspace')) {\n                    return [[top, item], true];\n                }\n                if (NodeUtil_js_1.default.isEmbellished(mml)) {\n                    mml = NodeUtil_js_1.default.getCoreMO(mml);\n                }\n                var form = NodeUtil_js_1.default.getForm(mml);\n                if (form != null && [0, 0, 1, 1, 0, 1, 1, 0, 0, 0][form[2]]) {\n                    return [[top, item], true];\n                }\n            }\n            var node = this.create('token', 'mo', { texClass: MmlNode_js_1.TEXCLASS.NONE }, Entities_js_1.entities.ApplyFunction);\n            return [[top, node, item], true];\n        }\n        return _super.prototype.checkItem.apply(this, arguments);\n    };\n    return FnItem;\n}(StackItem_js_1.BaseItem));\nexports.FnItem = FnItem;\nvar NotItem = (function (_super) {\n    __extends(NotItem, _super);\n    function NotItem() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.remap = MapHandler_js_1.MapHandler.getMap('not_remap');\n        return _this;\n    }\n    Object.defineProperty(NotItem.prototype, \"kind\", {\n        get: function () {\n            return 'not';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    NotItem.prototype.checkItem = function (item) {\n        var mml;\n        var c;\n        var textNode;\n        if (item.isKind('open') || item.isKind('left')) {\n            return StackItem_js_1.BaseItem.success;\n        }\n        if (item.isKind('mml') &&\n            (NodeUtil_js_1.default.isType(item.First, 'mo') || NodeUtil_js_1.default.isType(item.First, 'mi') ||\n                NodeUtil_js_1.default.isType(item.First, 'mtext'))) {\n            mml = item.First;\n            c = NodeUtil_js_1.default.getText(mml);\n            if (c.length === 1 && !NodeUtil_js_1.default.getProperty(mml, 'movesupsub') &&\n                NodeUtil_js_1.default.getChildren(mml).length === 1) {\n                if (this.remap.contains(c)) {\n                    textNode = this.create('text', this.remap.lookup(c).char);\n                    NodeUtil_js_1.default.setChild(mml, 0, textNode);\n                }\n                else {\n                    textNode = this.create('text', '\\u0338');\n                    NodeUtil_js_1.default.appendChildren(mml, [textNode]);\n                }\n                return [[item], true];\n            }\n        }\n        textNode = this.create('text', '\\u29F8');\n        var mtextNode = this.create('node', 'mtext', [], {}, textNode);\n        var paddedNode = this.create('node', 'mpadded', [mtextNode], { width: 0 });\n        mml = this.create('node', 'TeXAtom', [paddedNode], { texClass: MmlNode_js_1.TEXCLASS.REL });\n        return [[mml, item], true];\n    };\n    return NotItem;\n}(StackItem_js_1.BaseItem));\nexports.NotItem = NotItem;\nvar NonscriptItem = (function (_super) {\n    __extends(NonscriptItem, _super);\n    function NonscriptItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(NonscriptItem.prototype, \"kind\", {\n        get: function () {\n            return 'nonscript';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    NonscriptItem.prototype.checkItem = function (item) {\n        if (item.isKind('mml') && item.Size() === 1) {\n            var mml = item.First;\n            if (mml.isKind('mstyle') && mml.notParent) {\n                mml = NodeUtil_js_1.default.getChildren(NodeUtil_js_1.default.getChildren(mml)[0])[0];\n            }\n            if (mml.isKind('mspace')) {\n                if (mml !== item.First) {\n                    var mrow = this.create('node', 'mrow', [item.Pop()]);\n                    item.Push(mrow);\n                }\n                this.factory.configuration.addNode('nonscript', item.First);\n            }\n        }\n        return [[item], true];\n    };\n    return NonscriptItem;\n}(StackItem_js_1.BaseItem));\nexports.NonscriptItem = NonscriptItem;\nvar DotsItem = (function (_super) {\n    __extends(DotsItem, _super);\n    function DotsItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(DotsItem.prototype, \"kind\", {\n        get: function () {\n            return 'dots';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    DotsItem.prototype.checkItem = function (item) {\n        if (item.isKind('open') || item.isKind('left')) {\n            return StackItem_js_1.BaseItem.success;\n        }\n        var dots = this.getProperty('ldots');\n        var top = item.First;\n        if (item.isKind('mml') && NodeUtil_js_1.default.isEmbellished(top)) {\n            var tclass = NodeUtil_js_1.default.getTexClass(NodeUtil_js_1.default.getCoreMO(top));\n            if (tclass === MmlNode_js_1.TEXCLASS.BIN || tclass === MmlNode_js_1.TEXCLASS.REL) {\n                dots = this.getProperty('cdots');\n            }\n        }\n        return [[dots, item], true];\n    };\n    return DotsItem;\n}(StackItem_js_1.BaseItem));\nexports.DotsItem = DotsItem;\nvar ArrayItem = (function (_super) {\n    __extends(ArrayItem, _super);\n    function ArrayItem() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.table = [];\n        _this.row = [];\n        _this.frame = [];\n        _this.hfill = [];\n        _this.arraydef = {};\n        _this.dashed = false;\n        return _this;\n    }\n    Object.defineProperty(ArrayItem.prototype, \"kind\", {\n        get: function () {\n            return 'array';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(ArrayItem.prototype, \"isOpen\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(ArrayItem.prototype, \"copyEnv\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ArrayItem.prototype.checkItem = function (item) {\n        if (item.isClose && !item.isKind('over')) {\n            if (item.getProperty('isEntry')) {\n                this.EndEntry();\n                this.clearEnv();\n                return StackItem_js_1.BaseItem.fail;\n            }\n            if (item.getProperty('isCR')) {\n                this.EndEntry();\n                this.EndRow();\n                this.clearEnv();\n                return StackItem_js_1.BaseItem.fail;\n            }\n            this.EndTable();\n            this.clearEnv();\n            var newItem = this.factory.create('mml', this.createMml());\n            if (this.getProperty('requireClose')) {\n                if (item.isKind('close')) {\n                    return [[newItem], true];\n                }\n                throw new TexError_js_1.default('MissingCloseBrace', 'Missing close brace');\n            }\n            return [[newItem, item], true];\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    ArrayItem.prototype.createMml = function () {\n        var scriptlevel = this.arraydef['scriptlevel'];\n        delete this.arraydef['scriptlevel'];\n        var mml = this.create('node', 'mtable', this.table, this.arraydef);\n        if (scriptlevel) {\n            mml.setProperty('scriptlevel', scriptlevel);\n        }\n        if (this.frame.length === 4) {\n            NodeUtil_js_1.default.setAttribute(mml, 'frame', this.dashed ? 'dashed' : 'solid');\n        }\n        else if (this.frame.length) {\n            if (this.arraydef['rowlines']) {\n                this.arraydef['rowlines'] =\n                    this.arraydef['rowlines'].replace(/none( none)+$/, 'none');\n            }\n            NodeUtil_js_1.default.setAttribute(mml, 'frame', '');\n            mml = this.create('node', 'menclose', [mml], { notation: this.frame.join(' ') });\n            if ((this.arraydef['columnlines'] || 'none') !== 'none' ||\n                (this.arraydef['rowlines'] || 'none') !== 'none') {\n                NodeUtil_js_1.default.setAttribute(mml, 'data-padding', 0);\n            }\n        }\n        if (this.getProperty('open') || this.getProperty('close')) {\n            mml = ParseUtil_js_1.default.fenced(this.factory.configuration, this.getProperty('open'), mml, this.getProperty('close'));\n        }\n        return mml;\n    };\n    ArrayItem.prototype.EndEntry = function () {\n        var mtd = this.create('node', 'mtd', this.nodes);\n        if (this.hfill.length) {\n            if (this.hfill[0] === 0) {\n                NodeUtil_js_1.default.setAttribute(mtd, 'columnalign', 'right');\n            }\n            if (this.hfill[this.hfill.length - 1] === this.Size()) {\n                NodeUtil_js_1.default.setAttribute(mtd, 'columnalign', NodeUtil_js_1.default.getAttribute(mtd, 'columnalign') ? 'center' : 'left');\n            }\n        }\n        this.row.push(mtd);\n        this.Clear();\n        this.hfill = [];\n    };\n    ArrayItem.prototype.EndRow = function () {\n        var node;\n        if (this.getProperty('isNumbered') && this.row.length === 3) {\n            this.row.unshift(this.row.pop());\n            node = this.create('node', 'mlabeledtr', this.row);\n        }\n        else {\n            node = this.create('node', 'mtr', this.row);\n        }\n        this.table.push(node);\n        this.row = [];\n    };\n    ArrayItem.prototype.EndTable = function () {\n        if (this.Size() || this.row.length) {\n            this.EndEntry();\n            this.EndRow();\n        }\n        this.checkLines();\n    };\n    ArrayItem.prototype.checkLines = function () {\n        if (this.arraydef['rowlines']) {\n            var lines = this.arraydef['rowlines'].split(/ /);\n            if (lines.length === this.table.length) {\n                this.frame.push('bottom');\n                lines.pop();\n                this.arraydef['rowlines'] = lines.join(' ');\n            }\n            else if (lines.length < this.table.length - 1) {\n                this.arraydef['rowlines'] += ' none';\n            }\n        }\n        if (this.getProperty('rowspacing')) {\n            var rows = this.arraydef['rowspacing'].split(/ /);\n            while (rows.length < this.table.length) {\n                rows.push(this.getProperty('rowspacing') + 'em');\n            }\n            this.arraydef['rowspacing'] = rows.join(' ');\n        }\n    };\n    ArrayItem.prototype.addRowSpacing = function (spacing) {\n        if (this.arraydef['rowspacing']) {\n            var rows = this.arraydef['rowspacing'].split(/ /);\n            if (!this.getProperty('rowspacing')) {\n                var dimem = ParseUtil_js_1.default.dimen2em(rows[0]);\n                this.setProperty('rowspacing', dimem);\n            }\n            var rowspacing = this.getProperty('rowspacing');\n            while (rows.length < this.table.length) {\n                rows.push(ParseUtil_js_1.default.Em(rowspacing));\n            }\n            rows[this.table.length - 1] = ParseUtil_js_1.default.Em(Math.max(0, rowspacing + ParseUtil_js_1.default.dimen2em(spacing)));\n            this.arraydef['rowspacing'] = rows.join(' ');\n        }\n    };\n    return ArrayItem;\n}(StackItem_js_1.BaseItem));\nexports.ArrayItem = ArrayItem;\nvar EqnArrayItem = (function (_super) {\n    __extends(EqnArrayItem, _super);\n    function EqnArrayItem(factory) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        var _this = _super.call(this, factory) || this;\n        _this.maxrow = 0;\n        _this.factory.configuration.tags.start(args[0], args[2], args[1]);\n        return _this;\n    }\n    Object.defineProperty(EqnArrayItem.prototype, \"kind\", {\n        get: function () {\n            return 'eqnarray';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    EqnArrayItem.prototype.EndEntry = function () {\n        if (this.row.length) {\n            ParseUtil_js_1.default.fixInitialMO(this.factory.configuration, this.nodes);\n        }\n        var node = this.create('node', 'mtd', this.nodes);\n        this.row.push(node);\n        this.Clear();\n    };\n    EqnArrayItem.prototype.EndRow = function () {\n        if (this.row.length > this.maxrow) {\n            this.maxrow = this.row.length;\n        }\n        var mtr = 'mtr';\n        var tag = this.factory.configuration.tags.getTag();\n        if (tag) {\n            this.row = [tag].concat(this.row);\n            mtr = 'mlabeledtr';\n        }\n        this.factory.configuration.tags.clearTag();\n        var node = this.create('node', mtr, this.row);\n        this.table.push(node);\n        this.row = [];\n    };\n    EqnArrayItem.prototype.EndTable = function () {\n        _super.prototype.EndTable.call(this);\n        this.factory.configuration.tags.end();\n        this.extendArray('columnalign', this.maxrow);\n        this.extendArray('columnwidth', this.maxrow);\n        this.extendArray('columnspacing', this.maxrow - 1);\n    };\n    EqnArrayItem.prototype.extendArray = function (name, max) {\n        if (!this.arraydef[name])\n            return;\n        var repeat = this.arraydef[name].split(/ /);\n        var columns = __spreadArray([], __read(repeat), false);\n        if (columns.length > 1) {\n            while (columns.length < max) {\n                columns.push.apply(columns, __spreadArray([], __read(repeat), false));\n            }\n            this.arraydef[name] = columns.slice(0, max).join(' ');\n        }\n    };\n    return EqnArrayItem;\n}(ArrayItem));\nexports.EqnArrayItem = EqnArrayItem;\nvar EquationItem = (function (_super) {\n    __extends(EquationItem, _super);\n    function EquationItem(factory) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        var _this = _super.call(this, factory) || this;\n        _this.factory.configuration.tags.start('equation', true, args[0]);\n        return _this;\n    }\n    Object.defineProperty(EquationItem.prototype, \"kind\", {\n        get: function () {\n            return 'equation';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(EquationItem.prototype, \"isOpen\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    EquationItem.prototype.checkItem = function (item) {\n        if (item.isKind('end')) {\n            var mml = this.toMml();\n            var tag = this.factory.configuration.tags.getTag();\n            this.factory.configuration.tags.end();\n            return [[tag ? this.factory.configuration.tags.enTag(mml, tag) : mml, item], true];\n        }\n        if (item.isKind('stop')) {\n            throw new TexError_js_1.default('EnvMissingEnd', 'Missing \\\\end{%1}', this.getName());\n        }\n        return _super.prototype.checkItem.call(this, item);\n    };\n    return EquationItem;\n}(StackItem_js_1.BaseItem));\nexports.EquationItem = EquationItem;\n//# sourceMappingURL=BaseItems.js.map", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar sm = __importStar(require(\"../SymbolMap.js\"));\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar BaseMethods_js_1 = __importDefault(require(\"./BaseMethods.js\"));\nvar ParseMethods_js_1 = __importDefault(require(\"../ParseMethods.js\"));\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar lengths_js_1 = require(\"../../../util/lengths.js\");\nnew sm.RegExpMap('letter', ParseMethods_js_1.default.variable, /[a-z]/i);\nnew sm.RegExpMap('digit', ParseMethods_js_1.default.digit, /[0-9.,]/);\nnew sm.RegExpMap('command', ParseMethods_js_1.default.controlSequence, /^\\\\/);\nnew sm.MacroMap('special', {\n    '{': 'Open',\n    '}': 'Close',\n    '~': 'Tilde',\n    '^': 'Superscript',\n    '_': 'Subscript',\n    ' ': 'Space',\n    '\\t': 'Space',\n    '\\r': 'Space',\n    '\\n': 'Space',\n    '\\'': 'Prime',\n    '%': 'Comment',\n    '&': 'Entry',\n    '#': 'Hash',\n    '\\u00A0': 'Space',\n    '\\u2019': 'Prime'\n}, BaseMethods_js_1.default);\nnew sm.CharacterMap('mathchar0mi', ParseMethods_js_1.default.mathchar0mi, {\n    alpha: '\\u03B1',\n    beta: '\\u03B2',\n    gamma: '\\u03B3',\n    delta: '\\u03B4',\n    epsilon: '\\u03F5',\n    zeta: '\\u03B6',\n    eta: '\\u03B7',\n    theta: '\\u03B8',\n    iota: '\\u03B9',\n    kappa: '\\u03BA',\n    lambda: '\\u03BB',\n    mu: '\\u03BC',\n    nu: '\\u03BD',\n    xi: '\\u03BE',\n    omicron: '\\u03BF',\n    pi: '\\u03C0',\n    rho: '\\u03C1',\n    sigma: '\\u03C3',\n    tau: '\\u03C4',\n    upsilon: '\\u03C5',\n    phi: '\\u03D5',\n    chi: '\\u03C7',\n    psi: '\\u03C8',\n    omega: '\\u03C9',\n    varepsilon: '\\u03B5',\n    vartheta: '\\u03D1',\n    varpi: '\\u03D6',\n    varrho: '\\u03F1',\n    varsigma: '\\u03C2',\n    varphi: '\\u03C6',\n    S: ['\\u00A7', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    aleph: ['\\u2135', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    hbar: ['\\u210F', { variantForm: true }],\n    imath: '\\u0131',\n    jmath: '\\u0237',\n    ell: '\\u2113',\n    wp: ['\\u2118', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    Re: ['\\u211C', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    Im: ['\\u2111', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    partial: ['\\u2202', { mathvariant: TexConstants_js_1.TexConstant.Variant.ITALIC }],\n    infty: ['\\u221E', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    prime: ['\\u2032', { variantForm: true }],\n    emptyset: ['\\u2205', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    nabla: ['\\u2207', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    top: ['\\u22A4', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    bot: ['\\u22A5', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    angle: ['\\u2220', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    triangle: ['\\u25B3', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    backslash: ['\\u2216', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    forall: ['\\u2200', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    exists: ['\\u2203', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    neg: ['\\u00AC', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    lnot: ['\\u00AC', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    flat: ['\\u266D', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    natural: ['\\u266E', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    sharp: ['\\u266F', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    clubsuit: ['\\u2663', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    diamondsuit: ['\\u2662', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    heartsuit: ['\\u2661', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }],\n    spadesuit: ['\\u2660', { mathvariant: TexConstants_js_1.TexConstant.Variant.NORMAL }]\n});\nnew sm.CharacterMap('mathchar0mo', ParseMethods_js_1.default.mathchar0mo, {\n    surd: '\\u221A',\n    coprod: ['\\u2210', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    bigvee: ['\\u22C1', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    bigwedge: ['\\u22C0', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    biguplus: ['\\u2A04', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    bigcap: ['\\u22C2', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    bigcup: ['\\u22C3', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    'int': ['\\u222B', { texClass: MmlNode_js_1.TEXCLASS.OP }],\n    intop: ['\\u222B', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true, movablelimits: true }],\n    iint: ['\\u222C', { texClass: MmlNode_js_1.TEXCLASS.OP }],\n    iiint: ['\\u222D', { texClass: MmlNode_js_1.TEXCLASS.OP }],\n    prod: ['\\u220F', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    sum: ['\\u2211', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    bigotimes: ['\\u2A02', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    bigoplus: ['\\u2A01', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    bigodot: ['\\u2A00', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    oint: ['\\u222E', { texClass: MmlNode_js_1.TEXCLASS.OP }],\n    bigsqcup: ['\\u2A06', { texClass: MmlNode_js_1.TEXCLASS.OP,\n            movesupsub: true }],\n    smallint: ['\\u222B', { largeop: false }],\n    triangleleft: '\\u25C3',\n    triangleright: '\\u25B9',\n    bigtriangleup: '\\u25B3',\n    bigtriangledown: '\\u25BD',\n    wedge: '\\u2227',\n    land: '\\u2227',\n    vee: '\\u2228',\n    lor: '\\u2228',\n    cap: '\\u2229',\n    cup: '\\u222A',\n    ddagger: '\\u2021',\n    dagger: '\\u2020',\n    sqcap: '\\u2293',\n    sqcup: '\\u2294',\n    uplus: '\\u228E',\n    amalg: '\\u2A3F',\n    diamond: '\\u22C4',\n    bullet: '\\u2219',\n    wr: '\\u2240',\n    div: '\\u00F7',\n    divsymbol: '\\u00F7',\n    odot: ['\\u2299', { largeop: false }],\n    oslash: ['\\u2298', { largeop: false }],\n    otimes: ['\\u2297', { largeop: false }],\n    ominus: ['\\u2296', { largeop: false }],\n    oplus: ['\\u2295', { largeop: false }],\n    mp: '\\u2213',\n    pm: '\\u00B1',\n    circ: '\\u2218',\n    bigcirc: '\\u25EF',\n    setminus: '\\u2216',\n    cdot: '\\u22C5',\n    ast: '\\u2217',\n    times: '\\u00D7',\n    star: '\\u22C6',\n    propto: '\\u221D',\n    sqsubseteq: '\\u2291',\n    sqsupseteq: '\\u2292',\n    parallel: '\\u2225',\n    mid: '\\u2223',\n    dashv: '\\u22A3',\n    vdash: '\\u22A2',\n    leq: '\\u2264',\n    le: '\\u2264',\n    geq: '\\u2265',\n    ge: '\\u2265',\n    lt: '\\u003C',\n    gt: '\\u003E',\n    succ: '\\u227B',\n    prec: '\\u227A',\n    approx: '\\u2248',\n    succeq: '\\u2AB0',\n    preceq: '\\u2AAF',\n    supset: '\\u2283',\n    subset: '\\u2282',\n    supseteq: '\\u2287',\n    subseteq: '\\u2286',\n    'in': '\\u2208',\n    ni: '\\u220B',\n    notin: '\\u2209',\n    owns: '\\u220B',\n    gg: '\\u226B',\n    ll: '\\u226A',\n    sim: '\\u223C',\n    simeq: '\\u2243',\n    perp: '\\u22A5',\n    equiv: '\\u2261',\n    asymp: '\\u224D',\n    smile: '\\u2323',\n    frown: '\\u2322',\n    ne: '\\u2260',\n    neq: '\\u2260',\n    cong: '\\u2245',\n    doteq: '\\u2250',\n    bowtie: '\\u22C8',\n    models: '\\u22A8',\n    notChar: '\\u29F8',\n    Leftrightarrow: '\\u21D4',\n    Leftarrow: '\\u21D0',\n    Rightarrow: '\\u21D2',\n    leftrightarrow: '\\u2194',\n    leftarrow: '\\u2190',\n    gets: '\\u2190',\n    rightarrow: '\\u2192',\n    to: ['\\u2192', { accent: false }],\n    mapsto: '\\u21A6',\n    leftharpoonup: '\\u21BC',\n    leftharpoondown: '\\u21BD',\n    rightharpoonup: '\\u21C0',\n    rightharpoondown: '\\u21C1',\n    nearrow: '\\u2197',\n    searrow: '\\u2198',\n    nwarrow: '\\u2196',\n    swarrow: '\\u2199',\n    rightleftharpoons: '\\u21CC',\n    hookrightarrow: '\\u21AA',\n    hookleftarrow: '\\u21A9',\n    longleftarrow: '\\u27F5',\n    Longleftarrow: '\\u27F8',\n    longrightarrow: '\\u27F6',\n    Longrightarrow: '\\u27F9',\n    Longleftrightarrow: '\\u27FA',\n    longleftrightarrow: '\\u27F7',\n    longmapsto: '\\u27FC',\n    ldots: '\\u2026',\n    cdots: '\\u22EF',\n    vdots: '\\u22EE',\n    ddots: '\\u22F1',\n    dotsc: '\\u2026',\n    dotsb: '\\u22EF',\n    dotsm: '\\u22EF',\n    dotsi: '\\u22EF',\n    dotso: '\\u2026',\n    ldotp: ['\\u002E', { texClass: MmlNode_js_1.TEXCLASS.PUNCT }],\n    cdotp: ['\\u22C5', { texClass: MmlNode_js_1.TEXCLASS.PUNCT }],\n    colon: ['\\u003A', { texClass: MmlNode_js_1.TEXCLASS.PUNCT }]\n});\nnew sm.CharacterMap('mathchar7', ParseMethods_js_1.default.mathchar7, {\n    Gamma: '\\u0393',\n    Delta: '\\u0394',\n    Theta: '\\u0398',\n    Lambda: '\\u039B',\n    Xi: '\\u039E',\n    Pi: '\\u03A0',\n    Sigma: '\\u03A3',\n    Upsilon: '\\u03A5',\n    Phi: '\\u03A6',\n    Psi: '\\u03A8',\n    Omega: '\\u03A9',\n    '_': '\\u005F',\n    '#': '\\u0023',\n    '$': '\\u0024',\n    '%': '\\u0025',\n    '&': '\\u0026',\n    And: '\\u0026'\n});\nnew sm.DelimiterMap('delimiter', ParseMethods_js_1.default.delimiter, {\n    '(': '(',\n    ')': ')',\n    '[': '[',\n    ']': ']',\n    '<': '\\u27E8',\n    '>': '\\u27E9',\n    '\\\\lt': '\\u27E8',\n    '\\\\gt': '\\u27E9',\n    '/': '/',\n    '|': ['|', { texClass: MmlNode_js_1.TEXCLASS.ORD }],\n    '.': '',\n    '\\\\\\\\': '\\\\',\n    '\\\\lmoustache': '\\u23B0',\n    '\\\\rmoustache': '\\u23B1',\n    '\\\\lgroup': '\\u27EE',\n    '\\\\rgroup': '\\u27EF',\n    '\\\\arrowvert': '\\u23D0',\n    '\\\\Arrowvert': '\\u2016',\n    '\\\\bracevert': '\\u23AA',\n    '\\\\Vert': ['\\u2016', { texClass: MmlNode_js_1.TEXCLASS.ORD }],\n    '\\\\|': ['\\u2016', { texClass: MmlNode_js_1.TEXCLASS.ORD }],\n    '\\\\vert': ['|', { texClass: MmlNode_js_1.TEXCLASS.ORD }],\n    '\\\\uparrow': '\\u2191',\n    '\\\\downarrow': '\\u2193',\n    '\\\\updownarrow': '\\u2195',\n    '\\\\Uparrow': '\\u21D1',\n    '\\\\Downarrow': '\\u21D3',\n    '\\\\Updownarrow': '\\u21D5',\n    '\\\\backslash': '\\\\',\n    '\\\\rangle': '\\u27E9',\n    '\\\\langle': '\\u27E8',\n    '\\\\rbrace': '}',\n    '\\\\lbrace': '{',\n    '\\\\}': '}',\n    '\\\\{': '{',\n    '\\\\rceil': '\\u2309',\n    '\\\\lceil': '\\u2308',\n    '\\\\rfloor': '\\u230B',\n    '\\\\lfloor': '\\u230A',\n    '\\\\lbrack': '[',\n    '\\\\rbrack': ']'\n});\nnew sm.CommandMap('macros', {\n    displaystyle: ['SetStyle', 'D', true, 0],\n    textstyle: ['SetStyle', 'T', false, 0],\n    scriptstyle: ['SetStyle', 'S', false, 1],\n    scriptscriptstyle: ['SetStyle', 'SS', false, 2],\n    rm: ['SetFont', TexConstants_js_1.TexConstant.Variant.NORMAL],\n    mit: ['SetFont', TexConstants_js_1.TexConstant.Variant.ITALIC],\n    oldstyle: ['SetFont', TexConstants_js_1.TexConstant.Variant.OLDSTYLE],\n    cal: ['SetFont', TexConstants_js_1.TexConstant.Variant.CALLIGRAPHIC],\n    it: ['SetFont', TexConstants_js_1.TexConstant.Variant.MATHITALIC],\n    bf: ['SetFont', TexConstants_js_1.TexConstant.Variant.BOLD],\n    bbFont: ['SetFont', TexConstants_js_1.TexConstant.Variant.DOUBLESTRUCK],\n    scr: ['SetFont', TexConstants_js_1.TexConstant.Variant.SCRIPT],\n    frak: ['SetFont', TexConstants_js_1.TexConstant.Variant.FRAKTUR],\n    sf: ['SetFont', TexConstants_js_1.TexConstant.Variant.SANSSERIF],\n    tt: ['SetFont', TexConstants_js_1.TexConstant.Variant.MONOSPACE],\n    mathrm: ['MathFont', TexConstants_js_1.TexConstant.Variant.NORMAL],\n    mathup: ['MathFont', TexConstants_js_1.TexConstant.Variant.NORMAL],\n    mathnormal: ['MathFont', ''],\n    mathbf: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLD],\n    mathbfup: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLD],\n    mathit: ['MathFont', TexConstants_js_1.TexConstant.Variant.MATHITALIC],\n    mathbfit: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDITALIC],\n    mathbb: ['MathFont', TexConstants_js_1.TexConstant.Variant.DOUBLESTRUCK],\n    Bbb: ['MathFont', TexConstants_js_1.TexConstant.Variant.DOUBLESTRUCK],\n    mathfrak: ['MathFont', TexConstants_js_1.TexConstant.Variant.FRAKTUR],\n    mathbffrak: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDFRAKTUR],\n    mathscr: ['MathFont', TexConstants_js_1.TexConstant.Variant.SCRIPT],\n    mathbfscr: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDSCRIPT],\n    mathsf: ['MathFont', TexConstants_js_1.TexConstant.Variant.SANSSERIF],\n    mathsfup: ['MathFont', TexConstants_js_1.TexConstant.Variant.SANSSERIF],\n    mathbfsf: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDSANSSERIF],\n    mathbfsfup: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDSANSSERIF],\n    mathsfit: ['MathFont', TexConstants_js_1.TexConstant.Variant.SANSSERIFITALIC],\n    mathbfsfit: ['MathFont', TexConstants_js_1.TexConstant.Variant.SANSSERIFBOLDITALIC],\n    mathtt: ['MathFont', TexConstants_js_1.TexConstant.Variant.MONOSPACE],\n    mathcal: ['MathFont', TexConstants_js_1.TexConstant.Variant.CALLIGRAPHIC],\n    mathbfcal: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDCALLIGRAPHIC],\n    symrm: ['MathFont', TexConstants_js_1.TexConstant.Variant.NORMAL],\n    symup: ['MathFont', TexConstants_js_1.TexConstant.Variant.NORMAL],\n    symnormal: ['MathFont', ''],\n    symbf: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLD],\n    symbfup: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLD],\n    symit: ['MathFont', TexConstants_js_1.TexConstant.Variant.ITALIC],\n    symbfit: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDITALIC],\n    symbb: ['MathFont', TexConstants_js_1.TexConstant.Variant.DOUBLESTRUCK],\n    symfrak: ['MathFont', TexConstants_js_1.TexConstant.Variant.FRAKTUR],\n    symbffrak: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDFRAKTUR],\n    symscr: ['MathFont', TexConstants_js_1.TexConstant.Variant.SCRIPT],\n    symbfscr: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDSCRIPT],\n    symsf: ['MathFont', TexConstants_js_1.TexConstant.Variant.SANSSERIF],\n    symsfup: ['MathFont', TexConstants_js_1.TexConstant.Variant.SANSSERIF],\n    symbfsf: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDSANSSERIF],\n    symbfsfup: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDSANSSERIF],\n    symsfit: ['MathFont', TexConstants_js_1.TexConstant.Variant.SANSSERIFITALIC],\n    symbfsfit: ['MathFont', TexConstants_js_1.TexConstant.Variant.SANSSERIFBOLDITALIC],\n    symtt: ['MathFont', TexConstants_js_1.TexConstant.Variant.MONOSPACE],\n    symcal: ['MathFont', TexConstants_js_1.TexConstant.Variant.CALLIGRAPHIC],\n    symbfcal: ['MathFont', TexConstants_js_1.TexConstant.Variant.BOLDCALLIGRAPHIC],\n    textrm: ['HBox', null, TexConstants_js_1.TexConstant.Variant.NORMAL],\n    textup: ['HBox', null, TexConstants_js_1.TexConstant.Variant.NORMAL],\n    textnormal: ['HBox'],\n    textit: ['HBox', null, TexConstants_js_1.TexConstant.Variant.ITALIC],\n    textbf: ['HBox', null, TexConstants_js_1.TexConstant.Variant.BOLD],\n    textsf: ['HBox', null, TexConstants_js_1.TexConstant.Variant.SANSSERIF],\n    texttt: ['HBox', null, TexConstants_js_1.TexConstant.Variant.MONOSPACE],\n    tiny: ['SetSize', 0.5],\n    Tiny: ['SetSize', 0.6],\n    scriptsize: ['SetSize', 0.7],\n    small: ['SetSize', 0.85],\n    normalsize: ['SetSize', 1.0],\n    large: ['SetSize', 1.2],\n    Large: ['SetSize', 1.44],\n    LARGE: ['SetSize', 1.73],\n    huge: ['SetSize', 2.07],\n    Huge: ['SetSize', 2.49],\n    arcsin: 'NamedFn',\n    arccos: 'NamedFn',\n    arctan: 'NamedFn',\n    arg: 'NamedFn',\n    cos: 'NamedFn',\n    cosh: 'NamedFn',\n    cot: 'NamedFn',\n    coth: 'NamedFn',\n    csc: 'NamedFn',\n    deg: 'NamedFn',\n    det: 'NamedOp',\n    dim: 'NamedFn',\n    exp: 'NamedFn',\n    gcd: 'NamedOp',\n    hom: 'NamedFn',\n    inf: 'NamedOp',\n    ker: 'NamedFn',\n    lg: 'NamedFn',\n    lim: 'NamedOp',\n    liminf: ['NamedOp', 'lim&thinsp;inf'],\n    limsup: ['NamedOp', 'lim&thinsp;sup'],\n    ln: 'NamedFn',\n    log: 'NamedFn',\n    max: 'NamedOp',\n    min: 'NamedOp',\n    Pr: 'NamedOp',\n    sec: 'NamedFn',\n    sin: 'NamedFn',\n    sinh: 'NamedFn',\n    sup: 'NamedOp',\n    tan: 'NamedFn',\n    tanh: 'NamedFn',\n    limits: ['Limits', 1],\n    nolimits: ['Limits', 0],\n    overline: ['UnderOver', '2015'],\n    underline: ['UnderOver', '2015'],\n    overbrace: ['UnderOver', '23DE', 1],\n    underbrace: ['UnderOver', '23DF', 1],\n    overparen: ['UnderOver', '23DC'],\n    underparen: ['UnderOver', '23DD'],\n    overrightarrow: ['UnderOver', '2192'],\n    underrightarrow: ['UnderOver', '2192'],\n    overleftarrow: ['UnderOver', '2190'],\n    underleftarrow: ['UnderOver', '2190'],\n    overleftrightarrow: ['UnderOver', '2194'],\n    underleftrightarrow: ['UnderOver', '2194'],\n    overset: 'Overset',\n    underset: 'Underset',\n    overunderset: 'Overunderset',\n    stackrel: ['Macro', '\\\\mathrel{\\\\mathop{#2}\\\\limits^{#1}}', 2],\n    stackbin: ['Macro', '\\\\mathbin{\\\\mathop{#2}\\\\limits^{#1}}', 2],\n    over: 'Over',\n    overwithdelims: 'Over',\n    atop: 'Over',\n    atopwithdelims: 'Over',\n    above: 'Over',\n    abovewithdelims: 'Over',\n    brace: ['Over', '{', '}'],\n    brack: ['Over', '[', ']'],\n    choose: ['Over', '(', ')'],\n    frac: 'Frac',\n    sqrt: 'Sqrt',\n    root: 'Root',\n    uproot: ['MoveRoot', 'upRoot'],\n    leftroot: ['MoveRoot', 'leftRoot'],\n    left: 'LeftRight',\n    right: 'LeftRight',\n    middle: 'LeftRight',\n    llap: 'Lap',\n    rlap: 'Lap',\n    raise: 'RaiseLower',\n    lower: 'RaiseLower',\n    moveleft: 'MoveLeftRight',\n    moveright: 'MoveLeftRight',\n    ',': ['Spacer', lengths_js_1.MATHSPACE.thinmathspace],\n    ':': ['Spacer', lengths_js_1.MATHSPACE.mediummathspace],\n    '>': ['Spacer', lengths_js_1.MATHSPACE.mediummathspace],\n    ';': ['Spacer', lengths_js_1.MATHSPACE.thickmathspace],\n    '!': ['Spacer', lengths_js_1.MATHSPACE.negativethinmathspace],\n    enspace: ['Spacer', .5],\n    quad: ['Spacer', 1],\n    qquad: ['Spacer', 2],\n    thinspace: ['Spacer', lengths_js_1.MATHSPACE.thinmathspace],\n    negthinspace: ['Spacer', lengths_js_1.MATHSPACE.negativethinmathspace],\n    hskip: 'Hskip',\n    hspace: 'Hskip',\n    kern: 'Hskip',\n    mskip: 'Hskip',\n    mspace: 'Hskip',\n    mkern: 'Hskip',\n    rule: 'rule',\n    Rule: ['Rule'],\n    Space: ['Rule', 'blank'],\n    nonscript: 'Nonscript',\n    big: ['MakeBig', MmlNode_js_1.TEXCLASS.ORD, 0.85],\n    Big: ['MakeBig', MmlNode_js_1.TEXCLASS.ORD, 1.15],\n    bigg: ['MakeBig', MmlNode_js_1.TEXCLASS.ORD, 1.45],\n    Bigg: ['MakeBig', MmlNode_js_1.TEXCLASS.ORD, 1.75],\n    bigl: ['MakeBig', MmlNode_js_1.TEXCLASS.OPEN, 0.85],\n    Bigl: ['MakeBig', MmlNode_js_1.TEXCLASS.OPEN, 1.15],\n    biggl: ['MakeBig', MmlNode_js_1.TEXCLASS.OPEN, 1.45],\n    Biggl: ['MakeBig', MmlNode_js_1.TEXCLASS.OPEN, 1.75],\n    bigr: ['MakeBig', MmlNode_js_1.TEXCLASS.CLOSE, 0.85],\n    Bigr: ['MakeBig', MmlNode_js_1.TEXCLASS.CLOSE, 1.15],\n    biggr: ['MakeBig', MmlNode_js_1.TEXCLASS.CLOSE, 1.45],\n    Biggr: ['MakeBig', MmlNode_js_1.TEXCLASS.CLOSE, 1.75],\n    bigm: ['MakeBig', MmlNode_js_1.TEXCLASS.REL, 0.85],\n    Bigm: ['MakeBig', MmlNode_js_1.TEXCLASS.REL, 1.15],\n    biggm: ['MakeBig', MmlNode_js_1.TEXCLASS.REL, 1.45],\n    Biggm: ['MakeBig', MmlNode_js_1.TEXCLASS.REL, 1.75],\n    mathord: ['TeXAtom', MmlNode_js_1.TEXCLASS.ORD],\n    mathop: ['TeXAtom', MmlNode_js_1.TEXCLASS.OP],\n    mathopen: ['TeXAtom', MmlNode_js_1.TEXCLASS.OPEN],\n    mathclose: ['TeXAtom', MmlNode_js_1.TEXCLASS.CLOSE],\n    mathbin: ['TeXAtom', MmlNode_js_1.TEXCLASS.BIN],\n    mathrel: ['TeXAtom', MmlNode_js_1.TEXCLASS.REL],\n    mathpunct: ['TeXAtom', MmlNode_js_1.TEXCLASS.PUNCT],\n    mathinner: ['TeXAtom', MmlNode_js_1.TEXCLASS.INNER],\n    vcenter: ['TeXAtom', MmlNode_js_1.TEXCLASS.VCENTER],\n    buildrel: 'BuildRel',\n    hbox: ['HBox', 0],\n    text: 'HBox',\n    mbox: ['HBox', 0],\n    fbox: 'FBox',\n    boxed: ['Macro', '\\\\fbox{$\\\\displaystyle{#1}$}', 1],\n    framebox: 'FrameBox',\n    strut: 'Strut',\n    mathstrut: ['Macro', '\\\\vphantom{(}'],\n    phantom: 'Phantom',\n    vphantom: ['Phantom', 1, 0],\n    hphantom: ['Phantom', 0, 1],\n    smash: 'Smash',\n    acute: ['Accent', '00B4'],\n    grave: ['Accent', '0060'],\n    ddot: ['Accent', '00A8'],\n    tilde: ['Accent', '007E'],\n    bar: ['Accent', '00AF'],\n    breve: ['Accent', '02D8'],\n    check: ['Accent', '02C7'],\n    hat: ['Accent', '005E'],\n    vec: ['Accent', '2192'],\n    dot: ['Accent', '02D9'],\n    widetilde: ['Accent', '007E', 1],\n    widehat: ['Accent', '005E', 1],\n    matrix: 'Matrix',\n    array: 'Matrix',\n    pmatrix: ['Matrix', '(', ')'],\n    cases: ['Matrix', '{', '', 'left left', null, '.1em', null,\n        true],\n    eqalign: ['Matrix', null, null, 'right left',\n        (0, lengths_js_1.em)(lengths_js_1.MATHSPACE.thickmathspace), '.5em', 'D'],\n    displaylines: ['Matrix', null, null, 'center', null, '.5em', 'D'],\n    cr: 'Cr',\n    '\\\\': 'CrLaTeX',\n    newline: ['CrLaTeX', true],\n    hline: ['HLine', 'solid'],\n    hdashline: ['HLine', 'dashed'],\n    eqalignno: ['Matrix', null, null, 'right left',\n        (0, lengths_js_1.em)(lengths_js_1.MATHSPACE.thickmathspace), '.5em', 'D', null,\n        'right'],\n    leqalignno: ['Matrix', null, null, 'right left',\n        (0, lengths_js_1.em)(lengths_js_1.MATHSPACE.thickmathspace), '.5em', 'D', null,\n        'left'],\n    hfill: 'HFill',\n    hfil: 'HFill',\n    hfilll: 'HFill',\n    bmod: ['Macro', '\\\\mmlToken{mo}[lspace=\"thickmathspace\"' +\n            ' rspace=\"thickmathspace\"]{mod}'],\n    pmod: ['Macro', '\\\\pod{\\\\mmlToken{mi}{mod}\\\\kern 6mu #1}', 1],\n    mod: ['Macro', '\\\\mathchoice{\\\\kern18mu}{\\\\kern12mu}' +\n            '{\\\\kern12mu}{\\\\kern12mu}\\\\mmlToken{mi}{mod}\\\\,\\\\,#1',\n        1],\n    pod: ['Macro', '\\\\mathchoice{\\\\kern18mu}{\\\\kern8mu}' +\n            '{\\\\kern8mu}{\\\\kern8mu}(#1)', 1],\n    iff: ['Macro', '\\\\;\\\\Longleftrightarrow\\\\;'],\n    skew: ['Macro', '{{#2{#3\\\\mkern#1mu}\\\\mkern-#1mu}{}}', 3],\n    pmb: ['Macro', '\\\\rlap{#1}\\\\kern1px{#1}', 1],\n    TeX: ['Macro', 'T\\\\kern-.14em\\\\lower.5ex{E}\\\\kern-.115em X'],\n    LaTeX: ['Macro', 'L\\\\kern-.325em\\\\raise.21em' +\n            '{\\\\scriptstyle{A}}\\\\kern-.17em\\\\TeX'],\n    ' ': ['Macro', '\\\\text{ }'],\n    not: 'Not',\n    dots: 'Dots',\n    space: 'Tilde',\n    '\\u00A0': 'Tilde',\n    begin: 'BeginEnd',\n    end: 'BeginEnd',\n    label: 'HandleLabel',\n    ref: 'HandleRef',\n    nonumber: 'HandleNoTag',\n    mathchoice: 'MathChoice',\n    mmlToken: 'MmlToken'\n}, BaseMethods_js_1.default);\nnew sm.EnvironmentMap('environment', ParseMethods_js_1.default.environment, {\n    array: ['AlignedArray'],\n    equation: ['Equation', null, true],\n    eqnarray: ['EqnArray', null, true, true, 'rcl',\n        ParseUtil_js_1.default.cols(0, lengths_js_1.MATHSPACE.thickmathspace), '.5em']\n}, BaseMethods_js_1.default);\nnew sm.CharacterMap('not_remap', null, {\n    '\\u2190': '\\u219A',\n    '\\u2192': '\\u219B',\n    '\\u2194': '\\u21AE',\n    '\\u21D0': '\\u21CD',\n    '\\u21D2': '\\u21CF',\n    '\\u21D4': '\\u21CE',\n    '\\u2208': '\\u2209',\n    '\\u220B': '\\u220C',\n    '\\u2223': '\\u2224',\n    '\\u2225': '\\u2226',\n    '\\u223C': '\\u2241',\n    '\\u007E': '\\u2241',\n    '\\u2243': '\\u2244',\n    '\\u2245': '\\u2247',\n    '\\u2248': '\\u2249',\n    '\\u224D': '\\u226D',\n    '\\u003D': '\\u2260',\n    '\\u2261': '\\u2262',\n    '\\u003C': '\\u226E',\n    '\\u003E': '\\u226F',\n    '\\u2264': '\\u2270',\n    '\\u2265': '\\u2271',\n    '\\u2272': '\\u2274',\n    '\\u2273': '\\u2275',\n    '\\u2276': '\\u2278',\n    '\\u2277': '\\u2279',\n    '\\u227A': '\\u2280',\n    '\\u227B': '\\u2281',\n    '\\u2282': '\\u2284',\n    '\\u2283': '\\u2285',\n    '\\u2286': '\\u2288',\n    '\\u2287': '\\u2289',\n    '\\u22A2': '\\u22AC',\n    '\\u22A8': '\\u22AD',\n    '\\u22A9': '\\u22AE',\n    '\\u22AB': '\\u22AF',\n    '\\u227C': '\\u22E0',\n    '\\u227D': '\\u22E1',\n    '\\u2291': '\\u22E2',\n    '\\u2292': '\\u22E3',\n    '\\u22B2': '\\u22EA',\n    '\\u22B3': '\\u22EB',\n    '\\u22B4': '\\u22EC',\n    '\\u22B5': '\\u22ED',\n    '\\u2203': '\\u2204'\n});\n//# sourceMappingURL=BaseMappings.js.map", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar sitem = __importStar(require(\"./BaseItems.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"../NodeUtil.js\"));\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar TexParser_js_1 = __importDefault(require(\"../TexParser.js\"));\nvar TexConstants_js_1 = require(\"../TexConstants.js\");\nvar ParseUtil_js_1 = __importDefault(require(\"../ParseUtil.js\"));\nvar MmlNode_js_1 = require(\"../../../core/MmlTree/MmlNode.js\");\nvar Tags_js_1 = require(\"../Tags.js\");\nvar lengths_js_1 = require(\"../../../util/lengths.js\");\nvar Entities_js_1 = require(\"../../../util/Entities.js\");\nvar Options_js_1 = require(\"../../../util/Options.js\");\nvar BaseMethods = {};\nvar P_HEIGHT = 1.2 / .85;\nvar MmlTokenAllow = {\n    fontfamily: 1, fontsize: 1, fontweight: 1, fontstyle: 1,\n    color: 1, background: 1,\n    id: 1, 'class': 1, href: 1, style: 1\n};\nBaseMethods.Open = function (parser, _c) {\n    parser.Push(parser.itemFactory.create('open'));\n};\nBaseMethods.Close = function (parser, _c) {\n    parser.Push(parser.itemFactory.create('close'));\n};\nBaseMethods.Tilde = function (parser, _c) {\n    parser.Push(parser.create('token', 'mtext', {}, Entities_js_1.entities.nbsp));\n};\nBaseMethods.Space = function (_parser, _c) { };\nBaseMethods.Superscript = function (parser, _c) {\n    var _a;\n    if (parser.GetNext().match(/\\d/)) {\n        parser.string = parser.string.substr(0, parser.i + 1) +\n            ' ' + parser.string.substr(parser.i + 1);\n    }\n    var primes;\n    var base;\n    var top = parser.stack.Top();\n    if (top.isKind('prime')) {\n        _a = __read(top.Peek(2), 2), base = _a[0], primes = _a[1];\n        parser.stack.Pop();\n    }\n    else {\n        base = parser.stack.Prev();\n        if (!base) {\n            base = parser.create('token', 'mi', {}, '');\n        }\n    }\n    var movesupsub = NodeUtil_js_1.default.getProperty(base, 'movesupsub');\n    var position = NodeUtil_js_1.default.isType(base, 'msubsup') ? base.sup :\n        base.over;\n    if ((NodeUtil_js_1.default.isType(base, 'msubsup') && !NodeUtil_js_1.default.isType(base, 'msup') &&\n        NodeUtil_js_1.default.getChildAt(base, base.sup)) ||\n        (NodeUtil_js_1.default.isType(base, 'munderover') && !NodeUtil_js_1.default.isType(base, 'mover') &&\n            NodeUtil_js_1.default.getChildAt(base, base.over) &&\n            !NodeUtil_js_1.default.getProperty(base, 'subsupOK'))) {\n        throw new TexError_js_1.default('DoubleExponent', 'Double exponent: use braces to clarify');\n    }\n    if (!NodeUtil_js_1.default.isType(base, 'msubsup') || NodeUtil_js_1.default.isType(base, 'msup')) {\n        if (movesupsub) {\n            if (!NodeUtil_js_1.default.isType(base, 'munderover') || NodeUtil_js_1.default.isType(base, 'mover') ||\n                NodeUtil_js_1.default.getChildAt(base, base.over)) {\n                base = parser.create('node', 'munderover', [base], { movesupsub: true });\n            }\n            position = base.over;\n        }\n        else {\n            base = parser.create('node', 'msubsup', [base]);\n            position = base.sup;\n        }\n    }\n    parser.Push(parser.itemFactory.create('subsup', base).setProperties({\n        position: position, primes: primes, movesupsub: movesupsub\n    }));\n};\nBaseMethods.Subscript = function (parser, _c) {\n    var _a;\n    if (parser.GetNext().match(/\\d/)) {\n        parser.string =\n            parser.string.substr(0, parser.i + 1) + ' ' +\n                parser.string.substr(parser.i + 1);\n    }\n    var primes, base;\n    var top = parser.stack.Top();\n    if (top.isKind('prime')) {\n        _a = __read(top.Peek(2), 2), base = _a[0], primes = _a[1];\n        parser.stack.Pop();\n    }\n    else {\n        base = parser.stack.Prev();\n        if (!base) {\n            base = parser.create('token', 'mi', {}, '');\n        }\n    }\n    var movesupsub = NodeUtil_js_1.default.getProperty(base, 'movesupsub');\n    var position = NodeUtil_js_1.default.isType(base, 'msubsup') ?\n        base.sub : base.under;\n    if ((NodeUtil_js_1.default.isType(base, 'msubsup') && !NodeUtil_js_1.default.isType(base, 'msup') &&\n        NodeUtil_js_1.default.getChildAt(base, base.sub)) ||\n        (NodeUtil_js_1.default.isType(base, 'munderover') && !NodeUtil_js_1.default.isType(base, 'mover') &&\n            NodeUtil_js_1.default.getChildAt(base, base.under) &&\n            !NodeUtil_js_1.default.getProperty(base, 'subsupOK'))) {\n        throw new TexError_js_1.default('DoubleSubscripts', 'Double subscripts: use braces to clarify');\n    }\n    if (!NodeUtil_js_1.default.isType(base, 'msubsup') || NodeUtil_js_1.default.isType(base, 'msup')) {\n        if (movesupsub) {\n            if (!NodeUtil_js_1.default.isType(base, 'munderover') || NodeUtil_js_1.default.isType(base, 'mover') ||\n                NodeUtil_js_1.default.getChildAt(base, base.under)) {\n                base = parser.create('node', 'munderover', [base], { movesupsub: true });\n            }\n            position = base.under;\n        }\n        else {\n            base = parser.create('node', 'msubsup', [base]);\n            position = base.sub;\n        }\n    }\n    parser.Push(parser.itemFactory.create('subsup', base).setProperties({\n        position: position, primes: primes, movesupsub: movesupsub\n    }));\n};\nBaseMethods.Prime = function (parser, c) {\n    var base = parser.stack.Prev();\n    if (!base) {\n        base = parser.create('node', 'mi');\n    }\n    if (NodeUtil_js_1.default.isType(base, 'msubsup') && !NodeUtil_js_1.default.isType(base, 'msup') &&\n        NodeUtil_js_1.default.getChildAt(base, base.sup)) {\n        throw new TexError_js_1.default('DoubleExponentPrime', 'Prime causes double exponent: use braces to clarify');\n    }\n    var sup = '';\n    parser.i--;\n    do {\n        sup += Entities_js_1.entities.prime;\n        parser.i++, c = parser.GetNext();\n    } while (c === '\\'' || c === Entities_js_1.entities.rsquo);\n    sup = ['', '\\u2032', '\\u2033', '\\u2034', '\\u2057'][sup.length] || sup;\n    var node = parser.create('token', 'mo', { variantForm: true }, sup);\n    parser.Push(parser.itemFactory.create('prime', base, node));\n};\nBaseMethods.Comment = function (parser, _c) {\n    while (parser.i < parser.string.length && parser.string.charAt(parser.i) !== '\\n') {\n        parser.i++;\n    }\n};\nBaseMethods.Hash = function (_parser, _c) {\n    throw new TexError_js_1.default('CantUseHash1', 'You can\\'t use \\'macro parameter character #\\' in math mode');\n};\nBaseMethods.MathFont = function (parser, name, variant) {\n    var text = parser.GetArgument(name);\n    var mml = new TexParser_js_1.default(text, __assign(__assign({}, parser.stack.env), { font: variant, multiLetterIdentifiers: /^[a-zA-Z]+/, noAutoOP: true }), parser.configuration).mml();\n    parser.Push(parser.create('node', 'TeXAtom', [mml]));\n};\nBaseMethods.SetFont = function (parser, _name, font) {\n    parser.stack.env['font'] = font;\n};\nBaseMethods.SetStyle = function (parser, _name, texStyle, style, level) {\n    parser.stack.env['style'] = texStyle;\n    parser.stack.env['level'] = level;\n    parser.Push(parser.itemFactory.create('style').setProperty('styles', { displaystyle: style, scriptlevel: level }));\n};\nBaseMethods.SetSize = function (parser, _name, size) {\n    parser.stack.env['size'] = size;\n    parser.Push(parser.itemFactory.create('style').setProperty('styles', { mathsize: (0, lengths_js_1.em)(size) }));\n};\nBaseMethods.Spacer = function (parser, _name, space) {\n    var node = parser.create('node', 'mspace', [], { width: (0, lengths_js_1.em)(space) });\n    var style = parser.create('node', 'mstyle', [node], { scriptlevel: 0 });\n    parser.Push(style);\n};\nBaseMethods.LeftRight = function (parser, name) {\n    var first = name.substr(1);\n    parser.Push(parser.itemFactory.create(first, parser.GetDelimiter(name), parser.stack.env.color));\n};\nBaseMethods.NamedFn = function (parser, name, id) {\n    if (!id) {\n        id = name.substr(1);\n    }\n    var mml = parser.create('token', 'mi', { texClass: MmlNode_js_1.TEXCLASS.OP }, id);\n    parser.Push(parser.itemFactory.create('fn', mml));\n};\nBaseMethods.NamedOp = function (parser, name, id) {\n    if (!id) {\n        id = name.substr(1);\n    }\n    id = id.replace(/&thinsp;/, '\\u2006');\n    var mml = parser.create('token', 'mo', {\n        movablelimits: true,\n        movesupsub: true,\n        form: TexConstants_js_1.TexConstant.Form.PREFIX,\n        texClass: MmlNode_js_1.TEXCLASS.OP\n    }, id);\n    parser.Push(mml);\n};\nBaseMethods.Limits = function (parser, _name, limits) {\n    var op = parser.stack.Prev(true);\n    if (!op || (NodeUtil_js_1.default.getTexClass(NodeUtil_js_1.default.getCoreMO(op)) !== MmlNode_js_1.TEXCLASS.OP &&\n        NodeUtil_js_1.default.getProperty(op, 'movesupsub') == null)) {\n        throw new TexError_js_1.default('MisplacedLimits', '%1 is allowed only on operators', parser.currentCS);\n    }\n    var top = parser.stack.Top();\n    var node;\n    if (NodeUtil_js_1.default.isType(op, 'munderover') && !limits) {\n        node = parser.create('node', 'msubsup');\n        NodeUtil_js_1.default.copyChildren(op, node);\n        op = top.Last = node;\n    }\n    else if (NodeUtil_js_1.default.isType(op, 'msubsup') && limits) {\n        node = parser.create('node', 'munderover');\n        NodeUtil_js_1.default.copyChildren(op, node);\n        op = top.Last = node;\n    }\n    NodeUtil_js_1.default.setProperty(op, 'movesupsub', limits ? true : false);\n    NodeUtil_js_1.default.setProperties(NodeUtil_js_1.default.getCoreMO(op), { 'movablelimits': false });\n    if (NodeUtil_js_1.default.getAttribute(op, 'movablelimits') ||\n        NodeUtil_js_1.default.getProperty(op, 'movablelimits')) {\n        NodeUtil_js_1.default.setProperties(op, { 'movablelimits': false });\n    }\n};\nBaseMethods.Over = function (parser, name, open, close) {\n    var mml = parser.itemFactory.create('over').setProperty('name', parser.currentCS);\n    if (open || close) {\n        mml.setProperty('open', open);\n        mml.setProperty('close', close);\n    }\n    else if (name.match(/withdelims$/)) {\n        mml.setProperty('open', parser.GetDelimiter(name));\n        mml.setProperty('close', parser.GetDelimiter(name));\n    }\n    if (name.match(/^\\\\above/)) {\n        mml.setProperty('thickness', parser.GetDimen(name));\n    }\n    else if (name.match(/^\\\\atop/) || open || close) {\n        mml.setProperty('thickness', 0);\n    }\n    parser.Push(mml);\n};\nBaseMethods.Frac = function (parser, name) {\n    var num = parser.ParseArg(name);\n    var den = parser.ParseArg(name);\n    var node = parser.create('node', 'mfrac', [num, den]);\n    parser.Push(node);\n};\nBaseMethods.Sqrt = function (parser, name) {\n    var n = parser.GetBrackets(name);\n    var arg = parser.GetArgument(name);\n    if (arg === '\\\\frac') {\n        arg += '{' + parser.GetArgument(arg) + '}{' + parser.GetArgument(arg) + '}';\n    }\n    var mml = new TexParser_js_1.default(arg, parser.stack.env, parser.configuration).mml();\n    if (!n) {\n        mml = parser.create('node', 'msqrt', [mml]);\n    }\n    else {\n        mml = parser.create('node', 'mroot', [mml, parseRoot(parser, n)]);\n    }\n    parser.Push(mml);\n};\nfunction parseRoot(parser, n) {\n    var env = parser.stack.env;\n    var inRoot = env['inRoot'];\n    env['inRoot'] = true;\n    var newParser = new TexParser_js_1.default(n, env, parser.configuration);\n    var node = newParser.mml();\n    var global = newParser.stack.global;\n    if (global['leftRoot'] || global['upRoot']) {\n        var def = {};\n        if (global['leftRoot']) {\n            def['width'] = global['leftRoot'];\n        }\n        if (global['upRoot']) {\n            def['voffset'] = global['upRoot'];\n            def['height'] = global['upRoot'];\n        }\n        node = parser.create('node', 'mpadded', [node], def);\n    }\n    env['inRoot'] = inRoot;\n    return node;\n}\nBaseMethods.Root = function (parser, name) {\n    var n = parser.GetUpTo(name, '\\\\of');\n    var arg = parser.ParseArg(name);\n    var node = parser.create('node', 'mroot', [arg, parseRoot(parser, n)]);\n    parser.Push(node);\n};\nBaseMethods.MoveRoot = function (parser, name, id) {\n    if (!parser.stack.env['inRoot']) {\n        throw new TexError_js_1.default('MisplacedMoveRoot', '%1 can appear only within a root', parser.currentCS);\n    }\n    if (parser.stack.global[id]) {\n        throw new TexError_js_1.default('MultipleMoveRoot', 'Multiple use of %1', parser.currentCS);\n    }\n    var n = parser.GetArgument(name);\n    if (!n.match(/-?[0-9]+/)) {\n        throw new TexError_js_1.default('IntegerArg', 'The argument to %1 must be an integer', parser.currentCS);\n    }\n    n = (parseInt(n, 10) / 15) + 'em';\n    if (n.substr(0, 1) !== '-') {\n        n = '+' + n;\n    }\n    parser.stack.global[id] = n;\n};\nBaseMethods.Accent = function (parser, name, accent, stretchy) {\n    var c = parser.ParseArg(name);\n    var def = __assign(__assign({}, ParseUtil_js_1.default.getFontDef(parser)), { accent: true, mathaccent: true });\n    var entity = NodeUtil_js_1.default.createEntity(accent);\n    var moNode = parser.create('token', 'mo', def, entity);\n    var mml = moNode;\n    NodeUtil_js_1.default.setAttribute(mml, 'stretchy', stretchy ? true : false);\n    var mo = (NodeUtil_js_1.default.isEmbellished(c) ? NodeUtil_js_1.default.getCoreMO(c) : c);\n    if (NodeUtil_js_1.default.isType(mo, 'mo') || NodeUtil_js_1.default.getProperty(mo, 'movablelimits')) {\n        NodeUtil_js_1.default.setProperties(mo, { 'movablelimits': false });\n    }\n    var muoNode = parser.create('node', 'munderover');\n    NodeUtil_js_1.default.setChild(muoNode, 0, c);\n    NodeUtil_js_1.default.setChild(muoNode, 1, null);\n    NodeUtil_js_1.default.setChild(muoNode, 2, mml);\n    var texAtom = parser.create('node', 'TeXAtom', [muoNode]);\n    parser.Push(texAtom);\n};\nBaseMethods.UnderOver = function (parser, name, c, stack) {\n    var entity = NodeUtil_js_1.default.createEntity(c);\n    var mo = parser.create('token', 'mo', { stretchy: true, accent: true }, entity);\n    var pos = (name.charAt(1) === 'o' ? 'over' : 'under');\n    var base = parser.ParseArg(name);\n    parser.Push(ParseUtil_js_1.default.underOver(parser, base, mo, pos, stack));\n};\nBaseMethods.Overset = function (parser, name) {\n    var top = parser.ParseArg(name);\n    var base = parser.ParseArg(name);\n    ParseUtil_js_1.default.checkMovableLimits(base);\n    if (top.isKind('mo')) {\n        NodeUtil_js_1.default.setAttribute(top, 'accent', false);\n    }\n    var node = parser.create('node', 'mover', [base, top]);\n    parser.Push(node);\n};\nBaseMethods.Underset = function (parser, name) {\n    var bot = parser.ParseArg(name);\n    var base = parser.ParseArg(name);\n    ParseUtil_js_1.default.checkMovableLimits(base);\n    if (bot.isKind('mo')) {\n        NodeUtil_js_1.default.setAttribute(bot, 'accent', false);\n    }\n    var node = parser.create('node', 'munder', [base, bot], { accentunder: false });\n    parser.Push(node);\n};\nBaseMethods.Overunderset = function (parser, name) {\n    var top = parser.ParseArg(name);\n    var bot = parser.ParseArg(name);\n    var base = parser.ParseArg(name);\n    ParseUtil_js_1.default.checkMovableLimits(base);\n    if (top.isKind('mo')) {\n        NodeUtil_js_1.default.setAttribute(top, 'accent', false);\n    }\n    if (bot.isKind('mo')) {\n        NodeUtil_js_1.default.setAttribute(bot, 'accent', false);\n    }\n    var node = parser.create('node', 'munderover', [base, bot, top], { accent: false, accentunder: false });\n    parser.Push(node);\n};\nBaseMethods.TeXAtom = function (parser, name, mclass) {\n    var def = { texClass: mclass };\n    var mml;\n    var node;\n    var parsed;\n    if (mclass === MmlNode_js_1.TEXCLASS.OP) {\n        def['movesupsub'] = def['movablelimits'] = true;\n        var arg = parser.GetArgument(name);\n        var match = arg.match(/^\\s*\\\\rm\\s+([a-zA-Z0-9 ]+)$/);\n        if (match) {\n            def['mathvariant'] = TexConstants_js_1.TexConstant.Variant.NORMAL;\n            node = parser.create('token', 'mi', def, match[1]);\n        }\n        else {\n            parsed = new TexParser_js_1.default(arg, parser.stack.env, parser.configuration).mml();\n            node = parser.create('node', 'TeXAtom', [parsed], def);\n        }\n        mml = parser.itemFactory.create('fn', node);\n    }\n    else {\n        parsed = parser.ParseArg(name);\n        mml = parser.create('node', 'TeXAtom', [parsed], def);\n    }\n    parser.Push(mml);\n};\nBaseMethods.MmlToken = function (parser, name) {\n    var kind = parser.GetArgument(name);\n    var attr = parser.GetBrackets(name, '').replace(/^\\s+/, '');\n    var text = parser.GetArgument(name);\n    var def = {};\n    var keep = [];\n    var node;\n    try {\n        node = parser.create('node', kind);\n    }\n    catch (e) {\n        node = null;\n    }\n    if (!node || !node.isToken) {\n        throw new TexError_js_1.default('NotMathMLToken', '%1 is not a token element', kind);\n    }\n    while (attr !== '') {\n        var match = attr.match(/^([a-z]+)\\s*=\\s*('[^']*'|\"[^\"]*\"|[^ ,]*)\\s*,?\\s*/i);\n        if (!match) {\n            throw new TexError_js_1.default('InvalidMathMLAttr', 'Invalid MathML attribute: %1', attr);\n        }\n        if (!node.attributes.hasDefault(match[1]) && !MmlTokenAllow[match[1]]) {\n            throw new TexError_js_1.default('UnknownAttrForElement', '%1 is not a recognized attribute for %2', match[1], kind);\n        }\n        var value = ParseUtil_js_1.default.MmlFilterAttribute(parser, match[1], match[2].replace(/^(['\"])(.*)\\1$/, '$2'));\n        if (value) {\n            if (value.toLowerCase() === 'true') {\n                value = true;\n            }\n            else if (value.toLowerCase() === 'false') {\n                value = false;\n            }\n            def[match[1]] = value;\n            keep.push(match[1]);\n        }\n        attr = attr.substr(match[0].length);\n    }\n    if (keep.length) {\n        def['mjx-keep-attrs'] = keep.join(' ');\n    }\n    var textNode = parser.create('text', text);\n    node.appendChild(textNode);\n    NodeUtil_js_1.default.setProperties(node, def);\n    parser.Push(node);\n};\nBaseMethods.Strut = function (parser, _name) {\n    var row = parser.create('node', 'mrow');\n    var padded = parser.create('node', 'mpadded', [row], { height: '8.6pt', depth: '3pt', width: 0 });\n    parser.Push(padded);\n};\nBaseMethods.Phantom = function (parser, name, v, h) {\n    var box = parser.create('node', 'mphantom', [parser.ParseArg(name)]);\n    if (v || h) {\n        box = parser.create('node', 'mpadded', [box]);\n        if (h) {\n            NodeUtil_js_1.default.setAttribute(box, 'height', 0);\n            NodeUtil_js_1.default.setAttribute(box, 'depth', 0);\n        }\n        if (v) {\n            NodeUtil_js_1.default.setAttribute(box, 'width', 0);\n        }\n    }\n    var atom = parser.create('node', 'TeXAtom', [box]);\n    parser.Push(atom);\n};\nBaseMethods.Smash = function (parser, name) {\n    var bt = ParseUtil_js_1.default.trimSpaces(parser.GetBrackets(name, ''));\n    var smash = parser.create('node', 'mpadded', [parser.ParseArg(name)]);\n    switch (bt) {\n        case 'b':\n            NodeUtil_js_1.default.setAttribute(smash, 'depth', 0);\n            break;\n        case 't':\n            NodeUtil_js_1.default.setAttribute(smash, 'height', 0);\n            break;\n        default:\n            NodeUtil_js_1.default.setAttribute(smash, 'height', 0);\n            NodeUtil_js_1.default.setAttribute(smash, 'depth', 0);\n    }\n    var atom = parser.create('node', 'TeXAtom', [smash]);\n    parser.Push(atom);\n};\nBaseMethods.Lap = function (parser, name) {\n    var mml = parser.create('node', 'mpadded', [parser.ParseArg(name)], { width: 0 });\n    if (name === '\\\\llap') {\n        NodeUtil_js_1.default.setAttribute(mml, 'lspace', '-1width');\n    }\n    var atom = parser.create('node', 'TeXAtom', [mml]);\n    parser.Push(atom);\n};\nBaseMethods.RaiseLower = function (parser, name) {\n    var h = parser.GetDimen(name);\n    var item = parser.itemFactory.create('position').setProperties({ name: parser.currentCS, move: 'vertical' });\n    if (h.charAt(0) === '-') {\n        h = h.slice(1);\n        name = name.substr(1) === 'raise' ? '\\\\lower' : '\\\\raise';\n    }\n    if (name === '\\\\lower') {\n        item.setProperty('dh', '-' + h);\n        item.setProperty('dd', '+' + h);\n    }\n    else {\n        item.setProperty('dh', '+' + h);\n        item.setProperty('dd', '-' + h);\n    }\n    parser.Push(item);\n};\nBaseMethods.MoveLeftRight = function (parser, name) {\n    var h = parser.GetDimen(name);\n    var nh = (h.charAt(0) === '-' ? h.slice(1) : '-' + h);\n    if (name === '\\\\moveleft') {\n        var tmp = h;\n        h = nh;\n        nh = tmp;\n    }\n    parser.Push(parser.itemFactory.create('position').setProperties({\n        name: parser.currentCS, move: 'horizontal',\n        left: parser.create('node', 'mspace', [], { width: h }),\n        right: parser.create('node', 'mspace', [], { width: nh })\n    }));\n};\nBaseMethods.Hskip = function (parser, name) {\n    var node = parser.create('node', 'mspace', [], { width: parser.GetDimen(name) });\n    parser.Push(node);\n};\nBaseMethods.Nonscript = function (parser, _name) {\n    parser.Push(parser.itemFactory.create('nonscript'));\n};\nBaseMethods.Rule = function (parser, name, style) {\n    var w = parser.GetDimen(name), h = parser.GetDimen(name), d = parser.GetDimen(name);\n    var def = { width: w, height: h, depth: d };\n    if (style !== 'blank') {\n        def['mathbackground'] = (parser.stack.env['color'] || 'black');\n    }\n    var node = parser.create('node', 'mspace', [], def);\n    parser.Push(node);\n};\nBaseMethods.rule = function (parser, name) {\n    var v = parser.GetBrackets(name), w = parser.GetDimen(name), h = parser.GetDimen(name);\n    var mml = parser.create('node', 'mspace', [], {\n        width: w, height: h,\n        mathbackground: (parser.stack.env['color'] || 'black')\n    });\n    if (v) {\n        mml = parser.create('node', 'mpadded', [mml], { voffset: v });\n        if (v.match(/^\\-/)) {\n            NodeUtil_js_1.default.setAttribute(mml, 'height', v);\n            NodeUtil_js_1.default.setAttribute(mml, 'depth', '+' + v.substr(1));\n        }\n        else {\n            NodeUtil_js_1.default.setAttribute(mml, 'height', '+' + v);\n        }\n    }\n    parser.Push(mml);\n};\nBaseMethods.MakeBig = function (parser, name, mclass, size) {\n    size *= P_HEIGHT;\n    var sizeStr = String(size).replace(/(\\.\\d\\d\\d).+/, '$1') + 'em';\n    var delim = parser.GetDelimiter(name, true);\n    var mo = parser.create('token', 'mo', {\n        minsize: sizeStr, maxsize: sizeStr,\n        fence: true, stretchy: true, symmetric: true\n    }, delim);\n    var node = parser.create('node', 'TeXAtom', [mo], { texClass: mclass });\n    parser.Push(node);\n};\nBaseMethods.BuildRel = function (parser, name) {\n    var top = parser.ParseUpTo(name, '\\\\over');\n    var bot = parser.ParseArg(name);\n    var node = parser.create('node', 'munderover');\n    NodeUtil_js_1.default.setChild(node, 0, bot);\n    NodeUtil_js_1.default.setChild(node, 1, null);\n    NodeUtil_js_1.default.setChild(node, 2, top);\n    var atom = parser.create('node', 'TeXAtom', [node], { texClass: MmlNode_js_1.TEXCLASS.REL });\n    parser.Push(atom);\n};\nBaseMethods.HBox = function (parser, name, style, font) {\n    parser.PushAll(ParseUtil_js_1.default.internalMath(parser, parser.GetArgument(name), style, font));\n};\nBaseMethods.FBox = function (parser, name) {\n    var internal = ParseUtil_js_1.default.internalMath(parser, parser.GetArgument(name));\n    var node = parser.create('node', 'menclose', internal, { notation: 'box' });\n    parser.Push(node);\n};\nBaseMethods.FrameBox = function (parser, name) {\n    var width = parser.GetBrackets(name);\n    var pos = parser.GetBrackets(name) || 'c';\n    var mml = ParseUtil_js_1.default.internalMath(parser, parser.GetArgument(name));\n    if (width) {\n        mml = [parser.create('node', 'mpadded', mml, {\n                width: width,\n                'data-align': (0, Options_js_1.lookup)(pos, { l: 'left', r: 'right' }, 'center')\n            })];\n    }\n    var node = parser.create('node', 'TeXAtom', [parser.create('node', 'menclose', mml, { notation: 'box' })], { texClass: MmlNode_js_1.TEXCLASS.ORD });\n    parser.Push(node);\n};\nBaseMethods.Not = function (parser, _name) {\n    parser.Push(parser.itemFactory.create('not'));\n};\nBaseMethods.Dots = function (parser, _name) {\n    var ldotsEntity = NodeUtil_js_1.default.createEntity('2026');\n    var cdotsEntity = NodeUtil_js_1.default.createEntity('22EF');\n    var ldots = parser.create('token', 'mo', { stretchy: false }, ldotsEntity);\n    var cdots = parser.create('token', 'mo', { stretchy: false }, cdotsEntity);\n    parser.Push(parser.itemFactory.create('dots').setProperties({\n        ldots: ldots,\n        cdots: cdots\n    }));\n};\nBaseMethods.Matrix = function (parser, _name, open, close, align, spacing, vspacing, style, cases, numbered) {\n    var c = parser.GetNext();\n    if (c === '') {\n        throw new TexError_js_1.default('MissingArgFor', 'Missing argument for %1', parser.currentCS);\n    }\n    if (c === '{') {\n        parser.i++;\n    }\n    else {\n        parser.string = c + '}' + parser.string.slice(parser.i + 1);\n        parser.i = 0;\n    }\n    var array = parser.itemFactory.create('array').setProperty('requireClose', true);\n    array.arraydef = {\n        rowspacing: (vspacing || '4pt'),\n        columnspacing: (spacing || '1em')\n    };\n    if (cases) {\n        array.setProperty('isCases', true);\n    }\n    if (numbered) {\n        array.setProperty('isNumbered', true);\n        array.arraydef.side = numbered;\n    }\n    if (open || close) {\n        array.setProperty('open', open);\n        array.setProperty('close', close);\n    }\n    if (style === 'D') {\n        array.arraydef.displaystyle = true;\n    }\n    if (align != null) {\n        array.arraydef.columnalign = align;\n    }\n    parser.Push(array);\n};\nBaseMethods.Entry = function (parser, name) {\n    parser.Push(parser.itemFactory.create('cell').setProperties({ isEntry: true, name: name }));\n    var top = parser.stack.Top();\n    var env = top.getProperty('casesEnv');\n    var cases = top.getProperty('isCases');\n    if (!cases && !env)\n        return;\n    var str = parser.string;\n    var braces = 0, close = -1, i = parser.i, m = str.length;\n    var end = (env ? new RegExp(\"^\\\\\\\\end\\\\s*\\\\{\".concat(env.replace(/\\*/, '\\\\*'), \"\\\\}\")) : null);\n    while (i < m) {\n        var c = str.charAt(i);\n        if (c === '{') {\n            braces++;\n            i++;\n        }\n        else if (c === '}') {\n            if (braces === 0) {\n                m = 0;\n            }\n            else {\n                braces--;\n                if (braces === 0 && close < 0) {\n                    close = i - parser.i;\n                }\n                i++;\n            }\n        }\n        else if (c === '&' && braces === 0) {\n            throw new TexError_js_1.default('ExtraAlignTab', 'Extra alignment tab in \\\\cases text');\n        }\n        else if (c === '\\\\') {\n            var rest = str.substr(i);\n            if (rest.match(/^((\\\\cr)[^a-zA-Z]|\\\\\\\\)/) || (end && rest.match(end))) {\n                m = 0;\n            }\n            else {\n                i += 2;\n            }\n        }\n        else {\n            i++;\n        }\n    }\n    var text = str.substr(parser.i, i - parser.i);\n    if (!text.match(/^\\s*\\\\text[^a-zA-Z]/) || close !== text.replace(/\\s+$/, '').length - 1) {\n        var internal = ParseUtil_js_1.default.internalMath(parser, ParseUtil_js_1.default.trimSpaces(text), 0);\n        parser.PushAll(internal);\n        parser.i = i;\n    }\n};\nBaseMethods.Cr = function (parser, name) {\n    parser.Push(parser.itemFactory.create('cell').setProperties({ isCR: true, name: name }));\n};\nBaseMethods.CrLaTeX = function (parser, name, nobrackets) {\n    if (nobrackets === void 0) { nobrackets = false; }\n    var n;\n    if (!nobrackets) {\n        if (parser.string.charAt(parser.i) === '*') {\n            parser.i++;\n        }\n        if (parser.string.charAt(parser.i) === '[') {\n            var dim = parser.GetBrackets(name, '');\n            var _a = __read(ParseUtil_js_1.default.matchDimen(dim), 2), value = _a[0], unit = _a[1];\n            if (dim && !value) {\n                throw new TexError_js_1.default('BracketMustBeDimension', 'Bracket argument to %1 must be a dimension', parser.currentCS);\n            }\n            n = value + unit;\n        }\n    }\n    parser.Push(parser.itemFactory.create('cell').setProperties({ isCR: true, name: name, linebreak: true }));\n    var top = parser.stack.Top();\n    var node;\n    if (top instanceof sitem.ArrayItem) {\n        if (n) {\n            top.addRowSpacing(n);\n        }\n    }\n    else {\n        if (n) {\n            node = parser.create('node', 'mspace', [], { depth: n });\n            parser.Push(node);\n        }\n        node = parser.create('node', 'mspace', [], { linebreak: TexConstants_js_1.TexConstant.LineBreak.NEWLINE });\n        parser.Push(node);\n    }\n};\nBaseMethods.HLine = function (parser, _name, style) {\n    if (style == null) {\n        style = 'solid';\n    }\n    var top = parser.stack.Top();\n    if (!(top instanceof sitem.ArrayItem) || top.Size()) {\n        throw new TexError_js_1.default('Misplaced', 'Misplaced %1', parser.currentCS);\n    }\n    if (!top.table.length) {\n        top.frame.push('top');\n    }\n    else {\n        var lines = (top.arraydef['rowlines'] ? top.arraydef['rowlines'].split(/ /) : []);\n        while (lines.length < top.table.length) {\n            lines.push('none');\n        }\n        lines[top.table.length - 1] = style;\n        top.arraydef['rowlines'] = lines.join(' ');\n    }\n};\nBaseMethods.HFill = function (parser, _name) {\n    var top = parser.stack.Top();\n    if (top instanceof sitem.ArrayItem) {\n        top.hfill.push(top.Size());\n    }\n    else {\n        throw new TexError_js_1.default('UnsupportedHFill', 'Unsupported use of %1', parser.currentCS);\n    }\n};\nBaseMethods.BeginEnd = function (parser, name) {\n    var env = parser.GetArgument(name);\n    if (env.match(/\\\\/i)) {\n        throw new TexError_js_1.default('InvalidEnv', 'Invalid environment name \\'%1\\'', env);\n    }\n    var macro = parser.configuration.handlers.get('environment').lookup(env);\n    if (macro && name === '\\\\end') {\n        if (!macro.args[0]) {\n            var mml = parser.itemFactory.create('end').setProperty('name', env);\n            parser.Push(mml);\n            return;\n        }\n        parser.stack.env['closing'] = env;\n    }\n    ParseUtil_js_1.default.checkMaxMacros(parser, false);\n    parser.parse('environment', [parser, env]);\n};\nBaseMethods.Array = function (parser, begin, open, close, align, spacing, vspacing, style, raggedHeight) {\n    if (!align) {\n        align = parser.GetArgument('\\\\begin{' + begin.getName() + '}');\n    }\n    var lines = ('c' + align).replace(/[^clr|:]/g, '').replace(/[^|:]([|:])+/g, '$1');\n    align = align.replace(/[^clr]/g, '').split('').join(' ');\n    align = align.replace(/l/g, 'left').replace(/r/g, 'right').replace(/c/g, 'center');\n    var array = parser.itemFactory.create('array');\n    array.arraydef = {\n        columnalign: align,\n        columnspacing: (spacing || '1em'),\n        rowspacing: (vspacing || '4pt')\n    };\n    if (lines.match(/[|:]/)) {\n        if (lines.charAt(0).match(/[|:]/)) {\n            array.frame.push('left');\n            array.dashed = lines.charAt(0) === ':';\n        }\n        if (lines.charAt(lines.length - 1).match(/[|:]/)) {\n            array.frame.push('right');\n        }\n        lines = lines.substr(1, lines.length - 2);\n        array.arraydef.columnlines =\n            lines.split('').join(' ').replace(/[^|: ]/g, 'none').replace(/\\|/g, 'solid').replace(/:/g, 'dashed');\n    }\n    if (open) {\n        array.setProperty('open', parser.convertDelimiter(open));\n    }\n    if (close) {\n        array.setProperty('close', parser.convertDelimiter(close));\n    }\n    if ((style || '').charAt(1) === '\\'') {\n        array.arraydef['data-cramped'] = true;\n        style = style.charAt(0);\n    }\n    if (style === 'D') {\n        array.arraydef['displaystyle'] = true;\n    }\n    else if (style) {\n        array.arraydef['displaystyle'] = false;\n    }\n    if (style === 'S') {\n        array.arraydef['scriptlevel'] = 1;\n    }\n    if (raggedHeight) {\n        array.arraydef['useHeight'] = false;\n    }\n    parser.Push(begin);\n    return array;\n};\nBaseMethods.AlignedArray = function (parser, begin) {\n    var align = parser.GetBrackets('\\\\begin{' + begin.getName() + '}');\n    var item = BaseMethods.Array(parser, begin);\n    return ParseUtil_js_1.default.setArrayAlign(item, align);\n};\nBaseMethods.Equation = function (parser, begin, numbered) {\n    parser.Push(begin);\n    ParseUtil_js_1.default.checkEqnEnv(parser);\n    return parser.itemFactory.create('equation', numbered).\n        setProperty('name', begin.getName());\n};\nBaseMethods.EqnArray = function (parser, begin, numbered, taggable, align, spacing) {\n    parser.Push(begin);\n    if (taggable) {\n        ParseUtil_js_1.default.checkEqnEnv(parser);\n    }\n    align = align.replace(/[^clr]/g, '').split('').join(' ');\n    align = align.replace(/l/g, 'left').replace(/r/g, 'right').replace(/c/g, 'center');\n    var newItem = parser.itemFactory.create('eqnarray', begin.getName(), numbered, taggable, parser.stack.global);\n    newItem.arraydef = {\n        displaystyle: true,\n        columnalign: align,\n        columnspacing: (spacing || '1em'),\n        rowspacing: '3pt',\n        side: parser.options['tagSide'],\n        minlabelspacing: parser.options['tagIndent']\n    };\n    return newItem;\n};\nBaseMethods.HandleNoTag = function (parser, _name) {\n    parser.tags.notag();\n};\nBaseMethods.HandleLabel = function (parser, name) {\n    var label = parser.GetArgument(name);\n    if (label === '') {\n        return;\n    }\n    if (!parser.tags.refUpdate) {\n        if (parser.tags.label) {\n            throw new TexError_js_1.default('MultipleCommand', 'Multiple %1', parser.currentCS);\n        }\n        parser.tags.label = label;\n        if ((parser.tags.allLabels[label] || parser.tags.labels[label]) && !parser.options['ignoreDuplicateLabels']) {\n            throw new TexError_js_1.default('MultipleLabel', 'Label \\'%1\\' multiply defined', label);\n        }\n        parser.tags.labels[label] = new Tags_js_1.Label();\n    }\n};\nBaseMethods.HandleRef = function (parser, name, eqref) {\n    var label = parser.GetArgument(name);\n    var ref = parser.tags.allLabels[label] || parser.tags.labels[label];\n    if (!ref) {\n        if (!parser.tags.refUpdate) {\n            parser.tags.redo = true;\n        }\n        ref = new Tags_js_1.Label();\n    }\n    var tag = ref.tag;\n    if (eqref) {\n        tag = parser.tags.formatTag(tag);\n    }\n    var node = parser.create('node', 'mrow', ParseUtil_js_1.default.internalMath(parser, tag), {\n        href: parser.tags.formatUrl(ref.id, parser.options.baseURL), 'class': 'MathJax_ref'\n    });\n    parser.Push(node);\n};\nBaseMethods.Macro = function (parser, name, macro, argcount, def) {\n    if (argcount) {\n        var args = [];\n        if (def != null) {\n            var optional = parser.GetBrackets(name);\n            args.push(optional == null ? def : optional);\n        }\n        for (var i = args.length; i < argcount; i++) {\n            args.push(parser.GetArgument(name));\n        }\n        macro = ParseUtil_js_1.default.substituteArgs(parser, args, macro);\n    }\n    parser.string = ParseUtil_js_1.default.addArgs(parser, macro, parser.string.slice(parser.i));\n    parser.i = 0;\n    ParseUtil_js_1.default.checkMaxMacros(parser);\n};\nBaseMethods.MathChoice = function (parser, name) {\n    var D = parser.ParseArg(name);\n    var T = parser.ParseArg(name);\n    var S = parser.ParseArg(name);\n    var SS = parser.ParseArg(name);\n    parser.Push(parser.create('node', 'MathChoice', [D, T, S, SS]));\n};\nexports.default = BaseMethods;\n//# sourceMappingURL=BaseMethods.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.px = exports.emRounded = exports.em = exports.percent = exports.length2em = exports.MATHSPACE = exports.RELUNITS = exports.UNITS = exports.BIGDIMEN = void 0;\nexports.BIGDIMEN = 1000000;\nexports.UNITS = {\n    px: 1,\n    'in': 96,\n    cm: 96 / 2.54,\n    mm: 96 / 25.4\n};\nexports.RELUNITS = {\n    em: 1,\n    ex: .431,\n    pt: 1 / 10,\n    pc: 12 / 10,\n    mu: 1 / 18\n};\nexports.MATHSPACE = {\n    veryverythinmathspace: 1 / 18,\n    verythinmathspace: 2 / 18,\n    thinmathspace: 3 / 18,\n    mediummathspace: 4 / 18,\n    thickmathspace: 5 / 18,\n    verythickmathspace: 6 / 18,\n    veryverythickmathspace: 7 / 18,\n    negativeveryverythinmathspace: -1 / 18,\n    negativeverythinmathspace: -2 / 18,\n    negativethinmathspace: -3 / 18,\n    negativemediummathspace: -4 / 18,\n    negativethickmathspace: -5 / 18,\n    negativeverythickmathspace: -6 / 18,\n    negativeveryverythickmathspace: -7 / 18,\n    thin: .04,\n    medium: .06,\n    thick: .1,\n    normal: 1,\n    big: 2,\n    small: 1 / Math.sqrt(2),\n    infinity: exports.BIGDIMEN\n};\nfunction length2em(length, size, scale, em) {\n    if (size === void 0) { size = 0; }\n    if (scale === void 0) { scale = 1; }\n    if (em === void 0) { em = 16; }\n    if (typeof length !== 'string') {\n        length = String(length);\n    }\n    if (length === '' || length == null) {\n        return size;\n    }\n    if (exports.MATHSPACE[length]) {\n        return exports.MATHSPACE[length];\n    }\n    var match = length.match(/^\\s*([-+]?(?:\\.\\d+|\\d+(?:\\.\\d*)?))?(pt|em|ex|mu|px|pc|in|mm|cm|%)?/);\n    if (!match) {\n        return size;\n    }\n    var m = parseFloat(match[1] || '1'), unit = match[2];\n    if (exports.UNITS.hasOwnProperty(unit)) {\n        return m * exports.UNITS[unit] / em / scale;\n    }\n    if (exports.RELUNITS.hasOwnProperty(unit)) {\n        return m * exports.RELUNITS[unit];\n    }\n    if (unit === '%') {\n        return m / 100 * size;\n    }\n    return m * size;\n}\nexports.length2em = length2em;\nfunction percent(m) {\n    return (100 * m).toFixed(1).replace(/\\.?0+$/, '') + '%';\n}\nexports.percent = percent;\nfunction em(m) {\n    if (Math.abs(m) < .001)\n        return '0';\n    return (m.toFixed(3).replace(/\\.?0+$/, '')) + 'em';\n}\nexports.em = em;\nfunction emRounded(m, em) {\n    if (em === void 0) { em = 16; }\n    m = (Math.round(m * em) + .05) / em;\n    if (Math.abs(m) < .001)\n        return '0em';\n    return m.toFixed(3).replace(/\\.?0+$/, '') + 'em';\n}\nexports.emRounded = emRounded;\nfunction px(m, M, em) {\n    if (M === void 0) { M = -exports.BIGDIMEN; }\n    if (em === void 0) { em = 16; }\n    m *= em;\n    if (M && m < M)\n        m = M;\n    if (Math.abs(m) < .1)\n        return '0';\n    return m.toFixed(1).replace(/\\.0$/, '') + 'px';\n}\nexports.px = px;\n//# sourceMappingURL=lengths.js.map"], "names": [], "sourceRoot": ""}