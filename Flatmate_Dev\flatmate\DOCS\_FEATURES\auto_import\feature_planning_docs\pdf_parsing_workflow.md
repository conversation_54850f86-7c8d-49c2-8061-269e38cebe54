# PDF Statement Parsing Workflow

This document outlines the iterative process for developing a new PDF statement handler, from initial analysis to final implementation.

## Phase 1: Analysis & Prototyping (Manual/Jupyter Notebook)

The goal of this phase is to understand the PDF's structure and determine the best extraction strategy. This should be done in a Jupyter Notebook or a simple Python script for rapid experimentation.

**Step 1: Manual Inspection**
- Open the target PDF statement.
- Identify key anchors: What text is always in the same place? (e.g., "Account Number", "Statement Period", table headers like "Date", "Description", "Amount").
- Observe the table structure: Are there visible lines (grid)? Or is it defined by whitespace? Does it span multiple pages?

**Step 2: Prototype with `Camelot` (for simple tables)**
- Attempt to extract tables using `camelot.read_pdf()`, trying both `flavor='lattice'` (for grids) and `flavor='stream'` (for whitespace).
- Review the extracted dataframes. If the output is clean and accurate, `Camelot` may be sufficient for this statement. Note the page numbers and table areas that work.

**Step 3: Prototype with `pdfplumber` (for complex tables)**
- Open the PDF with `pdfplumber.open()`.
- Use `page.extract_text()` to find the coordinates of your anchor text from Step 1.
- Use `page.extract_tables()` to see if it can auto-detect the table.
- If `extract_tables` fails, use `page.filter()` to isolate text within the table's bounding box (found using your anchors) and `page.lines`/`page.rects` to understand its structure.
- The goal is to write a script that reliably returns a clean list of transaction data (as a list of lists or a Pandas DataFrame).

**Step 4: Select Tool and Finalise Logic**
- Based on the results, decide whether `Camelot` or `pdfplumber` is the right tool for this specific statement.
- Finalise the prototyping script so it reliably extracts the account number, statement period, and a clean transaction table from the sample PDF.

## Phase 2: Handler Implementation

**Step 5: Create New Statement Handler**
- Create a new handler file (e.g., `kiwibank_pdf_handler.py`).
- Create a new class inheriting from `_base_statement_handler.py` (or a future `_base_pdf_handler.py`).

**Step 6: Implement `_format_df`**
- Port the logic from your prototyping script (Step 4) into the handler's `_format_df` or equivalent internal method.
- This method should take the file path, perform the PDF extraction, and return a standardised DataFrame that matches the application's required columns (e.g., `Date`, `Description`, `Amount`).

**Step 7: Implement `can_handle_file`**
- This method is crucial for uniquely identifying this specific PDF.
- It should open the PDF (using `pdfplumber` for its lightweight text extraction) and check for the presence of several unique anchor strings at expected locations (e.g., "Kiwibank" in the header and "Transaction History" on page 2).
- This ensures the system correctly routes this PDF to *only* this handler.

## Phase 3: Testing

**Step 8: Write Tests**
- Add the new PDF to the test assets.
- Add a new test case in `test_real_csvs.py` (or a new test file) that runs the `dw_director` against the new PDF and asserts that the correct number of transactions are parsed successfully.
