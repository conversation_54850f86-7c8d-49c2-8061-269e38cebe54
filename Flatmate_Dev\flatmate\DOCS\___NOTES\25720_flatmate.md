---
Sunday, July 20, 2025 @ 04:38:43 PM
---

hitlist 

1 search logic in cat

2 clock logic in table view info bar

3 modules open at last used 

---
# 1 
---

# we need to rationalise this workspace
- I have a list of things to address:

## the search column selector should default to details
- `all visible columns` should be called simply `all visible` 
- `all searchable` is another matter requiring some thought but this was discussed in the recent debug report 
### current behaviour:
Apply button does not appear, either with `all cols` or `(` system gets laggy suggesting issue with dispatch logic and the filtering logic which should ignore logical operators 

**logical operators should be defined in one place** they should include synonymns
as defined in the documentaion, and any Operator charactor should be ignored by filtering until either - the logical operator has been ruledout: as in "OR...PHEUS " (complex logic) 
or more simply - the apply button should just be triggered and live filtering suspended.

Note: this was working in an earlier implementation ! check archives in table view folder 
Arc. question - where should this be defined !?
currently all table view utility files are together in one folder. 

## show/hide (eye icon)
- drop down should organise by a sensible 12 lines per column
- we need to refine and define it's list and where that should be defined
  - standards/Collumns ?
 - in categories and set by caller !?
*dispatched to augment*
---
...refactoring....
---
test notes: `-` alone doesnt need to signify a complex query - past tests have shown the basic search parser handles tea -coffee - biscuits fine 
the character should be a live filter ignore group though when first character of new search term 
live_filter_ignore=true check the local codebase for naming convention...
(would a pydantic class simplify things here?)

- I'm questioning whether after a term has been evaluated it can be applied automatically ... *this is speculative and should be relagted to a discussion document)

We should apply the more obvious code fixes,  tidy up the features table view workspace and record our insights
and update our discussion document and hit list ...

Notes: letter based logical operations should be in capitals to avoid confusion with terms
or should not function like OR or is a search term OR is an operator and needs evaluation
(as in ORPHEUS)
----
Sunday, July 20, 2025 @ 08:12:44 PM
---

Monday, July 21, 2025 @ 02:35:02 AM
# Auto Import  
test notes:
on running the app I navigated to categorise
the new opton is available 
on selection and clicking select - the pop up stand in gui opened 
- i couldnt browse to set the folder 

- nothing seemed functional
i hit okay and the info bar had a message saying that I needed to retart the app for the change to take effect

 I have restarted the app

 the Source Files option displays `Set auto import folder...` 

 save location displaya `Same as source files...`

info bar says `save location: None`


- (there is a double up on infobars ud_data is obviously still creating its old prototype vversion)

center panel has an early stand in welcome message and is otherwise blank

![1753023199149](image/25720_flatmate/1753023199149.png)

I can see no new folders in downloads 

I'm unsure how to proceed 

the gui implementation while approaching functional now, seems illogical in the flow for the user ....

The question is what should it do? what would make sense? we have an unused right panel 
which was intended for context specific options and settings

We have a huge mostly empty center panel

we need to think about some good gui design principles
at the moment the update database chek box is a hacked in place label and check box that doesnt use the app base widgets 

we have numerous optins we can show hide elements, swap them out 
put the table view in the center - such that switching modules simply means swapping out panels for the the viewer
which would be fine - making the nav bar / side bar simply a task context switxher as far as the user is concerned... we have  alot of room to play with the question is ux design and logical flow

for now the priority is probably to get the auto import logic working and some feed back in the center panel 

i should export these test notes to the feature appropriate auto import feature design dir 
But where exactly the raises a question about out workflow protocls foe this review debug and refine part of the new feature process 




