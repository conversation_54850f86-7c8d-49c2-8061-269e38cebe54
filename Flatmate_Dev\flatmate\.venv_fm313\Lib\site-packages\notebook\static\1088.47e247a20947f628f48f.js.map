{"version": 3, "file": "1088.47e247a20947f628f48f.js?v=47e247a20947f628f48f", "mappings": ";;;;;;;;;;AAAA;AACA;AACA,0BAA0B,sBAAsB;AAChD,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,IAAI;AAC/E,iCAAiC,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,IAAI;AAC3D,4BAA4B,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,IAAI;AACrD,mCAAmC,EAAE;AACrC;AACA,kCAAkC,EAAE;AACpC;AACA,uCAAuC,EAAE;AACzC,yCAAyC,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA,6BAA6B,SAAS;AACtC;AACA;AACA;AACA;AACA;AACA,qCAAqC,eAAe;AACpD,2BAA2B,kCAAkC;AACtD;AACP;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/q.js"], "sourcesContent": ["var curPunc,\n    keywords=buildRE([\"abs\",\"acos\",\"aj\",\"aj0\",\"all\",\"and\",\"any\",\"asc\",\"asin\",\"asof\",\"atan\",\"attr\",\"avg\",\"avgs\",\"bin\",\"by\",\"ceiling\",\"cols\",\"cor\",\"cos\",\"count\",\"cov\",\"cross\",\"csv\",\"cut\",\"delete\",\"deltas\",\"desc\",\"dev\",\"differ\",\"distinct\",\"div\",\"do\",\"each\",\"ej\",\"enlist\",\"eval\",\"except\",\"exec\",\"exit\",\"exp\",\"fby\",\"fills\",\"first\",\"fkeys\",\"flip\",\"floor\",\"from\",\"get\",\"getenv\",\"group\",\"gtime\",\"hclose\",\"hcount\",\"hdel\",\"hopen\",\"hsym\",\"iasc\",\"idesc\",\"if\",\"ij\",\"in\",\"insert\",\"inter\",\"inv\",\"key\",\"keys\",\"last\",\"like\",\"list\",\"lj\",\"load\",\"log\",\"lower\",\"lsq\",\"ltime\",\"ltrim\",\"mavg\",\"max\",\"maxs\",\"mcount\",\"md5\",\"mdev\",\"med\",\"meta\",\"min\",\"mins\",\"mmax\",\"mmin\",\"mmu\",\"mod\",\"msum\",\"neg\",\"next\",\"not\",\"null\",\"or\",\"over\",\"parse\",\"peach\",\"pj\",\"plist\",\"prd\",\"prds\",\"prev\",\"prior\",\"rand\",\"rank\",\"ratios\",\"raze\",\"read0\",\"read1\",\"reciprocal\",\"reverse\",\"rload\",\"rotate\",\"rsave\",\"rtrim\",\"save\",\"scan\",\"select\",\"set\",\"setenv\",\"show\",\"signum\",\"sin\",\"sqrt\",\"ss\",\"ssr\",\"string\",\"sublist\",\"sum\",\"sums\",\"sv\",\"system\",\"tables\",\"tan\",\"til\",\"trim\",\"txf\",\"type\",\"uj\",\"ungroup\",\"union\",\"update\",\"upper\",\"upsert\",\"value\",\"var\",\"view\",\"views\",\"vs\",\"wavg\",\"where\",\"where\",\"while\",\"within\",\"wj\",\"wj1\",\"wsum\",\"xasc\",\"xbar\",\"xcol\",\"xcols\",\"xdesc\",\"xexp\",\"xgroup\",\"xkey\",\"xlog\",\"xprev\",\"xrank\"]),\n    E=/[|/&^!+:\\\\\\-*%$=~#;@><,?_\\'\\\"\\[\\(\\]\\)\\s{}]/;\nfunction buildRE(w){return new RegExp(\"^(\"+w.join(\"|\")+\")$\");}\nfunction tokenBase(stream,state){\n  var sol=stream.sol(),c=stream.next();\n  curPunc=null;\n  if(sol)\n    if(c==\"/\")\n      return(state.tokenize=tokenLineComment)(stream,state);\n  else if(c==\"\\\\\"){\n    if(stream.eol()||/\\s/.test(stream.peek()))\n      return stream.skipToEnd(),/^\\\\\\s*$/.test(stream.current())?(state.tokenize=tokenCommentToEOF)(stream):state.tokenize=tokenBase,\"comment\";\n    else\n      return state.tokenize=tokenBase,\"builtin\";\n  }\n  if(/\\s/.test(c))\n    return stream.peek()==\"/\"?(stream.skipToEnd(),\"comment\"):\"null\";\n  if(c=='\"')\n    return(state.tokenize=tokenString)(stream,state);\n  if(c=='`')\n    return stream.eatWhile(/[A-Za-z\\d_:\\/.]/),\"macroName\";\n  if((\".\"==c&&/\\d/.test(stream.peek()))||/\\d/.test(c)){\n    var t=null;\n    stream.backUp(1);\n    if(stream.match(/^\\d{4}\\.\\d{2}(m|\\.\\d{2}([DT](\\d{2}(:\\d{2}(:\\d{2}(\\.\\d{1,9})?)?)?)?)?)/)\n       || stream.match(/^\\d+D(\\d{2}(:\\d{2}(:\\d{2}(\\.\\d{1,9})?)?)?)/)\n       || stream.match(/^\\d{2}:\\d{2}(:\\d{2}(\\.\\d{1,9})?)?/)\n       || stream.match(/^\\d+[ptuv]{1}/))\n      t=\"temporal\";\n    else if(stream.match(/^0[NwW]{1}/)\n            || stream.match(/^0x[\\da-fA-F]*/)\n            || stream.match(/^[01]+[b]{1}/)\n            || stream.match(/^\\d+[chijn]{1}/)\n            || stream.match(/-?\\d*(\\.\\d*)?(e[+\\-]?\\d+)?(e|f)?/))\n      t=\"number\";\n    return(t&&(!(c=stream.peek())||E.test(c)))?t:(stream.next(),\"error\");\n  }\n  if(/[A-Za-z]|\\./.test(c))\n    return stream.eatWhile(/[A-Za-z._\\d]/),keywords.test(stream.current())?\"keyword\":\"variable\";\n  if(/[|/&^!+:\\\\\\-*%$=~#;@><\\.,?_\\']/.test(c))\n    return null;\n  if(/[{}\\(\\[\\]\\)]/.test(c))\n    return null;\n  return\"error\";\n}\nfunction tokenLineComment(stream,state){\n  return stream.skipToEnd(),/\\/\\s*$/.test(stream.current())?(state.tokenize=tokenBlockComment)(stream,state):(state.tokenize=tokenBase),\"comment\";\n}\nfunction tokenBlockComment(stream,state){\n  var f=stream.sol()&&stream.peek()==\"\\\\\";\n  stream.skipToEnd();\n  if(f&&/^\\\\\\s*$/.test(stream.current()))\n    state.tokenize=tokenBase;\n  return\"comment\";\n}\nfunction tokenCommentToEOF(stream){return stream.skipToEnd(),\"comment\";}\nfunction tokenString(stream,state){\n  var escaped=false,next,end=false;\n  while((next=stream.next())){\n    if(next==\"\\\"\"&&!escaped){end=true;break;}\n    escaped=!escaped&&next==\"\\\\\";\n  }\n  if(end)state.tokenize=tokenBase;\n  return\"string\";\n}\nfunction pushContext(state,type,col){state.context={prev:state.context,indent:state.indent,col:col,type:type};}\nfunction popContext(state){state.indent=state.context.indent;state.context=state.context.prev;}\nexport const q = {\n  name: \"q\",\n  startState:function(){\n    return{tokenize:tokenBase,\n           context:null,\n           indent:0,\n           col:0};\n  },\n  token:function(stream,state){\n    if(stream.sol()){\n      if(state.context&&state.context.align==null)\n        state.context.align=false;\n      state.indent=stream.indentation();\n    }\n    //if (stream.eatSpace()) return null;\n    var style=state.tokenize(stream,state);\n    if(style!=\"comment\"&&state.context&&state.context.align==null&&state.context.type!=\"pattern\"){\n      state.context.align=true;\n    }\n    if(curPunc==\"(\")pushContext(state,\")\",stream.column());\n    else if(curPunc==\"[\")pushContext(state,\"]\",stream.column());\n    else if(curPunc==\"{\")pushContext(state,\"}\",stream.column());\n    else if(/[\\]\\}\\)]/.test(curPunc)){\n      while(state.context&&state.context.type==\"pattern\")popContext(state);\n      if(state.context&&curPunc==state.context.type)popContext(state);\n    }\n    else if(curPunc==\".\"&&state.context&&state.context.type==\"pattern\")popContext(state);\n    else if(/atom|string|variable/.test(style)&&state.context){\n      if(/[\\}\\]]/.test(state.context.type))\n        pushContext(state,\"pattern\",stream.column());\n      else if(state.context.type==\"pattern\"&&!state.context.align){\n        state.context.align=true;\n        state.context.col=stream.column();\n      }\n    }\n    return style;\n  },\n  indent:function(state,textAfter,cx){\n    var firstChar=textAfter&&textAfter.charAt(0);\n    var context=state.context;\n    if(/[\\]\\}]/.test(firstChar))\n      while (context&&context.type==\"pattern\")context=context.prev;\n    var closing=context&&firstChar==context.type;\n    if(!context)\n      return 0;\n    else if(context.type==\"pattern\")\n      return context.col;\n    else if(context.align)\n      return context.col+(closing?0:1);\n    else\n      return context.indent+(closing?0:cx.unit);\n  }\n};\n"], "names": [], "sourceRoot": ""}