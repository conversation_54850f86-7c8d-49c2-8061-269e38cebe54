{"version": 3, "file": "1837.6bbfd9967be58e1325f1.js?v=6bbfd9967be58e1325f1", "mappings": ";;;;;;;;;;AAAA;AACA;AACA,oCAAoC;AACpC;AACA;;AAEA,yBAAyB,IAAI;AAC7B,gCAAgC,IAAI;AACpC;AACA;;AAEA;AACA;AACA,OAAO,IAAI;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,QAAQ;AAC7B;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,uBAAuB,GAAG;AAC1B;AACA,IAAI,0BAA0B,IAAI;AAClC;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,iEAAiE;AACjE;AACA;AACA;AACA;;AAEA;AACA,iEAAiE;AACjE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,wBAAwB,GAAG,GAAG;AAC9B;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,0DAA0D;AAC1D;AACA,0CAA0C,wBAAwB;AAClE,sCAAsC,wBAAwB;AAC9D,uCAAuC,wBAAwB;AAC/D;AACA,uHAAuH;AACvH,oDAAoD,wBAAwB;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B;AAC3B,qBAAqB;AACrB;AACA,IAAI,wBAAwB;AAC5B;AACA;AACA;AACA,yBAAyB,EAAE;AAC3B,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI,4CAA4C,IAAI;AACpD;AACA,4CAA4C;AAC5C;AACA;AACA;AACA,IAAI,+CAA+C,IAAI;AACvD;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC,2BAA2B;AAC3B;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,oBAAoB,mBAAmB,yBAAyB;AAChE,oBAAoB,uBAAuB,QAAQ;AACnD;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/julia.js"], "sourcesContent": ["function wordRegexp(words, end, pre) {\n  if (typeof pre === \"undefined\") pre = \"\";\n  if (typeof end === \"undefined\") { end = \"\\\\b\"; }\n  return new RegExp(\"^\" + pre + \"((\" + words.join(\")|(\") + \"))\" + end);\n}\n\nvar octChar = \"\\\\\\\\[0-7]{1,3}\";\nvar hexChar = \"\\\\\\\\x[A-Fa-f0-9]{1,2}\";\nvar sChar = \"\\\\\\\\[abefnrtv0%?'\\\"\\\\\\\\]\";\nvar uChar = \"([^\\\\u0027\\\\u005C\\\\uD800-\\\\uDFFF]|[\\\\uD800-\\\\uDFFF][\\\\uDC00-\\\\uDFFF])\";\n\nvar asciiOperatorsList = [\n  \"[<>]:\", \"[<>=]=\", \"<<=?\", \">>>?=?\", \"=>\", \"--?>\", \"<--[->]?\", \"\\\\/\\\\/\",\n  \"\\\\.{2,3}\", \"[\\\\.\\\\\\\\%*+\\\\-<>!\\\\/^|&]=?\", \"\\\\?\", \"\\\\$\", \"~\", \":\"\n];\nvar operators = wordRegexp([\n  \"[<>]:\", \"[<>=]=\", \"[!=]==\", \"<<=?\", \">>>?=?\", \"=>?\", \"--?>\", \"<--[->]?\", \"\\\\/\\\\/\",\n  \"[\\\\\\\\%*+\\\\-<>!\\\\/^|&\\\\u00F7\\\\u22BB]=?\", \"\\\\?\", \"\\\\$\", \"~\", \":\",\n  \"\\\\u00D7\", \"\\\\u2208\", \"\\\\u2209\", \"\\\\u220B\", \"\\\\u220C\", \"\\\\u2218\",\n  \"\\\\u221A\", \"\\\\u221B\", \"\\\\u2229\", \"\\\\u222A\", \"\\\\u2260\", \"\\\\u2264\",\n  \"\\\\u2265\", \"\\\\u2286\", \"\\\\u2288\", \"\\\\u228A\", \"\\\\u22C5\",\n  \"\\\\b(in|isa)\\\\b(?!\\.?\\\\()\"\n], \"\");\nvar delimiters = /^[;,()[\\]{}]/;\nvar identifiers = /^[_A-Za-z\\u00A1-\\u2217\\u2219-\\uFFFF][\\w\\u00A1-\\u2217\\u2219-\\uFFFF]*!*/;\n\nvar chars = wordRegexp([octChar, hexChar, sChar, uChar], \"'\");\n\nvar openersList = [\"begin\", \"function\", \"type\", \"struct\", \"immutable\", \"let\",\n                   \"macro\", \"for\", \"while\", \"quote\", \"if\", \"else\", \"elseif\", \"try\",\n                   \"finally\", \"catch\", \"do\"];\n\nvar closersList = [\"end\", \"else\", \"elseif\", \"catch\", \"finally\"];\n\nvar keywordsList = [\"if\", \"else\", \"elseif\", \"while\", \"for\", \"begin\", \"let\",\n                    \"end\", \"do\", \"try\", \"catch\", \"finally\", \"return\", \"break\", \"continue\",\n                    \"global\", \"local\", \"const\", \"export\", \"import\", \"importall\", \"using\",\n                    \"function\", \"where\", \"macro\", \"module\", \"baremodule\", \"struct\", \"type\",\n                    \"mutable\", \"immutable\", \"quote\", \"typealias\", \"abstract\", \"primitive\",\n                    \"bitstype\"];\n\nvar builtinsList = [\"true\", \"false\", \"nothing\", \"NaN\", \"Inf\"];\n\nvar openers = wordRegexp(openersList);\nvar closers = wordRegexp(closersList);\nvar keywords = wordRegexp(keywordsList);\nvar builtins = wordRegexp(builtinsList);\n\nvar macro = /^@[_A-Za-z\\u00A1-\\uFFFF][\\w\\u00A1-\\uFFFF]*!*/;\nvar symbol = /^:[_A-Za-z\\u00A1-\\uFFFF][\\w\\u00A1-\\uFFFF]*!*/;\nvar stringPrefixes = /^(`|([_A-Za-z\\u00A1-\\uFFFF]*\"(\"\")?))/;\n\nvar macroOperators = wordRegexp(asciiOperatorsList, \"\", \"@\");\nvar symbolOperators = wordRegexp(asciiOperatorsList, \"\", \":\");\n\nfunction inArray(state) {\n  return (state.nestedArrays > 0);\n}\n\nfunction inGenerator(state) {\n  return (state.nestedGenerators > 0);\n}\n\nfunction currentScope(state, n) {\n  if (typeof(n) === \"undefined\") { n = 0; }\n  if (state.scopes.length <= n) {\n    return null;\n  }\n  return state.scopes[state.scopes.length - (n + 1)];\n}\n\n// tokenizers\nfunction tokenBase(stream, state) {\n  // Handle multiline comments\n  if (stream.match('#=', false)) {\n    state.tokenize = tokenComment;\n    return state.tokenize(stream, state);\n  }\n\n  // Handle scope changes\n  var leavingExpr = state.leavingExpr;\n  if (stream.sol()) {\n    leavingExpr = false;\n  }\n  state.leavingExpr = false;\n\n  if (leavingExpr) {\n    if (stream.match(/^'+/)) {\n      return \"operator\";\n    }\n  }\n\n  if (stream.match(/\\.{4,}/)) {\n    return \"error\";\n  } else if (stream.match(/\\.{1,3}/)) {\n    return \"operator\";\n  }\n\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  var ch = stream.peek();\n\n  // Handle single line comments\n  if (ch === '#') {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  if (ch === '[') {\n    state.scopes.push('[');\n    state.nestedArrays++;\n  }\n\n  if (ch === '(') {\n    state.scopes.push('(');\n    state.nestedGenerators++;\n  }\n\n  if (inArray(state) && ch === ']') {\n    while (state.scopes.length && currentScope(state) !== \"[\") { state.scopes.pop(); }\n    state.scopes.pop();\n    state.nestedArrays--;\n    state.leavingExpr = true;\n  }\n\n  if (inGenerator(state) && ch === ')') {\n    while (state.scopes.length && currentScope(state) !== \"(\") { state.scopes.pop(); }\n    state.scopes.pop();\n    state.nestedGenerators--;\n    state.leavingExpr = true;\n  }\n\n  if (inArray(state)) {\n    if (state.lastToken == \"end\" && stream.match(':')) {\n      return \"operator\";\n    }\n    if (stream.match('end')) {\n      return \"number\";\n    }\n  }\n\n  var match;\n  if (match = stream.match(openers, false)) {\n    state.scopes.push(match[0]);\n  }\n\n  if (stream.match(closers, false)) {\n    state.scopes.pop();\n  }\n\n  // Handle type annotations\n  if (stream.match(/^::(?![:\\$])/)) {\n    state.tokenize = tokenAnnotation;\n    return state.tokenize(stream, state);\n  }\n\n  // Handle symbols\n  if (!leavingExpr && (stream.match(symbol) || stream.match(symbolOperators))) {\n    return \"builtin\";\n  }\n\n  // Handle parametric types\n  //if (stream.match(/^{[^}]*}(?=\\()/)) {\n  //  return \"builtin\";\n  //}\n\n  // Handle operators and Delimiters\n  if (stream.match(operators)) {\n    return \"operator\";\n  }\n\n  // Handle Number Literals\n  if (stream.match(/^\\.?\\d/, false)) {\n    var imMatcher = RegExp(/^im\\b/);\n    var numberLiteral = false;\n    if (stream.match(/^0x\\.[0-9a-f_]+p[\\+\\-]?[_\\d]+/i)) { numberLiteral = true; }\n    // Integers\n    if (stream.match(/^0x[0-9a-f_]+/i)) { numberLiteral = true; } // Hex\n    if (stream.match(/^0b[01_]+/i)) { numberLiteral = true; } // Binary\n    if (stream.match(/^0o[0-7_]+/i)) { numberLiteral = true; } // Octal\n    // Floats\n    if (stream.match(/^(?:(?:\\d[_\\d]*)?\\.(?!\\.)(?:\\d[_\\d]*)?|\\d[_\\d]*\\.(?!\\.)(?:\\d[_\\d]*))?([Eef][\\+\\-]?[_\\d]+)?/i)) { numberLiteral = true; }\n    if (stream.match(/^\\d[_\\d]*(e[\\+\\-]?\\d+)?/i)) { numberLiteral = true; } // Decimal\n    if (numberLiteral) {\n      // Integer literals may be \"long\"\n      stream.match(imMatcher);\n      state.leavingExpr = true;\n      return \"number\";\n    }\n  }\n\n  // Handle Chars\n  if (stream.match(\"'\")) {\n    state.tokenize = tokenChar;\n    return state.tokenize(stream, state);\n  }\n\n  // Handle Strings\n  if (stream.match(stringPrefixes)) {\n    state.tokenize = tokenStringFactory(stream.current());\n    return state.tokenize(stream, state);\n  }\n\n  if (stream.match(macro) || stream.match(macroOperators)) {\n    return \"meta\";\n  }\n\n  if (stream.match(delimiters)) {\n    return null;\n  }\n\n  if (stream.match(keywords)) {\n    return \"keyword\";\n  }\n\n  if (stream.match(builtins)) {\n    return \"builtin\";\n  }\n\n  var isDefinition = state.isDefinition || state.lastToken == \"function\" ||\n      state.lastToken == \"macro\" || state.lastToken == \"type\" ||\n      state.lastToken == \"struct\" || state.lastToken == \"immutable\";\n\n  if (stream.match(identifiers)) {\n    if (isDefinition) {\n      if (stream.peek() === '.') {\n        state.isDefinition = true;\n        return \"variable\";\n      }\n      state.isDefinition = false;\n      return \"def\";\n    }\n    state.leavingExpr = true;\n    return \"variable\";\n  }\n\n  // Handle non-detected items\n  stream.next();\n  return \"error\";\n}\n\nfunction tokenAnnotation(stream, state) {\n  stream.match(/.*?(?=[,;{}()=\\s]|$)/);\n  if (stream.match('{')) {\n    state.nestedParameters++;\n  } else if (stream.match('}') && state.nestedParameters > 0) {\n    state.nestedParameters--;\n  }\n  if (state.nestedParameters > 0) {\n    stream.match(/.*?(?={|})/) || stream.next();\n  } else if (state.nestedParameters == 0) {\n    state.tokenize = tokenBase;\n  }\n  return \"builtin\";\n}\n\nfunction tokenComment(stream, state) {\n  if (stream.match('#=')) {\n    state.nestedComments++;\n  }\n  if (!stream.match(/.*?(?=(#=|=#))/)) {\n    stream.skipToEnd();\n  }\n  if (stream.match('=#')) {\n    state.nestedComments--;\n    if (state.nestedComments == 0)\n      state.tokenize = tokenBase;\n  }\n  return \"comment\";\n}\n\nfunction tokenChar(stream, state) {\n  var isChar = false, match;\n  if (stream.match(chars)) {\n    isChar = true;\n  } else if (match = stream.match(/\\\\u([a-f0-9]{1,4})(?=')/i)) {\n    var value = parseInt(match[1], 16);\n    if (value <= 55295 || value >= 57344) { // (U+0,U+D7FF), (U+E000,U+FFFF)\n      isChar = true;\n      stream.next();\n    }\n  } else if (match = stream.match(/\\\\U([A-Fa-f0-9]{5,8})(?=')/)) {\n    var value = parseInt(match[1], 16);\n    if (value <= 1114111) { // U+10FFFF\n      isChar = true;\n      stream.next();\n    }\n  }\n  if (isChar) {\n    state.leavingExpr = true;\n    state.tokenize = tokenBase;\n    return \"string\";\n  }\n  if (!stream.match(/^[^']+(?=')/)) { stream.skipToEnd(); }\n  if (stream.match(\"'\")) { state.tokenize = tokenBase; }\n  return \"error\";\n}\n\nfunction tokenStringFactory(delimiter) {\n  if (delimiter.substr(-3) === '\"\"\"') {\n    delimiter = '\"\"\"';\n  } else if (delimiter.substr(-1) === '\"') {\n    delimiter = '\"';\n  }\n  function tokenString(stream, state) {\n    if (stream.eat('\\\\')) {\n      stream.next();\n    } else if (stream.match(delimiter)) {\n      state.tokenize = tokenBase;\n      state.leavingExpr = true;\n      return \"string\";\n    } else {\n      stream.eat(/[`\"]/);\n    }\n    stream.eatWhile(/[^\\\\`\"]/);\n    return \"string\";\n  }\n  return tokenString;\n}\n\nexport const julia = {\n  name: \"julia\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      scopes: [],\n      lastToken: null,\n      leavingExpr: false,\n      isDefinition: false,\n      nestedArrays: 0,\n      nestedComments: 0,\n      nestedGenerators: 0,\n      nestedParameters: 0,\n      firstParenPos: -1\n    };\n  },\n\n  token: function(stream, state) {\n    var style = state.tokenize(stream, state);\n    var current = stream.current();\n\n    if (current && style) {\n      state.lastToken = current;\n    }\n\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var delta = 0;\n    if ( textAfter === ']' || textAfter === ')' || /^end\\b/.test(textAfter) ||\n         /^else/.test(textAfter) || /^catch\\b/.test(textAfter) || /^elseif\\b/.test(textAfter) ||\n         /^finally/.test(textAfter) ) {\n      delta = -1;\n    }\n    return (state.scopes.length + delta) * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*(end|else|catch|finally)\\b$/,\n    commentTokens: {line: \"#\", block: {open: \"#=\", close: \"=#\"}},\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']},\n    autocomplete: keywordsList.concat(builtinsList)\n  }\n};\n"], "names": [], "sourceRoot": ""}