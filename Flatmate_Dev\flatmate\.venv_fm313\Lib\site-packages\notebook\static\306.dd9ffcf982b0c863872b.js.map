{"version": 3, "file": "306.dd9ffcf982b0c863872b.js?v=dd9ffcf982b0c863872b", "mappings": ";;;;;;;;;;;;;;AAAyC;AACa;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD,mBAAmB;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,cAAc;AACd,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,8CAA8C,iBAAiB;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,WAAW,UAAU,GAAG,qBAAqB,IAAI,WAAW,IAAI,aAAa;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,QAAQ,EAAE,wBAAwB,qBAAqB,WAAW,EAAE,YAAY;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,yBAAyB,EAAE;AAC9C;AACA,SAAS,EAAE;AACX;AACA;AACA;AACA,+CAA+C,8BAA8B,0BAA0B;AACvG;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,WAAW;AACX;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,cAAc;AACrC,mCAAmC,EAAE,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA;AACA,gCAAgC;AAChC,sBAAsB;AACtB;AACA;AACA;AACA,iBAAiB;AACjB,qBAAqB;AACrB,qBAAqB;AACrB,gBAAgB;AAChB,kBAAkB;AAClB,gBAAgB;AAChB,wBAAwB;AACxB,qBAAqB;AACrB,0BAA0B;AAC1B,qBAAqB;AACrB,wBAAwB;AACxB,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,6BAA6B,KAAK,GAAG,EAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,eAAe,SAAS;AAC7C,yEAAyE;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,uBAAuB;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,oBAAoB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,wBAAwB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,4BAA4B;AACxD,oCAAoC,4BAA4B;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,uBAAuB,SAAS;AAChC;AACA,SAAS;AACT;AACA;AACA,wBAAwB,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,4BAA4B;AAC5B;AACA;AACA,4BAA4B,UAAU,SAAS,uCAAuC,EAAE;AACxF;AACA,4BAA4B,UAAU,EAAE,MAAM;AAC9C,SAAS;AACT,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,QAAQ;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,wCAAwC;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,mCAAmC,aAAa,IAAI;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,6BAA6B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,mBAAmB;AACnB;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,0BAA0B,uBAAuB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,eAAe;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,OAAO;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,mBAAmB;AACnB,wBAAwB;AACxB;AACA;AACA;AACA;AACA,sBAAsB,WAAW;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,YAAY,WAAW,EAAE,YAAY,EAAE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sBAAsB;AACtB;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;;AAEA,wBAAwB,OAAO,mBAAmB,OAAO;AACzD;AACA;AACA;AACA;AACA,mBAAmB,OAAO,GAAG,sCAAsC;AACnE;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,gBAAgB,KAAK,gBAAgB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,KAAK;AAClC,sCAAsC,QAAQ;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,uBAAuB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,UAAU,oBAAoB,GAAG,uBAAuB;AACzE,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,wBAAwB,GAAG,gCAAgC;AAC3G;AACA;AACA;AACA,+BAA+B,yBAAyB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA,qGAAqG;AACrG;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D,YAAY,WAAW,kBAAkB;AACrG;AACA,6DAA6D,iBAAiB,WAAW,kBAAkB;AAC3G,yCAAyC,wBAAwB,IAAI,YAAY;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,OAAO;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,mBAAmB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,GAAG;AACvB,yBAAyB,IAAI;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,kCAAkC,QAAQ;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,uBAAuB,oBAAoB,WAAW,2BAA2B,SAAS;AACnI,uBAAuB,gBAAgB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,eAAe,eAAe,sCAAsC;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,wBAAwB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,uBAAuB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,eAAe;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,0BAA0B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,mBAAmB;AACrD;AACA;AACA,4BAA4B,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,mBAAmB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA,kDAAkD,cAAc;AAChE;AACA,4BAA4B,8BAA8B;AAC1D,oCAAoC,0BAA0B;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,KAAK,EAAE,2BAA2B,GAAG,sCAAsC;AACpH;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,KAAK,EAAE,0BAA0B,GAAG,sCAAsC;AAC1H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,sBAAsB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,mBAAmB;AACvC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,2BAA2B;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,wBAAwB;AAChD;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,iBAAiB,mDAAQ;AACzB;AACA,oCAAoC,mDAAQ;AAC5C,0CAA0C,0BAA0B;AACpE;AACA;AACA;AACA,gGAAgG,mDAAQ;AACxG,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,sBAAsB;AAC3D;AACA,gCAAgC,sBAAsB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,YAAY;AACrD;AACA,oCAAoC,YAAY;AAChD;AACA,uBAAuB,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,gCAAgC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,aAAa;AACpC;AACA;AACA;AACA,sBAAsB,cAAc;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,aAAa;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,0CAA0C,WAAW;AACrD;AACA;AACA;AACA;AACA;AACA,mBAAmB,oBAAoB;AACvC;AACA;AACA;AACA;AACA;AACA,wDAAwD,QAAQ;AAChE;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C,wCAAwC;AACxC,8BAA8B,KAAK,GAAG,EAAE;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,sDAAsD;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,SAAS;AACT;AACA;AACA;AACA,cAAc,oCAAoC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,oFAAoF;AACnH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,wBAAwB,0BAA0B;AAClD;AACA;AACA;AACA;AACA,uBAAuB,aAAa;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,0BAA0B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,wNAAwN;AACtO;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,SAAS;AACT,eAAe,yDAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,aAAa;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,cAAc,oPAAoP;AAClQ;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,4BAA4B,EAAE,QAAQ;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,aAAa;AACzD,kBAAkB,SAAS;AAC3B;AACA,uBAAuB,OAAO,IAAI,wCAAwC;AAC1E,SAAS;AACT;AACA,qBAAqB,oDAAoD,6DAA6D,GAAG,WAAW;AACpJ,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,QAAQ,UAAU,eAAe,GAAG,SAAS,GAAG,QAAQ,KAAK,sBAAsB,+BAA+B,0BAA0B,OAAO,cAAc,KAAK,EAAE,gDAAgD;AACjP;AACA;AACA;AACA;AACA,qCAAqC,WAAW,IAAI,mCAAmC;AACvF,qEAAqE,UAAU;AAC/E,yBAAyB,QAAQ,WAAW,eAAe,GAAG,OAAO,UAAU,cAAc;AAC7F;AACA,SAAS;AACT;AACA;AACA;AACA,gCAAgC,EAAE,6BAA6B,YAAY,OAAO;AAClF;AACA,iCAAiC,EAAE,6BAA6B,OAAO,OAAO;AAC9E;AACA;AACA;AACA;AACA;AACA,4DAA4D,EAAE,IAAI,eAAe;AACjF,2BAA2B,SAAS;AACpC,aAAa,sBAAsB;AACnC,YAAY,gCAAgC;AAC5C,eAAe,uBAAuB;AACtC,UAAU,kBAAkB;AAC5B,eAAe,0BAA0B;AACzC,aAAa,QAAQ,EAAE;AACvB,aAAa,QAAQ,OAAO,EAAE;AAC9B;AACA,MAAM;AACN,UAAU,EAAE;AACZ,kBAAkB,mBAAmB,QAAQ,EAAE;AAC/C,kBAAkB,6BAA6B,OAAO;AACtD,qBAAqB,gBAAgB;AACrC,eAAe,uBAAuB;AACtC,iBAAiB,sBAAsB;AACvC,cAAc,yBAAyB,EAAE;AACzC,aAAa,EAAE,qBAAqB,OAAO,EAAE;AAC7C,wBAAwB,mCAAmC,OAAO,EAAE;AACpE,kBAAkB,sBAAsB,QAAQ;AAChD,eAAe,UAAU,EAAE;AAC3B,eAAe,0BAA0B;AACzC,CAAC;AACD;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,GAAG,EAAE,2BAA2B,EAAE,qBAAqB;AACjF;AACA,yBAAyB,2BAA2B;AACpD,kCAAkC,kBAAkB,EAAE,2BAA2B,EAAE,GAAG;AACtF;AACA,sDAAsD,YAAY,IAAI,UAAU,sBAAsB,YAAY,IAAI,UAAU;AAChI,qCAAqC,IAAI,kBAAkB,MAAM,oBAAoB,GAAG;AACxF,qBAAqB,IAAI,kBAAkB,oBAAoB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,kBAAkB;AAC7D;AACA;AACA;AACA;AACA,wCAAwC,cAAc;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,6CAA6C;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,4BAA4B,kBAAkB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,KAAK;AACzE;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,eAAe;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,aAAa;AAC3E;AACA,yDAAyD,aAAa;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,UAAU,SAAS,UAAU;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,cAAc;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,KAAK;AAChE;AACA;AACA;AACA;AACA,cAAc,8EAA8E;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,cAAc,uBAAuB,aAAa;AAChF;AACA,gCAAgC,aAAa,IAAI,cAAc;AAC/D;AACA;AACA;AACA,sCAAsC,mBAAmB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,UAAU;AACxD,qDAAqD,UAAU,GAAG,QAAQ;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,KAAK;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,oBAAoB;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,UAAU;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,UAAU;AAC5D;AACA;AACA;AACA;AACA;AACA,6CAA6C,KAAK;AAClD,SAAS;AACT;AACA;AACA;AACA,cAAc,iCAAiC;AAC/C;AACA;AACA,iDAAiD,KAAK;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,UAAU;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,yCAAyC;AACtE,kDAAkD;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE,uBAAuB,KAAK,WAAW,GAAG,MAAM,KAAK,WAAW;AACvI;AACA,0EAA0E,uBAAuB,KAAK,UAAU;AAChH;AACA,uEAAuE,uBAAuB,KAAK,UAAU;AAC7G;AACA,wEAAwE,uBAAuB,KAAK,UAAU;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,kBAAkB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,YAAY,2EAA2E,UAAU;AACrI;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,YAAY;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA,yCAAyC,KAAK;AAC9C,gCAAgC,kBAAkB;AAClD;AACA;AACA;AACA,yCAAyC,KAAK,SAAS,SAAS;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,wBAAwB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,6CAA6C;AACrE;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,4BAA4B;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,oBAAoB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,iBAAiB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE;AACtE;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,uBAAuB,iBAAiB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,2CAA2C;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,KAAK;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,6DAA6D;AACtI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,gBAAgB;AACrE;AACA;AACA;AACA;AACA,gEAAgE,aAAa;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,uBAAuB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,uBAAuB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E,KAAK;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,aAAa;AAChC;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA,4BAA4B,gBAAgB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,gCAAgC;AACrG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA,eAAe,iCAAiC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,KAAK;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,MAAM;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,yBAAyB;AAC9F;AACA;AACA;AACA;AACA,uBAAuB,MAAM;AAC7B,mBAAmB,OAAO;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,WAAW;AAC5D;AACA,4BAA4B,qDAAqD;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,kCAAkC,EAAE,2BAA2B,kCAAkC,OAAO;AAChL;AACA,yDAAyD,WAAW,MAAM,kBAAkB,sBAAsB,QAAQ;AAC1H,0CAA0C,qBAAqB;AAC/D;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,cAAc;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,aAAa;AAC/E;AACA,sBAAsB,kCAAkC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA,qEAAqE,yBAAyB;AAC9F,mBAAmB,iBAAiB;AACpC;AACA,mDAAmD,QAAQ,MAAM,QAAQ,sBAAsB,yBAAyB,yBAAyB,QAAQ;AACzJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qGAAqG,eAAe,OAAO,eAAe;AAC1I;AACA;AACA;AACA;AACA,yCAAyC,2BAA2B;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,gEAAe;AAC7C,+CAA+C,2CAA2C,GAAG,sBAAsB,IAAI,WAAW,EAAE,qBAAqB,iBAAiB,OAAO;AACjL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,uBAAuB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,aAAa;AAChC;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D;AAC5D;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,cAAc,cAAc,SAAS;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,cAAc;AAC7E;AACA;AACA,4DAA4D;AAC5D;AACA,qBAAqB;AACrB;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,iBAAiB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEkD", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@lezer/generator/dist/index.js"], "sourcesContent": ["import { NodeProp } from '@lezer/common';\nimport { <PERSON><PERSON><PERSON>er, LocalTokenGroup } from '@lezer/lr';\n\nclass Node {\n    constructor(start) {\n        this.start = start;\n    }\n}\nclass GrammarDeclaration extends Node {\n    constructor(start, rules, topRules, tokens, localTokens, context, externalTokens, externalSpecializers, externalPropSources, precedences, mainSkip, scopedSkip, dialects, externalProps, autoDelim) {\n        super(start);\n        this.rules = rules;\n        this.topRules = topRules;\n        this.tokens = tokens;\n        this.localTokens = localTokens;\n        this.context = context;\n        this.externalTokens = externalTokens;\n        this.externalSpecializers = externalSpecializers;\n        this.externalPropSources = externalPropSources;\n        this.precedences = precedences;\n        this.mainSkip = mainSkip;\n        this.scopedSkip = scopedSkip;\n        this.dialects = dialects;\n        this.externalProps = externalProps;\n        this.autoDelim = autoDelim;\n    }\n    toString() { return Object.values(this.rules).join(\"\\n\"); }\n}\nclass RuleDeclaration extends Node {\n    constructor(start, id, props, params, expr) {\n        super(start);\n        this.id = id;\n        this.props = props;\n        this.params = params;\n        this.expr = expr;\n    }\n    toString() {\n        return this.id.name + (this.params.length ? `<${this.params.join()}>` : \"\") + \" -> \" + this.expr;\n    }\n}\nclass PrecDeclaration extends Node {\n    constructor(start, items) {\n        super(start);\n        this.items = items;\n    }\n}\nclass TokenPrecDeclaration extends Node {\n    constructor(start, items) {\n        super(start);\n        this.items = items;\n    }\n}\nclass TokenConflictDeclaration extends Node {\n    constructor(start, a, b) {\n        super(start);\n        this.a = a;\n        this.b = b;\n    }\n}\nclass TokenDeclaration extends Node {\n    constructor(start, precedences, conflicts, rules, literals) {\n        super(start);\n        this.precedences = precedences;\n        this.conflicts = conflicts;\n        this.rules = rules;\n        this.literals = literals;\n    }\n}\nclass LocalTokenDeclaration extends Node {\n    constructor(start, precedences, rules, fallback) {\n        super(start);\n        this.precedences = precedences;\n        this.rules = rules;\n        this.fallback = fallback;\n    }\n}\nclass LiteralDeclaration extends Node {\n    constructor(start, literal, props) {\n        super(start);\n        this.literal = literal;\n        this.props = props;\n    }\n}\nclass ContextDeclaration extends Node {\n    constructor(start, id, source) {\n        super(start);\n        this.id = id;\n        this.source = source;\n    }\n}\nclass ExternalTokenDeclaration extends Node {\n    constructor(start, id, source, tokens) {\n        super(start);\n        this.id = id;\n        this.source = source;\n        this.tokens = tokens;\n    }\n}\nclass ExternalSpecializeDeclaration extends Node {\n    constructor(start, type, token, id, source, tokens) {\n        super(start);\n        this.type = type;\n        this.token = token;\n        this.id = id;\n        this.source = source;\n        this.tokens = tokens;\n    }\n}\nclass ExternalPropSourceDeclaration extends Node {\n    constructor(start, id, source) {\n        super(start);\n        this.id = id;\n        this.source = source;\n    }\n}\nclass ExternalPropDeclaration extends Node {\n    constructor(start, id, externalID, source) {\n        super(start);\n        this.id = id;\n        this.externalID = externalID;\n        this.source = source;\n    }\n}\nclass Identifier extends Node {\n    constructor(start, name) {\n        super(start);\n        this.name = name;\n    }\n    toString() { return this.name; }\n}\nclass Expression extends Node {\n    walk(f) { return f(this); }\n    eq(_other) { return false; }\n}\nExpression.prototype.prec = 10;\nclass NameExpression extends Expression {\n    constructor(start, id, args) {\n        super(start);\n        this.id = id;\n        this.args = args;\n    }\n    toString() { return this.id.name + (this.args.length ? `<${this.args.join()}>` : \"\"); }\n    eq(other) {\n        return this.id.name == other.id.name && exprsEq(this.args, other.args);\n    }\n    walk(f) {\n        let args = walkExprs(this.args, f);\n        return f(args == this.args ? this : new NameExpression(this.start, this.id, args));\n    }\n}\nclass SpecializeExpression extends Expression {\n    constructor(start, type, props, token, content) {\n        super(start);\n        this.type = type;\n        this.props = props;\n        this.token = token;\n        this.content = content;\n    }\n    toString() { return `@${this.type}[${this.props.join(\",\")}]<${this.token}, ${this.content}>`; }\n    eq(other) {\n        return this.type == other.type && Prop.eqProps(this.props, other.props) && exprEq(this.token, other.token) &&\n            exprEq(this.content, other.content);\n    }\n    walk(f) {\n        let token = this.token.walk(f), content = this.content.walk(f);\n        return f(token == this.token && content == this.content ? this : new SpecializeExpression(this.start, this.type, this.props, token, content));\n    }\n}\nclass InlineRuleExpression extends Expression {\n    constructor(start, rule) {\n        super(start);\n        this.rule = rule;\n    }\n    toString() {\n        let rule = this.rule;\n        return `${rule.id}${rule.props.length ? `[${rule.props.join(\",\")}]` : \"\"} { ${rule.expr} }`;\n    }\n    eq(other) {\n        let rule = this.rule, oRule = other.rule;\n        return exprEq(rule.expr, oRule.expr) && rule.id.name == oRule.id.name && Prop.eqProps(rule.props, oRule.props);\n    }\n    walk(f) {\n        let rule = this.rule, expr = rule.expr.walk(f);\n        return f(expr == rule.expr ? this :\n            new InlineRuleExpression(this.start, new RuleDeclaration(rule.start, rule.id, rule.props, [], expr)));\n    }\n}\nclass ChoiceExpression extends Expression {\n    constructor(start, exprs) {\n        super(start);\n        this.exprs = exprs;\n    }\n    toString() { return this.exprs.map(e => maybeParens(e, this)).join(\" | \"); }\n    eq(other) {\n        return exprsEq(this.exprs, other.exprs);\n    }\n    walk(f) {\n        let exprs = walkExprs(this.exprs, f);\n        return f(exprs == this.exprs ? this : new ChoiceExpression(this.start, exprs));\n    }\n}\nChoiceExpression.prototype.prec = 1;\nclass SequenceExpression extends Expression {\n    constructor(start, exprs, markers, empty = false) {\n        super(start);\n        this.exprs = exprs;\n        this.markers = markers;\n        this.empty = empty;\n    }\n    toString() { return this.empty ? \"()\" : this.exprs.map(e => maybeParens(e, this)).join(\" \"); }\n    eq(other) {\n        return exprsEq(this.exprs, other.exprs) && this.markers.every((m, i) => {\n            let om = other.markers[i];\n            return m.length == om.length && m.every((x, i) => x.eq(om[i]));\n        });\n    }\n    walk(f) {\n        let exprs = walkExprs(this.exprs, f);\n        return f(exprs == this.exprs ? this : new SequenceExpression(this.start, exprs, this.markers, this.empty && !exprs.length));\n    }\n}\nSequenceExpression.prototype.prec = 2;\nclass ConflictMarker extends Node {\n    constructor(start, id, type) {\n        super(start);\n        this.id = id;\n        this.type = type;\n    }\n    toString() { return (this.type == \"ambig\" ? \"~\" : \"!\") + this.id.name; }\n    eq(other) { return this.id.name == other.id.name && this.type == other.type; }\n}\nclass RepeatExpression extends Expression {\n    constructor(start, expr, kind) {\n        super(start);\n        this.expr = expr;\n        this.kind = kind;\n    }\n    toString() { return maybeParens(this.expr, this) + this.kind; }\n    eq(other) {\n        return exprEq(this.expr, other.expr) && this.kind == other.kind;\n    }\n    walk(f) {\n        let expr = this.expr.walk(f);\n        return f(expr == this.expr ? this : new RepeatExpression(this.start, expr, this.kind));\n    }\n}\nRepeatExpression.prototype.prec = 3;\nclass LiteralExpression extends Expression {\n    // value.length is always > 0\n    constructor(start, value) {\n        super(start);\n        this.value = value;\n    }\n    toString() { return JSON.stringify(this.value); }\n    eq(other) { return this.value == other.value; }\n}\nclass SetExpression extends Expression {\n    constructor(start, ranges, inverted) {\n        super(start);\n        this.ranges = ranges;\n        this.inverted = inverted;\n    }\n    toString() {\n        return `[${this.inverted ? \"^\" : \"\"}${this.ranges.map(([a, b]) => {\n            return String.fromCodePoint(a) + (b == a + 1 ? \"\" : \"-\" + String.fromCodePoint(b));\n        })}]`;\n    }\n    eq(other) {\n        return this.inverted == other.inverted && this.ranges.length == other.ranges.length &&\n            this.ranges.every(([a, b], i) => { let [x, y] = other.ranges[i]; return a == x && b == y; });\n    }\n}\nclass AnyExpression extends Expression {\n    constructor(start) {\n        super(start);\n    }\n    toString() { return \"_\"; }\n    eq() { return true; }\n}\nfunction walkExprs(exprs, f) {\n    let result = null;\n    for (let i = 0; i < exprs.length; i++) {\n        let expr = exprs[i].walk(f);\n        if (expr != exprs[i] && !result)\n            result = exprs.slice(0, i);\n        if (result)\n            result.push(expr);\n    }\n    return result || exprs;\n}\nconst CharClasses = {\n    asciiLetter: [[65, 91], [97, 123]],\n    asciiLowercase: [[97, 123]],\n    asciiUppercase: [[65, 91]],\n    digit: [[48, 58]],\n    whitespace: [[9, 14], [32, 33], [133, 134], [160, 161], [5760, 5761], [8192, 8203],\n        [8232, 8234], [8239, 8240], [8287, 8288], [12288, 12289]],\n    eof: [[0xffff, 0xffff]]\n};\nclass CharClass extends Expression {\n    constructor(start, type) {\n        super(start);\n        this.type = type;\n    }\n    toString() { return \"@\" + this.type; }\n    eq(expr) { return this.type == expr.type; }\n}\nfunction exprEq(a, b) {\n    return a.constructor == b.constructor && a.eq(b);\n}\nfunction exprsEq(a, b) {\n    return a.length == b.length && a.every((e, i) => exprEq(e, b[i]));\n}\nclass Prop extends Node {\n    constructor(start, at, name, value) {\n        super(start);\n        this.at = at;\n        this.name = name;\n        this.value = value;\n    }\n    eq(other) {\n        return this.name == other.name && this.value.length == other.value.length &&\n            this.value.every((v, i) => v.value == other.value[i].value && v.name == other.value[i].name);\n    }\n    toString() {\n        let result = (this.at ? \"@\" : \"\") + this.name;\n        if (this.value.length) {\n            result += \"=\";\n            for (let { name, value } of this.value)\n                result += name ? `{${name}}` : /[^\\w-]/.test(value) ? JSON.stringify(value) : value;\n        }\n        return result;\n    }\n    static eqProps(a, b) {\n        return a.length == b.length && a.every((p, i) => p.eq(b[i]));\n    }\n}\nclass PropPart extends Node {\n    constructor(start, value, name) {\n        super(start);\n        this.value = value;\n        this.name = name;\n    }\n}\nfunction maybeParens(node, parent) {\n    return node.prec < parent.prec ? \"(\" + node.toString() + \")\" : node.toString();\n}\n\n/**\nThe type of error raised when the parser generator finds an issue.\n*/\nclass GenError extends Error {\n}\n\nfunction hasProps(props) {\n    for (let _p in props)\n        return true;\n    return false;\n}\nlet termHash = 0;\nclass Term {\n    constructor(name, flags, nodeName, props = {}) {\n        this.name = name;\n        this.flags = flags;\n        this.nodeName = nodeName;\n        this.props = props;\n        this.hash = ++termHash; // Used for sorting and hashing during parser generation\n        this.id = -1; // Assigned in a later stage, used in actual output\n        // Filled in only after the rules are simplified, used in automaton.ts\n        this.rules = [];\n    }\n    toString() { return this.name; }\n    get nodeType() { return this.top || this.nodeName != null || hasProps(this.props) || this.repeated; }\n    get terminal() { return (this.flags & 1 /* TermFlag.Terminal */) > 0; }\n    get eof() { return (this.flags & 4 /* TermFlag.Eof */) > 0; }\n    get error() { return \"error\" in this.props; }\n    get top() { return (this.flags & 2 /* TermFlag.Top */) > 0; }\n    get interesting() { return this.flags > 0 || this.nodeName != null; }\n    get repeated() { return (this.flags & 16 /* TermFlag.Repeated */) > 0; }\n    set preserve(value) { this.flags = value ? this.flags | 8 /* TermFlag.Preserve */ : this.flags & ~8 /* TermFlag.Preserve */; }\n    get preserve() { return (this.flags & 8 /* TermFlag.Preserve */) > 0; }\n    set inline(value) { this.flags = value ? this.flags | 32 /* TermFlag.Inline */ : this.flags & ~32 /* TermFlag.Inline */; }\n    get inline() { return (this.flags & 32 /* TermFlag.Inline */) > 0; }\n    cmp(other) { return this.hash - other.hash; }\n}\nclass TermSet {\n    constructor() {\n        this.terms = [];\n        // Map from term names to Term instances\n        this.names = Object.create(null);\n        this.tops = [];\n        this.eof = this.term(\"␄\", null, 1 /* TermFlag.Terminal */ | 4 /* TermFlag.Eof */);\n        this.error = this.term(\"⚠\", \"⚠\", 8 /* TermFlag.Preserve */);\n    }\n    term(name, nodeName, flags = 0, props = {}) {\n        let term = new Term(name, flags, nodeName, props);\n        this.terms.push(term);\n        this.names[name] = term;\n        return term;\n    }\n    makeTop(nodeName, props) {\n        const term = this.term(\"@top\", nodeName, 2 /* TermFlag.Top */, props);\n        this.tops.push(term);\n        return term;\n    }\n    makeTerminal(name, nodeName, props = {}) {\n        return this.term(name, nodeName, 1 /* TermFlag.Terminal */, props);\n    }\n    makeNonTerminal(name, nodeName, props = {}) {\n        return this.term(name, nodeName, 0, props);\n    }\n    makeRepeat(name) {\n        return this.term(name, null, 16 /* TermFlag.Repeated */);\n    }\n    uniqueName(name) {\n        for (let i = 0;; i++) {\n            let cur = i ? `${name}-${i}` : name;\n            if (!this.names[cur])\n                return cur;\n        }\n    }\n    finish(rules) {\n        for (let rule of rules)\n            rule.name.rules.push(rule);\n        this.terms = this.terms.filter(t => t.terminal || t.preserve || rules.some(r => r.name == t || r.parts.includes(t)));\n        let names = {};\n        let nodeTypes = [this.error];\n        this.error.id = 0 /* T.Err */;\n        let nextID = 0 /* T.Err */ + 1;\n        // Assign ids to terms that represent node types\n        for (let term of this.terms)\n            if (term.id < 0 && term.nodeType && !term.repeated) {\n                term.id = nextID++;\n                nodeTypes.push(term);\n            }\n        // Put all repeated terms after the regular node types\n        let minRepeatTerm = nextID;\n        for (let term of this.terms)\n            if (term.repeated) {\n                term.id = nextID++;\n                nodeTypes.push(term);\n            }\n        // Then comes the EOF term\n        this.eof.id = nextID++;\n        // And then the remaining (non-node, non-repeat) terms.\n        for (let term of this.terms) {\n            if (term.id < 0)\n                term.id = nextID++;\n            if (term.name)\n                names[term.id] = term.name;\n        }\n        if (nextID >= 0xfffe)\n            throw new GenError(\"Too many terms\");\n        return { nodeTypes, names, minRepeatTerm, maxTerm: nextID - 1 };\n    }\n}\nfunction cmpSet(a, b, cmp) {\n    if (a.length != b.length)\n        return a.length - b.length;\n    for (let i = 0; i < a.length; i++) {\n        let diff = cmp(a[i], b[i]);\n        if (diff)\n            return diff;\n    }\n    return 0;\n}\nconst none$3 = [];\nclass Conflicts {\n    constructor(precedence, ambigGroups = none$3, cut = 0) {\n        this.precedence = precedence;\n        this.ambigGroups = ambigGroups;\n        this.cut = cut;\n    }\n    join(other) {\n        if (this == Conflicts.none || this == other)\n            return other;\n        if (other == Conflicts.none)\n            return this;\n        return new Conflicts(Math.max(this.precedence, other.precedence), union(this.ambigGroups, other.ambigGroups), Math.max(this.cut, other.cut));\n    }\n    cmp(other) {\n        return this.precedence - other.precedence || cmpSet(this.ambigGroups, other.ambigGroups, (a, b) => a < b ? -1 : a > b ? 1 : 0) ||\n            this.cut - other.cut;\n    }\n}\nConflicts.none = new Conflicts(0);\nfunction union(a, b) {\n    if (a.length == 0 || a == b)\n        return b;\n    if (b.length == 0)\n        return a;\n    let result = a.slice();\n    for (let value of b)\n        if (!a.includes(value))\n            result.push(value);\n    return result.sort();\n}\nlet ruleID = 0;\nclass Rule {\n    constructor(name, parts, conflicts, skip) {\n        this.name = name;\n        this.parts = parts;\n        this.conflicts = conflicts;\n        this.skip = skip;\n        this.id = ruleID++;\n    }\n    cmp(rule) {\n        return this.id - rule.id;\n    }\n    cmpNoName(rule) {\n        return this.parts.length - rule.parts.length ||\n            this.skip.hash - rule.skip.hash ||\n            this.parts.reduce((r, s, i) => r || s.cmp(rule.parts[i]), 0) ||\n            cmpSet(this.conflicts, rule.conflicts, (a, b) => a.cmp(b));\n    }\n    toString() {\n        return this.name + \" -> \" + this.parts.join(\" \");\n    }\n    get isRepeatWrap() {\n        return this.name.repeated && this.parts.length == 2 && this.parts[0] == this.name;\n    }\n    sameReduce(other) {\n        return this.name == other.name && this.parts.length == other.parts.length && this.isRepeatWrap == other.isRepeatWrap;\n    }\n}\n\nconst MAX_CHAR = 0xffff;\nclass Edge {\n    constructor(from, to, target) {\n        this.from = from;\n        this.to = to;\n        this.target = target;\n    }\n    toString() {\n        return `-> ${this.target.id}[label=${JSON.stringify(this.from < 0 ? \"ε\" : charFor(this.from) +\n            (this.to > this.from + 1 ? \"-\" + charFor(this.to - 1) : \"\"))}]`;\n    }\n}\nfunction charFor(n) {\n    return n > MAX_CHAR ? \"∞\"\n        : n == 10 ? \"\\\\n\"\n            : n == 13 ? \"\\\\r\"\n                : n < 32 || n >= 0xd800 && n < 0xdfff ? \"\\\\u{\" + n.toString(16) + \"}\"\n                    : String.fromCharCode(n);\n}\nfunction minimize(states, start) {\n    let partition = Object.create(null);\n    let byAccepting = Object.create(null);\n    for (let state of states) {\n        let id = ids(state.accepting);\n        let group = byAccepting[id] || (byAccepting[id] = []);\n        group.push(state);\n        partition[state.id] = group;\n    }\n    for (;;) {\n        let split = false, newPartition = Object.create(null);\n        for (let state of states) {\n            if (newPartition[state.id])\n                continue;\n            let group = partition[state.id];\n            if (group.length == 1) {\n                newPartition[group[0].id] = group;\n                continue;\n            }\n            let parts = [];\n            groups: for (let state of group) {\n                for (let p of parts) {\n                    if (isEquivalent(state, p[0], partition)) {\n                        p.push(state);\n                        continue groups;\n                    }\n                }\n                parts.push([state]);\n            }\n            if (parts.length > 1)\n                split = true;\n            for (let p of parts)\n                for (let s of p)\n                    newPartition[s.id] = p;\n        }\n        if (!split)\n            return applyMinimization(states, start, partition);\n        partition = newPartition;\n    }\n}\nfunction isEquivalent(a, b, partition) {\n    if (a.edges.length != b.edges.length)\n        return false;\n    for (let i = 0; i < a.edges.length; i++) {\n        let eA = a.edges[i], eB = b.edges[i];\n        if (eA.from != eB.from || eA.to != eB.to || partition[eA.target.id] != partition[eB.target.id])\n            return false;\n    }\n    return true;\n}\nfunction applyMinimization(states, start, partition) {\n    for (let state of states) {\n        for (let i = 0; i < state.edges.length; i++) {\n            let edge = state.edges[i], target = partition[edge.target.id][0];\n            if (target != edge.target)\n                state.edges[i] = new Edge(edge.from, edge.to, target);\n        }\n    }\n    return partition[start.id][0];\n}\nlet stateID = 1;\nlet State$1 = class State {\n    constructor(accepting = [], id = stateID++) {\n        this.accepting = accepting;\n        this.id = id;\n        this.edges = [];\n    }\n    edge(from, to, target) {\n        this.edges.push(new Edge(from, to, target));\n    }\n    nullEdge(target) { this.edge(-1, -1, target); }\n    compile() {\n        let labeled = Object.create(null), localID = 0;\n        let startState = explore(this.closure().sort((a, b) => a.id - b.id));\n        return minimize(Object.values(labeled), startState);\n        function explore(states) {\n            let newState = labeled[ids(states)] =\n                new State(states.reduce((a, s) => union(a, s.accepting), []), localID++);\n            let out = [];\n            for (let state of states)\n                for (let edge of state.edges) {\n                    if (edge.from >= 0)\n                        out.push(edge);\n                }\n            let transitions = mergeEdges(out);\n            for (let merged of transitions) {\n                let targets = merged.targets.sort((a, b) => a.id - b.id);\n                newState.edge(merged.from, merged.to, labeled[ids(targets)] || explore(targets));\n            }\n            return newState;\n        }\n    }\n    closure() {\n        let result = [], seen = Object.create(null);\n        function explore(state) {\n            if (seen[state.id])\n                return;\n            seen[state.id] = true;\n            // States with only epsilon edges and no accepting term that\n            // isn't also in the next states are left out to help reduce the\n            // number of unique state combinations\n            if (state.edges.some(e => e.from >= 0) ||\n                (state.accepting.length > 0 && !state.edges.some(e => sameSet$1(state.accepting, e.target.accepting))))\n                result.push(state);\n            for (let edge of state.edges)\n                if (edge.from < 0)\n                    explore(edge.target);\n        }\n        explore(this);\n        return result;\n    }\n    findConflicts(occurTogether) {\n        let conflicts = [], cycleTerms = this.cycleTerms();\n        function add(a, b, soft, aEdges, bEdges) {\n            if (a.id < b.id) {\n                [a, b] = [b, a];\n                soft = -soft;\n            }\n            let found = conflicts.find(c => c.a == a && c.b == b);\n            if (!found)\n                conflicts.push(new Conflict$1(a, b, soft, exampleFromEdges(aEdges), bEdges && exampleFromEdges(bEdges)));\n            else if (found.soft != soft)\n                found.soft = 0;\n        }\n        this.reachable((state, edges) => {\n            if (state.accepting.length == 0)\n                return;\n            for (let i = 0; i < state.accepting.length; i++)\n                for (let j = i + 1; j < state.accepting.length; j++)\n                    add(state.accepting[i], state.accepting[j], 0, edges);\n            state.reachable((s, es) => {\n                if (s != state)\n                    for (let term of s.accepting) {\n                        let hasCycle = cycleTerms.includes(term);\n                        for (let orig of state.accepting)\n                            if (term != orig)\n                                add(term, orig, hasCycle || cycleTerms.includes(orig) || !occurTogether(term, orig) ? 0 : 1, edges, edges.concat(es));\n                    }\n            });\n        });\n        return conflicts;\n    }\n    cycleTerms() {\n        let work = [];\n        this.reachable(state => {\n            for (let { target } of state.edges)\n                work.push(state, target);\n        });\n        let table = new Map;\n        let haveCycle = [];\n        for (let i = 0; i < work.length;) {\n            let from = work[i++], to = work[i++];\n            let entry = table.get(from);\n            if (!entry)\n                table.set(from, entry = []);\n            if (entry.includes(to))\n                continue;\n            if (from == to) {\n                if (!haveCycle.includes(from))\n                    haveCycle.push(from);\n            }\n            else {\n                for (let next of entry)\n                    work.push(from, next);\n                entry.push(to);\n            }\n        }\n        let result = [];\n        for (let state of haveCycle) {\n            for (let term of state.accepting) {\n                if (!result.includes(term))\n                    result.push(term);\n            }\n        }\n        return result;\n    }\n    reachable(f) {\n        let seen = [], edges = [];\n        (function explore(s) {\n            f(s, edges);\n            seen.push(s);\n            for (let edge of s.edges)\n                if (!seen.includes(edge.target)) {\n                    edges.push(edge);\n                    explore(edge.target);\n                    edges.pop();\n                }\n        })(this);\n    }\n    toString() {\n        let out = \"digraph {\\n\";\n        this.reachable(state => {\n            if (state.accepting.length)\n                out += `  ${state.id} [label=${JSON.stringify(state.accepting.join())}];\\n`;\n            for (let edge of state.edges)\n                out += `  ${state.id} ${edge};\\n`;\n        });\n        return out + \"}\";\n    }\n    // Tokenizer data is represented as a single flat array. This\n    // contains regions for each tokenizer state. Region offsets are\n    // used to identify states.\n    //\n    // Each state is laid out as:\n    //  - Token group mask\n    //  - Offset of the end of the accepting data\n    //  - Number of outgoing edges in the state\n    //  - Pairs of token masks and term ids that indicate the accepting\n    //    states, sorted by precedence\n    //  - Triples for the edges: each with a low and high bound and the\n    //    offset of the next state.\n    toArray(groupMasks, precedence) {\n        let offsets = []; // Used to 'link' the states after building the arrays\n        let data = [];\n        this.reachable(state => {\n            let start = data.length;\n            let acceptEnd = start + 3 + state.accepting.length * 2;\n            offsets[state.id] = start;\n            data.push(state.stateMask(groupMasks), acceptEnd, state.edges.length);\n            state.accepting.sort((a, b) => precedence.indexOf(a.id) - precedence.indexOf(b.id));\n            for (let term of state.accepting)\n                data.push(term.id, groupMasks[term.id] || 0xffff);\n            for (let edge of state.edges)\n                data.push(edge.from, edge.to, -edge.target.id - 1);\n        });\n        // Replace negative numbers with resolved state offsets\n        for (let i = 0; i < data.length; i++)\n            if (data[i] < 0)\n                data[i] = offsets[-data[i] - 1];\n        if (data.length > Math.pow(2, 16))\n            throw new GenError(\"Tokenizer tables too big to represent with 16-bit offsets.\");\n        return Uint16Array.from(data);\n    }\n    stateMask(groupMasks) {\n        let mask = 0;\n        this.reachable(state => {\n            for (let term of state.accepting)\n                mask |= (groupMasks[term.id] || 0xffff);\n        });\n        return mask;\n    }\n};\nlet Conflict$1 = class Conflict {\n    constructor(a, b, \n    // Conflicts between two non-cyclic tokens are marked as\n    // 'soft', with a negative number if a is shorter than\n    // b, and a positive if b is shorter than a.\n    soft, exampleA, exampleB) {\n        this.a = a;\n        this.b = b;\n        this.soft = soft;\n        this.exampleA = exampleA;\n        this.exampleB = exampleB;\n    }\n};\nfunction exampleFromEdges(edges) {\n    let str = \"\";\n    for (let i = 0; i < edges.length; i++)\n        str += String.fromCharCode(edges[i].from);\n    return str;\n}\nfunction ids(elts) {\n    let result = \"\";\n    for (let elt of elts) {\n        if (result.length)\n            result += \"-\";\n        result += elt.id;\n    }\n    return result;\n}\nfunction sameSet$1(a, b) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (a[i] != b[i])\n            return false;\n    return true;\n}\nclass MergedEdge {\n    constructor(from, to, targets) {\n        this.from = from;\n        this.to = to;\n        this.targets = targets;\n    }\n}\n// Merge multiple edges (tagged by character ranges) into a set of\n// mutually exclusive ranges pointing at all target states for that\n// range\nfunction mergeEdges(edges) {\n    let separate = [], result = [];\n    for (let edge of edges) {\n        if (!separate.includes(edge.from))\n            separate.push(edge.from);\n        if (!separate.includes(edge.to))\n            separate.push(edge.to);\n    }\n    separate.sort((a, b) => a - b);\n    for (let i = 1; i < separate.length; i++) {\n        let from = separate[i - 1], to = separate[i];\n        let found = [];\n        for (let edge of edges)\n            if (edge.to > from && edge.from < to) {\n                for (let target of edge.target.closure())\n                    if (!found.includes(target))\n                        found.push(target);\n            }\n        if (found.length)\n            result.push(new MergedEdge(from, to, found));\n    }\n    let eof = edges.filter(e => e.from == 65535 /* Seq.End */ && e.to == 65535 /* Seq.End */);\n    if (eof.length) {\n        let found = [];\n        for (let edge of eof)\n            for (let target of edge.target.closure())\n                if (!found.includes(target))\n                    found.push(target);\n        if (found.length)\n            result.push(new MergedEdge(65535 /* Seq.End */, 65535 /* Seq.End */, found));\n    }\n    return result;\n}\n\n// Note that this is the parser for grammar files, not the generated parser\nlet word = /[\\w_-]+/gy;\n// Some engines (specifically SpiderMonkey) have still not implemented \\p\ntry {\n    word = /[\\p{Alphabetic}\\d_-]+/ugy;\n}\ncatch (_) { }\nconst none$2 = [];\nclass Input {\n    constructor(string, fileName = null) {\n        this.string = string;\n        this.fileName = fileName;\n        this.type = \"sof\";\n        this.value = null;\n        this.start = 0;\n        this.end = 0;\n        this.next();\n    }\n    lineInfo(pos) {\n        for (let line = 1, cur = 0;;) {\n            let next = this.string.indexOf(\"\\n\", cur);\n            if (next > -1 && next < pos) {\n                ++line;\n                cur = next + 1;\n            }\n            else {\n                return { line, ch: pos - cur };\n            }\n        }\n    }\n    message(msg, pos = -1) {\n        let posInfo = this.fileName || \"\";\n        if (pos > -1) {\n            let info = this.lineInfo(pos);\n            posInfo += (posInfo ? \" \" : \"\") + info.line + \":\" + info.ch;\n        }\n        return posInfo ? msg + ` (${posInfo})` : msg;\n    }\n    raise(msg, pos = -1) {\n        throw new GenError(this.message(msg, pos));\n    }\n    match(pos, re) {\n        let match = re.exec(this.string.slice(pos));\n        return match ? pos + match[0].length : -1;\n    }\n    next() {\n        let start = this.match(this.end, /^(\\s|\\/\\/.*|\\/\\*[^]*?\\*\\/)*/);\n        if (start == this.string.length)\n            return this.set(\"eof\", null, start, start);\n        let next = this.string[start];\n        if (next == '\"') {\n            let end = this.match(start + 1, /^(\\\\.|[^\"\\\\])*\"/);\n            if (end == -1)\n                this.raise(\"Unterminated string literal\", start);\n            return this.set(\"string\", readString(this.string.slice(start + 1, end - 1)), start, end);\n        }\n        else if (next == \"'\") {\n            let end = this.match(start + 1, /^(\\\\.|[^'\\\\])*'/);\n            if (end == -1)\n                this.raise(\"Unterminated string literal\", start);\n            return this.set(\"string\", readString(this.string.slice(start + 1, end - 1)), start, end);\n        }\n        else if (next == \"@\") {\n            word.lastIndex = start + 1;\n            let m = word.exec(this.string);\n            if (!m)\n                return this.raise(\"@ without a name\", start);\n            return this.set(\"at\", m[0], start, start + 1 + m[0].length);\n        }\n        else if ((next == \"$\" || next == \"!\") && this.string[start + 1] == \"[\") {\n            let end = this.match(start + 2, /^(?:\\\\.|[^\\]\\\\])*\\]/);\n            if (end == -1)\n                this.raise(\"Unterminated character set\", start);\n            return this.set(\"set\", this.string.slice(start + 2, end - 1), start, end);\n        }\n        else if (/[\\[\\]()!~+*?{}<>\\.,|:$=]/.test(next)) {\n            return this.set(next, null, start, start + 1);\n        }\n        else {\n            word.lastIndex = start;\n            let m = word.exec(this.string);\n            if (!m)\n                return this.raise(\"Unexpected character \" + JSON.stringify(next), start);\n            return this.set(\"id\", m[0], start, start + m[0].length);\n        }\n    }\n    set(type, value, start, end) {\n        this.type = type;\n        this.value = value;\n        this.start = start;\n        this.end = end;\n    }\n    eat(type, value = null) {\n        if (this.type == type && (value == null || this.value === value)) {\n            this.next();\n            return true;\n        }\n        else {\n            return false;\n        }\n    }\n    unexpected() {\n        return this.raise(`Unexpected token '${this.string.slice(this.start, this.end)}'`, this.start);\n    }\n    expect(type, value = null) {\n        let val = this.value;\n        if (this.type != type || !(value == null || val === value))\n            this.unexpected();\n        this.next();\n        return val;\n    }\n    parse() {\n        return parseGrammar(this);\n    }\n}\nfunction parseGrammar(input) {\n    let start = input.start;\n    let rules = [];\n    let prec = null;\n    let tokens = null;\n    let localTokens = [];\n    let mainSkip = null;\n    let scopedSkip = [];\n    let dialects = [];\n    let context = null;\n    let external = [];\n    let specialized = [];\n    let props = [];\n    let propSources = [];\n    let tops = [];\n    let sawTop = false;\n    let autoDelim = false;\n    while (input.type != \"eof\") {\n        let start = input.start;\n        if (input.eat(\"at\", \"top\")) {\n            if (input.type != \"id\")\n                input.raise(`Top rules must have a name`, input.start);\n            tops.push(parseRule(input, parseIdent(input)));\n            sawTop = true;\n        }\n        else if (input.type == \"at\" && input.value == \"tokens\") {\n            if (tokens)\n                input.raise(`Multiple @tokens declaractions`, input.start);\n            else\n                tokens = parseTokens(input);\n        }\n        else if (input.eat(\"at\", \"local\")) {\n            input.expect(\"id\", \"tokens\");\n            localTokens.push(parseLocalTokens(input, start));\n        }\n        else if (input.eat(\"at\", \"context\")) {\n            if (context)\n                input.raise(`Multiple @context declarations`, start);\n            let id = parseIdent(input);\n            input.expect(\"id\", \"from\");\n            let source = input.expect(\"string\");\n            context = new ContextDeclaration(start, id, source);\n        }\n        else if (input.eat(\"at\", \"external\")) {\n            if (input.eat(\"id\", \"tokens\"))\n                external.push(parseExternalTokens(input, start));\n            else if (input.eat(\"id\", \"prop\"))\n                props.push(parseExternalProp(input, start));\n            else if (input.eat(\"id\", \"extend\"))\n                specialized.push(parseExternalSpecialize(input, \"extend\", start));\n            else if (input.eat(\"id\", \"specialize\"))\n                specialized.push(parseExternalSpecialize(input, \"specialize\", start));\n            else if (input.eat(\"id\", \"propSource\"))\n                propSources.push(parseExternalPropSource(input, start));\n            else\n                input.unexpected();\n        }\n        else if (input.eat(\"at\", \"dialects\")) {\n            input.expect(\"{\");\n            for (let first = true; !input.eat(\"}\"); first = false) {\n                if (!first)\n                    input.eat(\",\");\n                dialects.push(parseIdent(input));\n            }\n        }\n        else if (input.type == \"at\" && input.value == \"precedence\") {\n            if (prec)\n                input.raise(`Multiple precedence declarations`, input.start);\n            prec = parsePrecedence(input);\n        }\n        else if (input.eat(\"at\", \"detectDelim\")) {\n            autoDelim = true;\n        }\n        else if (input.eat(\"at\", \"skip\")) {\n            let skip = parseBracedExpr(input);\n            if (input.type == \"{\") {\n                input.next();\n                let rules = [], topRules = [];\n                while (!input.eat(\"}\")) {\n                    if (input.eat(\"at\", \"top\")) {\n                        topRules.push(parseRule(input, parseIdent(input)));\n                        sawTop = true;\n                    }\n                    else {\n                        rules.push(parseRule(input));\n                    }\n                }\n                scopedSkip.push({ expr: skip, topRules, rules });\n            }\n            else {\n                if (mainSkip)\n                    input.raise(`Multiple top-level skip declarations`, input.start);\n                mainSkip = skip;\n            }\n        }\n        else {\n            rules.push(parseRule(input));\n        }\n    }\n    if (!sawTop)\n        return input.raise(`Missing @top declaration`);\n    return new GrammarDeclaration(start, rules, tops, tokens, localTokens, context, external, specialized, propSources, prec, mainSkip, scopedSkip, dialects, props, autoDelim);\n}\nfunction parseRule(input, named) {\n    let start = named ? named.start : input.start;\n    let id = named || parseIdent(input);\n    let props = parseProps(input);\n    let params = [];\n    if (input.eat(\"<\"))\n        while (!input.eat(\">\")) {\n            if (params.length)\n                input.expect(\",\");\n            params.push(parseIdent(input));\n        }\n    let expr = parseBracedExpr(input);\n    return new RuleDeclaration(start, id, props, params, expr);\n}\nfunction parseProps(input) {\n    if (input.type != \"[\")\n        return none$2;\n    let props = [];\n    input.expect(\"[\");\n    while (!input.eat(\"]\")) {\n        if (props.length)\n            input.expect(\",\");\n        props.push(parseProp(input));\n    }\n    return props;\n}\nfunction parseProp(input) {\n    let start = input.start, value = [], name = input.value, at = input.type == \"at\";\n    if (!input.eat(\"at\") && !input.eat(\"id\"))\n        input.unexpected();\n    if (input.eat(\"=\"))\n        for (;;) {\n            if (input.type == \"string\" || input.type == \"id\") {\n                value.push(new PropPart(input.start, input.value, null));\n                input.next();\n            }\n            else if (input.eat(\".\")) {\n                value.push(new PropPart(input.start, \".\", null));\n            }\n            else if (input.eat(\"{\")) {\n                value.push(new PropPart(input.start, null, input.expect(\"id\")));\n                input.expect(\"}\");\n            }\n            else {\n                break;\n            }\n        }\n    return new Prop(start, at, name, value);\n}\nfunction parseBracedExpr(input) {\n    input.expect(\"{\");\n    let expr = parseExprChoice(input);\n    input.expect(\"}\");\n    return expr;\n}\nconst SET_MARKER = \"\\ufdda\"; // (Invalid unicode character)\nfunction parseExprInner(input) {\n    let start = input.start;\n    if (input.eat(\"(\")) {\n        if (input.eat(\")\"))\n            return new SequenceExpression(start, none$2, [none$2, none$2]);\n        let expr = parseExprChoice(input);\n        input.expect(\")\");\n        return expr;\n    }\n    else if (input.type == \"string\") {\n        let value = input.value;\n        input.next();\n        if (value.length == 0)\n            return new SequenceExpression(start, none$2, [none$2, none$2]);\n        return new LiteralExpression(start, value);\n    }\n    else if (input.eat(\"id\", \"_\")) {\n        return new AnyExpression(start);\n    }\n    else if (input.type == \"set\") {\n        let content = input.value, invert = input.string[input.start] == \"!\";\n        let unescaped = readString(content.replace(/\\\\.|-|\"/g, (m) => {\n            return m == \"-\" ? SET_MARKER : m == '\"' ? '\\\\\"' : m;\n        }));\n        let ranges = [];\n        for (let pos = 0; pos < unescaped.length;) {\n            let code = unescaped.codePointAt(pos);\n            pos += code > 0xffff ? 2 : 1;\n            if (pos < unescaped.length - 1 && unescaped[pos] == SET_MARKER) {\n                let end = unescaped.codePointAt(pos + 1);\n                pos += end > 0xffff ? 3 : 2;\n                if (end < code)\n                    input.raise(\"Invalid character range\", input.start);\n                addRange(input, ranges, code, end + 1);\n            }\n            else {\n                if (code == SET_MARKER.charCodeAt(0))\n                    code = 45;\n                addRange(input, ranges, code, code + 1);\n            }\n        }\n        input.next();\n        return new SetExpression(start, ranges.sort((a, b) => a[0] - b[0]), invert);\n    }\n    else if (input.type == \"at\" && (input.value == \"specialize\" || input.value == \"extend\")) {\n        let { start, value } = input;\n        input.next();\n        let props = parseProps(input);\n        input.expect(\"<\");\n        let token = parseExprChoice(input), content;\n        if (input.eat(\",\")) {\n            content = parseExprChoice(input);\n        }\n        else if (token instanceof LiteralExpression) {\n            content = token;\n        }\n        else {\n            input.raise(`@${value} requires two arguments when its first argument isn't a literal string`);\n        }\n        input.expect(\">\");\n        return new SpecializeExpression(start, value, props, token, content);\n    }\n    else if (input.type == \"at\" && CharClasses.hasOwnProperty(input.value)) {\n        let cls = new CharClass(input.start, input.value);\n        input.next();\n        return cls;\n    }\n    else if (input.type == \"[\") {\n        let rule = parseRule(input, new Identifier(start, \"_anon\"));\n        if (rule.params.length)\n            input.raise(`Inline rules can't have parameters`, rule.start);\n        return new InlineRuleExpression(start, rule);\n    }\n    else {\n        let id = parseIdent(input);\n        if (input.type == \"[\" || input.type == \"{\") {\n            let rule = parseRule(input, id);\n            if (rule.params.length)\n                input.raise(`Inline rules can't have parameters`, rule.start);\n            return new InlineRuleExpression(start, rule);\n        }\n        else {\n            if (input.eat(\".\") && id.name == \"std\" && CharClasses.hasOwnProperty(input.value)) {\n                let cls = new CharClass(start, input.value);\n                input.next();\n                return cls;\n            }\n            return new NameExpression(start, id, parseArgs(input));\n        }\n    }\n}\nfunction parseArgs(input) {\n    let args = [];\n    if (input.eat(\"<\"))\n        while (!input.eat(\">\")) {\n            if (args.length)\n                input.expect(\",\");\n            args.push(parseExprChoice(input));\n        }\n    return args;\n}\nfunction addRange(input, ranges, from, to) {\n    if (!ranges.every(([a, b]) => b <= from || a >= to))\n        input.raise(\"Overlapping character range\", input.start);\n    ranges.push([from, to]);\n}\nfunction parseExprSuffix(input) {\n    let start = input.start;\n    let expr = parseExprInner(input);\n    for (;;) {\n        let kind = input.type;\n        if (input.eat(\"*\") || input.eat(\"?\") || input.eat(\"+\"))\n            expr = new RepeatExpression(start, expr, kind);\n        else\n            return expr;\n    }\n}\nfunction endOfSequence(input) {\n    return input.type == \"}\" || input.type == \")\" || input.type == \"|\" || input.type == \"/\" ||\n        input.type == \"/\\\\\" || input.type == \"{\" || input.type == \",\" || input.type == \">\";\n}\nfunction parseExprSequence(input) {\n    let start = input.start, exprs = [], markers = [none$2];\n    do {\n        // Add markers at this position\n        for (;;) {\n            let localStart = input.start, markerType;\n            if (input.eat(\"~\"))\n                markerType = \"ambig\";\n            else if (input.eat(\"!\"))\n                markerType = \"prec\";\n            else\n                break;\n            markers[markers.length - 1] =\n                markers[markers.length - 1].concat(new ConflictMarker(localStart, parseIdent(input), markerType));\n        }\n        if (endOfSequence(input))\n            break;\n        exprs.push(parseExprSuffix(input));\n        markers.push(none$2);\n    } while (!endOfSequence(input));\n    if (exprs.length == 1 && markers.every(ms => ms.length == 0))\n        return exprs[0];\n    return new SequenceExpression(start, exprs, markers, !exprs.length);\n}\nfunction parseExprChoice(input) {\n    let start = input.start, left = parseExprSequence(input);\n    if (!input.eat(\"|\"))\n        return left;\n    let exprs = [left];\n    do {\n        exprs.push(parseExprSequence(input));\n    } while (input.eat(\"|\"));\n    let empty = exprs.find(s => s instanceof SequenceExpression && s.empty);\n    if (empty)\n        input.raise(\"Empty expression in choice operator. If this is intentional, use () to make it explicit.\", empty.start);\n    return new ChoiceExpression(start, exprs);\n}\nfunction parseIdent(input) {\n    if (input.type != \"id\")\n        input.unexpected();\n    let start = input.start, name = input.value;\n    input.next();\n    return new Identifier(start, name);\n}\nfunction parsePrecedence(input) {\n    let start = input.start;\n    input.next();\n    input.expect(\"{\");\n    let items = [];\n    while (!input.eat(\"}\")) {\n        if (items.length)\n            input.eat(\",\");\n        items.push({\n            id: parseIdent(input),\n            type: input.eat(\"at\", \"left\") ? \"left\" : input.eat(\"at\", \"right\") ? \"right\" : input.eat(\"at\", \"cut\") ? \"cut\" : null\n        });\n    }\n    return new PrecDeclaration(start, items);\n}\nfunction parseTokens(input) {\n    let start = input.start;\n    input.next();\n    input.expect(\"{\");\n    let tokenRules = [];\n    let literals = [];\n    let precedences = [];\n    let conflicts = [];\n    while (!input.eat(\"}\")) {\n        if (input.type == \"at\" && input.value == \"precedence\") {\n            precedences.push(parseTokenPrecedence(input));\n        }\n        else if (input.type == \"at\" && input.value == \"conflict\") {\n            conflicts.push(parseTokenConflict(input));\n        }\n        else if (input.type == \"string\") {\n            literals.push(new LiteralDeclaration(input.start, input.expect(\"string\"), parseProps(input)));\n        }\n        else {\n            tokenRules.push(parseRule(input));\n        }\n    }\n    return new TokenDeclaration(start, precedences, conflicts, tokenRules, literals);\n}\nfunction parseLocalTokens(input, start) {\n    input.expect(\"{\");\n    let tokenRules = [];\n    let precedences = [];\n    let fallback = null;\n    while (!input.eat(\"}\")) {\n        if (input.type == \"at\" && input.value == \"precedence\") {\n            precedences.push(parseTokenPrecedence(input));\n        }\n        else if (input.eat(\"at\", \"else\") && !fallback) {\n            fallback = { id: parseIdent(input), props: parseProps(input) };\n        }\n        else {\n            tokenRules.push(parseRule(input));\n        }\n    }\n    return new LocalTokenDeclaration(start, precedences, tokenRules, fallback);\n}\nfunction parseTokenPrecedence(input) {\n    let start = input.start;\n    input.next();\n    input.expect(\"{\");\n    let tokens = [];\n    while (!input.eat(\"}\")) {\n        if (tokens.length)\n            input.eat(\",\");\n        let expr = parseExprInner(input);\n        if (expr instanceof LiteralExpression || expr instanceof NameExpression)\n            tokens.push(expr);\n        else\n            input.raise(`Invalid expression in token precedences`, expr.start);\n    }\n    return new TokenPrecDeclaration(start, tokens);\n}\nfunction parseTokenConflict(input) {\n    let start = input.start;\n    input.next();\n    input.expect(\"{\");\n    let a = parseExprInner(input);\n    if (!(a instanceof LiteralExpression || a instanceof NameExpression))\n        input.raise(`Invalid expression in token conflict`, a.start);\n    input.eat(\",\");\n    let b = parseExprInner(input);\n    if (!(b instanceof LiteralExpression || b instanceof NameExpression))\n        input.raise(`Invalid expression in token conflict`, b.start);\n    input.expect(\"}\");\n    return new TokenConflictDeclaration(start, a, b);\n}\nfunction parseExternalTokenSet(input) {\n    let tokens = [];\n    input.expect(\"{\");\n    while (!input.eat(\"}\")) {\n        if (tokens.length)\n            input.eat(\",\");\n        let id = parseIdent(input);\n        let props = parseProps(input);\n        tokens.push({ id, props });\n    }\n    return tokens;\n}\nfunction parseExternalTokens(input, start) {\n    let id = parseIdent(input);\n    input.expect(\"id\", \"from\");\n    let from = input.expect(\"string\");\n    return new ExternalTokenDeclaration(start, id, from, parseExternalTokenSet(input));\n}\nfunction parseExternalSpecialize(input, type, start) {\n    let token = parseBracedExpr(input);\n    let id = parseIdent(input);\n    input.expect(\"id\", \"from\");\n    let from = input.expect(\"string\");\n    return new ExternalSpecializeDeclaration(start, type, token, id, from, parseExternalTokenSet(input));\n}\nfunction parseExternalPropSource(input, start) {\n    let id = parseIdent(input);\n    input.expect(\"id\", \"from\");\n    return new ExternalPropSourceDeclaration(start, id, input.expect(\"string\"));\n}\nfunction parseExternalProp(input, start) {\n    let externalID = parseIdent(input);\n    let id = input.eat(\"id\", \"as\") ? parseIdent(input) : externalID;\n    input.expect(\"id\", \"from\");\n    let from = input.expect(\"string\");\n    return new ExternalPropDeclaration(start, id, externalID, from);\n}\nfunction readString(string) {\n    let point = /\\\\(?:u\\{([\\da-f]+)\\}|u([\\da-f]{4})|x([\\da-f]{2})|([ntbrf0])|(.))|[^]/yig;\n    let out = \"\", m;\n    while (m = point.exec(string)) {\n        let [all, u1, u2, u3, single, unknown] = m;\n        if (u1 || u2 || u3)\n            out += String.fromCodePoint(parseInt(u1 || u2 || u3, 16));\n        else if (single)\n            out += single == \"n\" ? \"\\n\" : single == \"t\" ? \"\\t\" : single == \"0\" ? \"\\0\" : single == \"r\" ? \"\\r\" : single == \"f\" ? \"\\f\" : \"\\b\";\n        else if (unknown)\n            out += unknown;\n        else\n            out += all;\n    }\n    return out;\n}\n\nfunction hash(a, b) { return (a << 5) + a + b; }\nfunction hashString(h, s) {\n    for (let i = 0; i < s.length; i++)\n        h = hash(h, s.charCodeAt(i));\n    return h;\n}\n\nconst verbose = (typeof process != \"undefined\" && process.env.LOG) || \"\";\nconst timing = /\\btime\\b/.test(verbose);\nconst time = timing ? (label, f) => {\n    let t0 = Date.now();\n    let result = f();\n    console.log(`${label} (${((Date.now() - t0) / 1000).toFixed(2)}s)`);\n    return result;\n} : (_label, f) => f();\n\nclass Pos {\n    constructor(rule, pos, \n    // NOTE `ahead` and `ambigAhead` aren't mutated anymore after `finish()` has been called\n    ahead, ambigAhead, skipAhead, via) {\n        this.rule = rule;\n        this.pos = pos;\n        this.ahead = ahead;\n        this.ambigAhead = ambigAhead;\n        this.skipAhead = skipAhead;\n        this.via = via;\n        this.hash = 0;\n    }\n    finish() {\n        let h = hash(hash(this.rule.id, this.pos), this.skipAhead.hash);\n        for (let a of this.ahead)\n            h = hash(h, a.hash);\n        for (let group of this.ambigAhead)\n            h = hashString(h, group);\n        this.hash = h;\n        return this;\n    }\n    get next() {\n        return this.pos < this.rule.parts.length ? this.rule.parts[this.pos] : null;\n    }\n    advance() {\n        return new Pos(this.rule, this.pos + 1, this.ahead, this.ambigAhead, this.skipAhead, this.via).finish();\n    }\n    get skip() {\n        return this.pos == this.rule.parts.length ? this.skipAhead : this.rule.skip;\n    }\n    cmp(pos) {\n        return this.rule.cmp(pos.rule) || this.pos - pos.pos || this.skipAhead.hash - pos.skipAhead.hash ||\n            cmpSet(this.ahead, pos.ahead, (a, b) => a.cmp(b)) || cmpSet(this.ambigAhead, pos.ambigAhead, cmpStr);\n    }\n    eqSimple(pos) {\n        return pos.rule == this.rule && pos.pos == this.pos;\n    }\n    toString() {\n        let parts = this.rule.parts.map(t => t.name);\n        parts.splice(this.pos, 0, \"·\");\n        return `${this.rule.name} -> ${parts.join(\" \")}`;\n    }\n    eq(other) {\n        return this == other ||\n            this.hash == other.hash && this.rule == other.rule && this.pos == other.pos && this.skipAhead == other.skipAhead &&\n                sameSet(this.ahead, other.ahead) &&\n                sameSet(this.ambigAhead, other.ambigAhead);\n    }\n    trail(maxLen = 60) {\n        let result = [];\n        for (let pos = this; pos; pos = pos.via) {\n            for (let i = pos.pos - 1; i >= 0; i--)\n                result.push(pos.rule.parts[i]);\n        }\n        let value = result.reverse().join(\" \");\n        if (value.length > maxLen)\n            value = value.slice(value.length - maxLen).replace(/.*? /, \"… \");\n        return value;\n    }\n    conflicts(pos = this.pos) {\n        let result = this.rule.conflicts[pos];\n        if (pos == this.rule.parts.length && this.ambigAhead.length)\n            result = result.join(new Conflicts(0, this.ambigAhead));\n        return result;\n    }\n    static addOrigins(group, context) {\n        let result = group.slice();\n        for (let i = 0; i < result.length; i++) {\n            let next = result[i];\n            if (next.pos == 0)\n                for (let pos of context) {\n                    if (pos.next == next.rule.name && !result.includes(pos))\n                        result.push(pos);\n                }\n        }\n        return result;\n    }\n}\nfunction conflictsAt(group) {\n    let result = Conflicts.none;\n    for (let pos of group)\n        result = result.join(pos.conflicts());\n    return result;\n}\n// Applies automatic action precedence based on repeat productions.\n// These are left-associative, so reducing the `R -> R R` rule has\n// higher precedence.\nfunction compareRepeatPrec(a, b) {\n    for (let pos of a)\n        if (pos.rule.name.repeated) {\n            for (let posB of b)\n                if (posB.rule.name == pos.rule.name) {\n                    if (pos.rule.isRepeatWrap && pos.pos == 2)\n                        return 1;\n                    if (posB.rule.isRepeatWrap && posB.pos == 2)\n                        return -1;\n                }\n        }\n    return 0;\n}\nfunction cmpStr(a, b) {\n    return a < b ? -1 : a > b ? 1 : 0;\n}\nfunction termsAhead(rule, pos, after, first) {\n    let found = [];\n    for (let i = pos + 1; i < rule.parts.length; i++) {\n        let next = rule.parts[i], cont = false;\n        if (next.terminal) {\n            addTo(next, found);\n        }\n        else\n            for (let term of first[next.name]) {\n                if (term == null)\n                    cont = true;\n                else\n                    addTo(term, found);\n            }\n        if (!cont)\n            return found;\n    }\n    for (let a of after)\n        addTo(a, found);\n    return found;\n}\nfunction eqSet(a, b) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (!a[i].eq(b[i]))\n            return false;\n    return true;\n}\nfunction sameSet(a, b) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (a[i] != b[i])\n            return false;\n    return true;\n}\nclass Shift {\n    constructor(term, target) {\n        this.term = term;\n        this.target = target;\n    }\n    eq(other) { return other instanceof Shift && this.term == other.term && other.target.id == this.target.id; }\n    cmp(other) { return other instanceof Reduce ? -1 : this.term.id - other.term.id || this.target.id - other.target.id; }\n    matches(other, mapping) {\n        return other instanceof Shift && mapping[other.target.id] == mapping[this.target.id];\n    }\n    toString() { return \"s\" + this.target.id; }\n    map(mapping, states) {\n        let mapped = states[mapping[this.target.id]];\n        return mapped == this.target ? this : new Shift(this.term, mapped);\n    }\n}\nclass Reduce {\n    constructor(term, rule) {\n        this.term = term;\n        this.rule = rule;\n    }\n    eq(other) {\n        return other instanceof Reduce && this.term == other.term && other.rule.sameReduce(this.rule);\n    }\n    cmp(other) {\n        return other instanceof Shift ? 1 : this.term.id - other.term.id || this.rule.name.id - other.rule.name.id ||\n            this.rule.parts.length - other.rule.parts.length;\n    }\n    matches(other, mapping) {\n        return other instanceof Reduce && other.rule.sameReduce(this.rule);\n    }\n    toString() { return `${this.rule.name.name}(${this.rule.parts.length})`; }\n    map() { return this; }\n}\nfunction hashPositions(set) {\n    let h = 5381;\n    for (let pos of set)\n        h = hash(h, pos.hash);\n    return h;\n}\nclass ConflictContext {\n    constructor(first) {\n        this.first = first;\n        this.conflicts = [];\n    }\n}\nclass State {\n    constructor(id, set, flags = 0, skip, hash = hashPositions(set), startRule = null) {\n        this.id = id;\n        this.set = set;\n        this.flags = flags;\n        this.skip = skip;\n        this.hash = hash;\n        this.startRule = startRule;\n        this.actions = [];\n        this.actionPositions = [];\n        this.goto = [];\n        this.tokenGroup = -1;\n        this.defaultReduce = null;\n        this._actionsByTerm = null;\n    }\n    toString() {\n        let actions = this.actions.map(t => t.term + \"=\" + t).join(\",\") +\n            (this.goto.length ? \" | \" + this.goto.map(g => g.term + \"=\" + g).join(\",\") : \"\");\n        return this.id + \": \" + this.set.filter(p => p.pos > 0).join() +\n            (this.defaultReduce ? `\\n  always ${this.defaultReduce.name}(${this.defaultReduce.parts.length})`\n                : actions.length ? \"\\n  \" + actions : \"\");\n    }\n    addActionInner(value, positions) {\n        check: for (let i = 0; i < this.actions.length; i++) {\n            let action = this.actions[i];\n            if (action.term == value.term) {\n                if (action.eq(value))\n                    return null;\n                let fullPos = Pos.addOrigins(positions, this.set), actionFullPos = Pos.addOrigins(this.actionPositions[i], this.set);\n                let conflicts = conflictsAt(fullPos), actionConflicts = conflictsAt(actionFullPos);\n                let diff = compareRepeatPrec(fullPos, actionFullPos) || conflicts.precedence - actionConflicts.precedence;\n                if (diff > 0) { // Drop the existing action\n                    this.actions.splice(i, 1);\n                    this.actionPositions.splice(i, 1);\n                    i--;\n                    continue check;\n                }\n                else if (diff < 0) { // Drop this one\n                    return null;\n                }\n                else if (conflicts.ambigGroups.some(g => actionConflicts.ambigGroups.includes(g))) { // Explicitly allowed ambiguity\n                    continue check;\n                }\n                else { // Not resolved\n                    return action;\n                }\n            }\n        }\n        this.actions.push(value);\n        this.actionPositions.push(positions);\n        return null;\n    }\n    addAction(value, positions, context) {\n        let conflict = this.addActionInner(value, positions);\n        if (conflict) {\n            let conflictPos = this.actionPositions[this.actions.indexOf(conflict)][0];\n            let rules = [positions[0].rule.name, conflictPos.rule.name];\n            if (context.conflicts.some(c => c.rules.some(r => rules.includes(r))))\n                return;\n            let error;\n            if (conflict instanceof Shift)\n                error = `shift/reduce conflict between\\n  ${conflictPos}\\nand\\n  ${positions[0].rule}`;\n            else\n                error = `reduce/reduce conflict between\\n  ${conflictPos.rule}\\nand\\n  ${positions[0].rule}`;\n            error += `\\nWith input:\\n  ${positions[0].trail(70)} · ${value.term} …`;\n            if (conflict instanceof Shift)\n                error += findConflictShiftSource(positions[0], conflict.term, context.first);\n            error += findConflictOrigin(conflictPos, positions[0]);\n            context.conflicts.push(new Conflict(error, rules));\n        }\n    }\n    getGoto(term) {\n        return this.goto.find(a => a.term == term);\n    }\n    hasSet(set) {\n        return eqSet(this.set, set);\n    }\n    actionsByTerm() {\n        let result = this._actionsByTerm;\n        if (!result) {\n            this._actionsByTerm = result = Object.create(null);\n            for (let action of this.actions)\n                (result[action.term.id] || (result[action.term.id] = [])).push(action);\n        }\n        return result;\n    }\n    finish() {\n        if (this.actions.length) {\n            let first = this.actions[0];\n            if (first instanceof Reduce) {\n                let { rule } = first;\n                if (this.actions.every(a => a instanceof Reduce && a.rule.sameReduce(rule)))\n                    this.defaultReduce = rule;\n            }\n        }\n        this.actions.sort((a, b) => a.cmp(b));\n        this.goto.sort((a, b) => a.cmp(b));\n    }\n    eq(other) {\n        let dThis = this.defaultReduce, dOther = other.defaultReduce;\n        if (dThis || dOther)\n            return dThis && dOther ? dThis.sameReduce(dOther) : false;\n        return this.skip == other.skip &&\n            this.tokenGroup == other.tokenGroup &&\n            eqSet(this.actions, other.actions) &&\n            eqSet(this.goto, other.goto);\n    }\n}\nfunction closure(set, first) {\n    let added = [], redo = [];\n    function addFor(name, ahead, ambigAhead, skipAhead, via) {\n        for (let rule of name.rules) {\n            let add = added.find(a => a.rule == rule);\n            if (!add) {\n                let existing = set.find(p => p.pos == 0 && p.rule == rule);\n                add = existing ? new Pos(rule, 0, existing.ahead.slice(), existing.ambigAhead, existing.skipAhead, existing.via)\n                    : new Pos(rule, 0, [], none$1, skipAhead, via);\n                added.push(add);\n            }\n            if (add.skipAhead != skipAhead)\n                throw new GenError(\"Inconsistent skip sets after \" + via.trail());\n            add.ambigAhead = union(add.ambigAhead, ambigAhead);\n            for (let term of ahead)\n                if (!add.ahead.includes(term)) {\n                    add.ahead.push(term);\n                    if (add.rule.parts.length && !add.rule.parts[0].terminal)\n                        addTo(add, redo);\n                }\n        }\n    }\n    for (let pos of set) {\n        let next = pos.next;\n        if (next && !next.terminal)\n            addFor(next, termsAhead(pos.rule, pos.pos, pos.ahead, first), pos.conflicts(pos.pos + 1).ambigGroups, pos.pos == pos.rule.parts.length - 1 ? pos.skipAhead : pos.rule.skip, pos);\n    }\n    while (redo.length) {\n        let add = redo.pop();\n        addFor(add.rule.parts[0], termsAhead(add.rule, 0, add.ahead, first), union(add.rule.conflicts[1].ambigGroups, add.rule.parts.length == 1 ? add.ambigAhead : none$1), add.rule.parts.length == 1 ? add.skipAhead : add.rule.skip, add);\n    }\n    let result = set.slice();\n    for (let add of added) {\n        add.ahead.sort((a, b) => a.hash - b.hash);\n        add.finish();\n        let origIndex = set.findIndex(p => p.pos == 0 && p.rule == add.rule);\n        if (origIndex > -1)\n            result[origIndex] = add;\n        else\n            result.push(add);\n    }\n    return result.sort((a, b) => a.cmp(b));\n}\nfunction addTo(value, array) {\n    if (!array.includes(value))\n        array.push(value);\n}\nfunction computeFirstSets(terms) {\n    let table = Object.create(null);\n    for (let t of terms.terms)\n        if (!t.terminal)\n            table[t.name] = [];\n    for (;;) {\n        let change = false;\n        for (let nt of terms.terms)\n            if (!nt.terminal)\n                for (let rule of nt.rules) {\n                    let set = table[nt.name];\n                    let found = false, startLen = set.length;\n                    for (let part of rule.parts) {\n                        found = true;\n                        if (part.terminal) {\n                            addTo(part, set);\n                        }\n                        else {\n                            for (let t of table[part.name]) {\n                                if (t == null)\n                                    found = false;\n                                else\n                                    addTo(t, set);\n                            }\n                        }\n                        if (found)\n                            break;\n                    }\n                    if (!found)\n                        addTo(null, set);\n                    if (set.length > startLen)\n                        change = true;\n                }\n        if (!change)\n            return table;\n    }\n}\nclass Core {\n    constructor(set, state) {\n        this.set = set;\n        this.state = state;\n    }\n}\nclass Conflict {\n    constructor(error, rules) {\n        this.error = error;\n        this.rules = rules;\n    }\n}\nfunction findConflictOrigin(a, b) {\n    if (a.eqSimple(b))\n        return \"\";\n    function via(root, start) {\n        let hist = [];\n        for (let p = start.via; !p.eqSimple(root); p = p.via)\n            hist.push(p);\n        if (!hist.length)\n            return \"\";\n        hist.unshift(start);\n        return hist.reverse().map((p, i) => \"\\n\" + \"  \".repeat(i + 1) + (p == start ? \"\" : \"via \") + p).join(\"\");\n    }\n    for (let p = a; p; p = p.via)\n        for (let p2 = b; p2; p2 = p2.via) {\n            if (p.eqSimple(p2))\n                return \"\\nShared origin: \" + p + via(p, a) + via(p, b);\n        }\n    return \"\";\n}\n// Search for the reason that a given 'after' token exists at the\n// given pos, by scanning up the trail of positions. Because the `via`\n// link is only one source of a pos, of potentially many, this\n// requires a re-simulation of the whole path up to the pos.\nfunction findConflictShiftSource(conflictPos, termAfter, first) {\n    let pos = conflictPos, path = [];\n    for (;;) {\n        for (let i = pos.pos - 1; i >= 0; i--)\n            path.push(pos.rule.parts[i]);\n        if (!pos.via)\n            break;\n        pos = pos.via;\n    }\n    path.reverse();\n    let seen = new Set();\n    function explore(pos, i, hasMatch) {\n        if (i == path.length && hasMatch && !pos.next)\n            return `\\nThe reduction of ${conflictPos.rule.name} is allowed before ${termAfter} because of this rule:\\n  ${hasMatch}`;\n        for (let next; next = pos.next;) {\n            if (i < path.length && next == path[i]) {\n                let inner = explore(pos.advance(), i + 1, hasMatch);\n                if (inner)\n                    return inner;\n            }\n            let after = pos.rule.parts[pos.pos + 1], match = pos.pos + 1 == pos.rule.parts.length ? hasMatch : null;\n            if (after && (after.terminal ? after == termAfter : first[after.name].includes(termAfter)))\n                match = pos.advance();\n            for (let rule of next.rules) {\n                let hash = (rule.id << 5) + i + (match ? 555 : 0);\n                if (!seen.has(hash)) {\n                    seen.add(hash);\n                    let inner = explore(new Pos(rule, 0, [], [], next, pos), i, match);\n                    if (inner)\n                        return inner;\n                }\n            }\n            if (!next.terminal && first[next.name].includes(null))\n                pos = pos.advance();\n            else\n                break;\n        }\n        return \"\";\n    }\n    return explore(pos, 0, null);\n}\n// Builds a full LR(1) automaton\nfunction buildFullAutomaton(terms, startTerms, first) {\n    let states = [], statesBySetHash = {};\n    let cores = {};\n    let t0 = Date.now();\n    function getState(core, top) {\n        if (core.length == 0)\n            return null;\n        let coreHash = hashPositions(core), byHash = cores[coreHash];\n        let skip;\n        for (let pos of core) {\n            if (!skip)\n                skip = pos.skip;\n            else if (skip != pos.skip)\n                throw new GenError(\"Inconsistent skip sets after \" + pos.trail());\n        }\n        if (byHash)\n            for (let known of byHash)\n                if (eqSet(core, known.set)) {\n                    if (known.state.skip != skip)\n                        throw new GenError(\"Inconsistent skip sets after \" + known.set[0].trail());\n                    return known.state;\n                }\n        let set = closure(core, first);\n        let hash = hashPositions(set), forHash = statesBySetHash[hash] || (statesBySetHash[hash] = []);\n        let found;\n        if (!top)\n            for (let state of forHash)\n                if (state.hasSet(set))\n                    found = state;\n        if (!found) {\n            found = new State(states.length, set, 0, skip, hash, top);\n            forHash.push(found);\n            states.push(found);\n            if (timing && states.length % 500 == 0)\n                console.log(`${states.length} states after ${((Date.now() - t0) / 1000).toFixed(2)}s`);\n        }\n        (cores[coreHash] || (cores[coreHash] = [])).push(new Core(core, found));\n        return found;\n    }\n    for (const startTerm of startTerms) {\n        const startSkip = startTerm.rules.length ? startTerm.rules[0].skip : terms.names[\"%noskip\"];\n        getState(startTerm.rules.map(rule => new Pos(rule, 0, [terms.eof], none$1, startSkip, null).finish()), startTerm);\n    }\n    let conflicts = new ConflictContext(first);\n    for (let filled = 0; filled < states.length; filled++) {\n        let state = states[filled];\n        let byTerm = [], byTermPos = [], atEnd = [];\n        for (let pos of state.set) {\n            if (pos.pos == pos.rule.parts.length) {\n                if (!pos.rule.name.top)\n                    atEnd.push(pos);\n            }\n            else {\n                let next = pos.rule.parts[pos.pos];\n                let index = byTerm.indexOf(next);\n                if (index < 0) {\n                    byTerm.push(next);\n                    byTermPos.push([pos]);\n                }\n                else {\n                    byTermPos[index].push(pos);\n                }\n            }\n        }\n        for (let i = 0; i < byTerm.length; i++) {\n            let term = byTerm[i], positions = byTermPos[i].map(p => p.advance());\n            if (term.terminal) {\n                let set = applyCut(positions);\n                let next = getState(set);\n                if (next)\n                    state.addAction(new Shift(term, next), byTermPos[i], conflicts);\n            }\n            else {\n                let goto = getState(positions);\n                if (goto)\n                    state.goto.push(new Shift(term, goto));\n            }\n        }\n        let replaced = false;\n        for (let pos of atEnd)\n            for (let ahead of pos.ahead) {\n                let count = state.actions.length;\n                state.addAction(new Reduce(ahead, pos.rule), [pos], conflicts);\n                if (state.actions.length == count)\n                    replaced = true;\n            }\n        // If some actions were replaced by others, double-check whether\n        // goto entries are now superfluous (for example, in an operator\n        // precedence-related state that has a shift for `*` but only a\n        // reduce for `+`, we don't need a goto entry for rules that start\n        // with `+`)\n        if (replaced)\n            for (let i = 0; i < state.goto.length; i++) {\n                let start = first[state.goto[i].term.name];\n                if (!start.some(term => state.actions.some(a => a.term == term && (a instanceof Shift))))\n                    state.goto.splice(i--, 1);\n            }\n    }\n    if (conflicts.conflicts.length)\n        throw new GenError(conflicts.conflicts.map(c => c.error).join(\"\\n\\n\"));\n    // Resolve alwaysReduce and sort actions\n    for (let state of states)\n        state.finish();\n    if (timing)\n        console.log(`${states.length} states total.`);\n    return states;\n}\nfunction applyCut(set) {\n    let found = null, cut = 1;\n    for (let pos of set) {\n        let value = pos.rule.conflicts[pos.pos - 1].cut;\n        if (value < cut)\n            continue;\n        if (!found || value > cut) {\n            cut = value;\n            found = [];\n        }\n        found.push(pos);\n    }\n    return found || set;\n}\n// Verify that there are no conflicting actions or goto entries in the\n// two given states (using the state ID remapping provided in mapping)\nfunction canMerge(a, b, mapping) {\n    // If a goto for the same term differs, that makes the states\n    // incompatible\n    for (let goto of a.goto)\n        for (let other of b.goto) {\n            if (goto.term == other.term && mapping[goto.target.id] != mapping[other.target.id])\n                return false;\n        }\n    // If there is an action where a conflicting action exists in the\n    // other state, the merge is only allowed when both states have the\n    // exact same set of actions for this term.\n    let byTerm = b.actionsByTerm();\n    for (let action of a.actions) {\n        let setB = byTerm[action.term.id];\n        if (setB && setB.some(other => !other.matches(action, mapping))) {\n            if (setB.length == 1)\n                return false;\n            let setA = a.actionsByTerm()[action.term.id];\n            if (setA.length != setB.length || setA.some(a1 => !setB.some(a2 => a1.matches(a2, mapping))))\n                return false;\n        }\n    }\n    return true;\n}\nfunction mergeStates(states, mapping) {\n    let newStates = [];\n    for (let state of states) {\n        let newID = mapping[state.id];\n        if (!newStates[newID]) {\n            newStates[newID] = new State(newID, state.set, 0, state.skip, state.hash, state.startRule);\n            newStates[newID].tokenGroup = state.tokenGroup;\n            newStates[newID].defaultReduce = state.defaultReduce;\n        }\n    }\n    for (let state of states) {\n        let newID = mapping[state.id], target = newStates[newID];\n        target.flags |= state.flags;\n        for (let i = 0; i < state.actions.length; i++) {\n            let action = state.actions[i].map(mapping, newStates);\n            if (!target.actions.some(a => a.eq(action))) {\n                target.actions.push(action);\n                target.actionPositions.push(state.actionPositions[i]);\n            }\n        }\n        for (let goto of state.goto) {\n            let mapped = goto.map(mapping, newStates);\n            if (!target.goto.some(g => g.eq(mapped)))\n                target.goto.push(mapped);\n        }\n    }\n    return newStates;\n}\nclass Group {\n    constructor(origin, member) {\n        this.origin = origin;\n        this.members = [member];\n    }\n}\nfunction samePosSet(a, b) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (!a[i].eqSimple(b[i]))\n            return false;\n    return true;\n}\n// Collapse an LR(1) automaton to an LALR-like automaton\nfunction collapseAutomaton(states) {\n    let mapping = [], groups = [];\n    assignGroups: for (let i = 0; i < states.length; i++) {\n        let state = states[i];\n        if (!state.startRule)\n            for (let j = 0; j < groups.length; j++) {\n                let group = groups[j], other = states[group.members[0]];\n                if (state.tokenGroup == other.tokenGroup &&\n                    state.skip == other.skip &&\n                    !other.startRule &&\n                    samePosSet(state.set, other.set)) {\n                    group.members.push(i);\n                    mapping.push(j);\n                    continue assignGroups;\n                }\n            }\n        mapping.push(groups.length);\n        groups.push(new Group(groups.length, i));\n    }\n    function spill(groupIndex, index) {\n        let group = groups[groupIndex], state = states[group.members[index]];\n        let pop = group.members.pop();\n        if (index != group.members.length)\n            group.members[index] = pop;\n        for (let i = groupIndex + 1; i < groups.length; i++) {\n            mapping[state.id] = i;\n            if (groups[i].origin == group.origin &&\n                groups[i].members.every(id => canMerge(state, states[id], mapping))) {\n                groups[i].members.push(state.id);\n                return;\n            }\n        }\n        mapping[state.id] = groups.length;\n        groups.push(new Group(group.origin, state.id));\n    }\n    for (let pass = 1;; pass++) {\n        let conflicts = false, t0 = Date.now();\n        for (let g = 0, startLen = groups.length; g < startLen; g++) {\n            let group = groups[g];\n            for (let i = 0; i < group.members.length - 1; i++) {\n                for (let j = i + 1; j < group.members.length; j++) {\n                    let idA = group.members[i], idB = group.members[j];\n                    if (!canMerge(states[idA], states[idB], mapping)) {\n                        conflicts = true;\n                        spill(g, j--);\n                    }\n                }\n            }\n        }\n        if (timing)\n            console.log(`Collapse pass ${pass}${conflicts ? `` : `, done`} (${((Date.now() - t0) / 1000).toFixed(2)}s)`);\n        if (!conflicts)\n            return mergeStates(states, mapping);\n    }\n}\nfunction mergeIdentical(states) {\n    for (let pass = 1;; pass++) {\n        let mapping = [], didMerge = false, t0 = Date.now();\n        let newStates = [];\n        // Find states that either have the same alwaysReduce or the same\n        // actions, and merge them.\n        for (let i = 0; i < states.length; i++) {\n            let state = states[i];\n            let match = newStates.findIndex(s => state.eq(s));\n            if (match < 0) {\n                mapping[i] = newStates.length;\n                newStates.push(state);\n            }\n            else {\n                mapping[i] = match;\n                didMerge = true;\n                let other = newStates[match], add = null;\n                for (let pos of state.set)\n                    if (!other.set.some(p => p.eqSimple(pos)))\n                        (add || (add = [])).push(pos);\n                if (add)\n                    other.set = add.concat(other.set).sort((a, b) => a.cmp(b));\n            }\n        }\n        if (timing)\n            console.log(`Merge identical pass ${pass}${didMerge ? \"\" : \", done\"} (${((Date.now() - t0) / 1000).toFixed(2)}s)`);\n        if (!didMerge)\n            return states;\n        // Make sure actions point at merged state objects\n        for (let state of newStates)\n            if (!state.defaultReduce) {\n                state.actions = state.actions.map(a => a.map(mapping, newStates));\n                state.goto = state.goto.map(a => a.map(mapping, newStates));\n            }\n        // Renumber ids\n        for (let i = 0; i < newStates.length; i++)\n            newStates[i].id = i;\n        states = newStates;\n    }\n}\nconst none$1 = [];\nfunction finishAutomaton(full) {\n    return mergeIdentical(collapseAutomaton(full));\n}\n\n// Encode numbers as groups of printable ascii characters\n//\n// - 0xffff, which is often used as placeholder, is encoded as \"~\"\n//\n// - The characters from \" \" (32) to \"}\" (125), excluding '\"' and\n//   \"\\\\\", indicate values from 0 to 92\n//\n// - The first bit in a 'digit' is used to indicate whether this is\n//   the end of a number.\n//\n// - That leaves 46 other values, which are actually significant.\n//\n// - The digits in a number are ordered from high to low significance.\nfunction digitToChar(digit) {\n    let ch = digit + 32 /* Encode.Start */;\n    if (ch >= 34 /* Encode.Gap1 */)\n        ch++;\n    if (ch >= 92 /* Encode.Gap2 */)\n        ch++;\n    return String.fromCharCode(ch);\n}\nfunction encode(value, max = 0xffff) {\n    if (value > max)\n        throw new Error(\"Trying to encode a number that's too big: \" + value);\n    if (value == 65535 /* Encode.BigVal */)\n        return String.fromCharCode(126 /* Encode.BigValCode */);\n    let result = \"\";\n    for (let first = 46 /* Encode.Base */;; first = 0) {\n        let low = value % 46 /* Encode.Base */, rest = value - low;\n        result = digitToChar(low + first) + result;\n        if (rest == 0)\n            break;\n        value = rest / 46 /* Encode.Base */;\n    }\n    return result;\n}\nfunction encodeArray(values, max = 0xffff) {\n    let result = '\"' + encode(values.length, 0xffffffff);\n    for (let i = 0; i < values.length; i++)\n        result += encode(values[i], max);\n    result += '\"';\n    return result;\n}\n\nconst none = [];\nclass Parts {\n    constructor(terms, conflicts) {\n        this.terms = terms;\n        this.conflicts = conflicts;\n    }\n    concat(other) {\n        if (this == Parts.none)\n            return other;\n        if (other == Parts.none)\n            return this;\n        let conflicts = null;\n        if (this.conflicts || other.conflicts) {\n            conflicts = this.conflicts ? this.conflicts.slice() : this.ensureConflicts();\n            let otherConflicts = other.ensureConflicts();\n            conflicts[conflicts.length - 1] = conflicts[conflicts.length - 1].join(otherConflicts[0]);\n            for (let i = 1; i < otherConflicts.length; i++)\n                conflicts.push(otherConflicts[i]);\n        }\n        return new Parts(this.terms.concat(other.terms), conflicts);\n    }\n    withConflicts(pos, conflicts) {\n        if (conflicts == Conflicts.none)\n            return this;\n        let array = this.conflicts ? this.conflicts.slice() : this.ensureConflicts();\n        array[pos] = array[pos].join(conflicts);\n        return new Parts(this.terms, array);\n    }\n    ensureConflicts() {\n        if (this.conflicts)\n            return this.conflicts;\n        let empty = [];\n        for (let i = 0; i <= this.terms.length; i++)\n            empty.push(Conflicts.none);\n        return empty;\n    }\n}\nParts.none = new Parts(none, null);\nfunction p(...terms) { return new Parts(terms, null); }\nclass BuiltRule {\n    constructor(id, args, term) {\n        this.id = id;\n        this.args = args;\n        this.term = term;\n    }\n    matches(expr) {\n        return this.id == expr.id.name && exprsEq(expr.args, this.args);\n    }\n    matchesRepeat(expr) {\n        return this.id == \"+\" && exprEq(expr.expr, this.args[0]);\n    }\n}\nclass Builder {\n    constructor(text, options) {\n        this.options = options;\n        this.terms = new TermSet;\n        this.specialized = Object.create(null);\n        this.tokenOrigins = Object.create(null);\n        this.rules = [];\n        this.built = [];\n        this.ruleNames = Object.create(null);\n        this.namespaces = Object.create(null);\n        this.namedTerms = Object.create(null);\n        this.termTable = Object.create(null);\n        this.knownProps = Object.create(null);\n        this.dynamicRulePrecedences = [];\n        this.definedGroups = [];\n        this.astRules = [];\n        this.currentSkip = [];\n        time(\"Parse\", () => {\n            this.input = new Input(text, options.fileName);\n            this.ast = this.input.parse();\n        });\n        let NP = NodeProp;\n        for (let prop in NP) {\n            if (NP[prop] instanceof NodeProp && !NP[prop].perNode)\n                this.knownProps[prop] = { prop: NP[prop], source: { name: prop, from: null } };\n        }\n        for (let prop of this.ast.externalProps) {\n            this.knownProps[prop.id.name] = {\n                prop: this.options.externalProp ? this.options.externalProp(prop.id.name) : new NodeProp(),\n                source: { name: prop.externalID.name, from: prop.source }\n            };\n        }\n        this.dialects = this.ast.dialects.map(d => d.name);\n        this.tokens = new MainTokenSet(this, this.ast.tokens);\n        this.localTokens = this.ast.localTokens.map(g => new LocalTokenSet(this, g));\n        this.externalTokens = this.ast.externalTokens.map(ext => new ExternalTokenSet(this, ext));\n        this.externalSpecializers = this.ast.externalSpecializers.map(decl => new ExternalSpecializer(this, decl));\n        time(\"Build rules\", () => {\n            let noSkip = this.newName(\"%noskip\", true);\n            this.defineRule(noSkip, []);\n            let mainSkip = this.ast.mainSkip ? this.newName(\"%mainskip\", true) : noSkip;\n            let scopedSkip = [], topRules = [];\n            for (let rule of this.ast.rules)\n                this.astRules.push({ skip: mainSkip, rule });\n            for (let rule of this.ast.topRules)\n                topRules.push({ skip: mainSkip, rule });\n            for (let scoped of this.ast.scopedSkip) {\n                let skip = noSkip, found = this.ast.scopedSkip.findIndex((sc, i) => i < scopedSkip.length && exprEq(sc.expr, scoped.expr));\n                if (found > -1)\n                    skip = scopedSkip[found];\n                else if (this.ast.mainSkip && exprEq(scoped.expr, this.ast.mainSkip))\n                    skip = mainSkip;\n                else if (!isEmpty(scoped.expr))\n                    skip = this.newName(\"%skip\", true);\n                scopedSkip.push(skip);\n                for (let rule of scoped.rules)\n                    this.astRules.push({ skip, rule });\n                for (let rule of scoped.topRules)\n                    topRules.push({ skip, rule });\n            }\n            for (let { rule } of this.astRules) {\n                this.unique(rule.id);\n            }\n            this.currentSkip.push(noSkip);\n            this.skipRules = mainSkip == noSkip ? [mainSkip] : [noSkip, mainSkip];\n            if (mainSkip != noSkip)\n                this.defineRule(mainSkip, this.normalizeExpr(this.ast.mainSkip));\n            for (let i = 0; i < this.ast.scopedSkip.length; i++) {\n                let skip = scopedSkip[i];\n                if (!this.skipRules.includes(skip)) {\n                    this.skipRules.push(skip);\n                    if (skip != noSkip)\n                        this.defineRule(skip, this.normalizeExpr(this.ast.scopedSkip[i].expr));\n                }\n            }\n            this.currentSkip.pop();\n            for (let { rule, skip } of topRules.sort((a, b) => a.rule.start - b.rule.start)) {\n                this.unique(rule.id);\n                this.used(rule.id.name);\n                this.currentSkip.push(skip);\n                let { name, props } = this.nodeInfo(rule.props, \"a\", rule.id.name, none, none, rule.expr);\n                let term = this.terms.makeTop(name, props);\n                this.namedTerms[name] = term;\n                this.defineRule(term, this.normalizeExpr(rule.expr));\n                this.currentSkip.pop();\n            }\n            for (let ext of this.externalSpecializers)\n                ext.finish();\n            for (let { skip, rule } of this.astRules) {\n                if (this.ruleNames[rule.id.name] && isExported(rule) && !rule.params.length) {\n                    this.buildRule(rule, [], skip, false);\n                    if (rule.expr instanceof SequenceExpression && rule.expr.exprs.length == 0)\n                        this.used(rule.id.name);\n                }\n            }\n        });\n        for (let name in this.ruleNames) {\n            let value = this.ruleNames[name];\n            if (value)\n                this.warn(`Unused rule '${value.name}'`, value.start);\n        }\n        this.tokens.takePrecedences();\n        this.tokens.takeConflicts();\n        for (let lt of this.localTokens)\n            lt.takePrecedences();\n        for (let { name, group, rule } of this.definedGroups)\n            this.defineGroup(name, group, rule);\n        this.checkGroups();\n    }\n    unique(id) {\n        if (id.name in this.ruleNames)\n            this.raise(`Duplicate definition of rule '${id.name}'`, id.start);\n        this.ruleNames[id.name] = id;\n    }\n    used(name) {\n        this.ruleNames[name] = null;\n    }\n    newName(base, nodeName = null, props = {}) {\n        for (let i = nodeName ? 0 : 1;; i++) {\n            let name = i ? `${base}-${i}` : base;\n            if (!this.terms.names[name])\n                return this.terms.makeNonTerminal(name, nodeName === true ? null : nodeName, props);\n        }\n    }\n    prepareParser() {\n        let rules = time(\"Simplify rules\", () => simplifyRules(this.rules, [\n            ...this.skipRules,\n            ...this.terms.tops\n        ]));\n        let { nodeTypes, names: termNames, minRepeatTerm, maxTerm } = this.terms.finish(rules);\n        for (let prop in this.namedTerms)\n            this.termTable[prop] = this.namedTerms[prop].id;\n        if (/\\bgrammar\\b/.test(verbose))\n            console.log(rules.join(\"\\n\"));\n        let startTerms = this.terms.tops.slice();\n        let first = computeFirstSets(this.terms);\n        let skipInfo = this.skipRules.map((name, id) => {\n            let skip = [], startTokens = [], rules = [];\n            for (let rule of name.rules) {\n                if (!rule.parts.length)\n                    continue;\n                let start = rule.parts[0];\n                for (let t of start.terminal ? [start] : first[start.name] || [])\n                    if (t && !startTokens.includes(t))\n                        startTokens.push(t);\n                if (start.terminal && rule.parts.length == 1 && !rules.some(r => r != rule && r.parts[0] == start))\n                    skip.push(start);\n                else\n                    rules.push(rule);\n            }\n            name.rules = rules;\n            if (rules.length)\n                startTerms.push(name);\n            return { skip, rule: rules.length ? name : null, startTokens, id };\n        });\n        let fullTable = time(\"Build full automaton\", () => buildFullAutomaton(this.terms, startTerms, first));\n        let localTokens = this.localTokens\n            .map((grp, i) => grp.buildLocalGroup(fullTable, skipInfo, i));\n        let { tokenGroups, tokenPrec, tokenData } = time(\"Build token groups\", () => this.tokens.buildTokenGroups(fullTable, skipInfo, localTokens.length));\n        let table = time(\"Finish automaton\", () => finishAutomaton(fullTable));\n        let skipState = findSkipStates(table, this.terms.tops);\n        if (/\\blr\\b/.test(verbose))\n            console.log(table.join(\"\\n\"));\n        let specialized = [];\n        for (let ext of this.externalSpecializers)\n            specialized.push(ext);\n        for (let name in this.specialized)\n            specialized.push({ token: this.terms.names[name], table: buildSpecializeTable(this.specialized[name]) });\n        let tokStart = (tokenizer) => {\n            if (tokenizer instanceof ExternalTokenSet)\n                return tokenizer.ast.start;\n            return this.tokens.ast ? this.tokens.ast.start : -1;\n        };\n        let tokenizers = tokenGroups\n            .concat(this.externalTokens)\n            .sort((a, b) => tokStart(a) - tokStart(b))\n            .concat(localTokens);\n        let data = new DataBuilder;\n        let skipData = skipInfo.map(info => {\n            let actions = [];\n            for (let term of info.skip)\n                actions.push(term.id, 0, 262144 /* Action.StayFlag */ >> 16);\n            if (info.rule) {\n                let state = table.find(s => s.startRule == info.rule);\n                for (let action of state.actions)\n                    actions.push(action.term.id, state.id, 131072 /* Action.GotoFlag */ >> 16);\n            }\n            actions.push(65535 /* Seq.End */, 0 /* Seq.Done */);\n            return data.storeArray(actions);\n        });\n        let states = time(\"Finish states\", () => {\n            let states = new Uint32Array(table.length * 6 /* ParseState.Size */);\n            let forceReductions = this.computeForceReductions(table, skipInfo);\n            let finishCx = new FinishStateContext(tokenizers, data, states, skipData, skipInfo, table, this);\n            for (let s of table)\n                finishCx.finish(s, skipState(s.id), forceReductions[s.id]);\n            return states;\n        });\n        let dialects = Object.create(null);\n        for (let i = 0; i < this.dialects.length; i++)\n            dialects[this.dialects[i]] = data.storeArray((this.tokens.byDialect[i] || none).map(t => t.id).concat(65535 /* Seq.End */));\n        let dynamicPrecedences = null;\n        if (this.dynamicRulePrecedences.length) {\n            dynamicPrecedences = Object.create(null);\n            for (let { rule, prec } of this.dynamicRulePrecedences)\n                dynamicPrecedences[rule.id] = prec;\n        }\n        let topRules = Object.create(null);\n        for (let term of this.terms.tops)\n            topRules[term.nodeName] = [table.find(state => state.startRule == term).id, term.id];\n        let precTable = data.storeArray(tokenPrec.concat(65535 /* Seq.End */));\n        let { nodeProps, skippedTypes } = this.gatherNodeProps(nodeTypes);\n        return {\n            states,\n            stateData: data.finish(),\n            goto: computeGotoTable(table),\n            nodeNames: nodeTypes.filter(t => t.id < minRepeatTerm).map(t => t.nodeName).join(\" \"),\n            nodeProps,\n            skippedTypes,\n            maxTerm,\n            repeatNodeCount: nodeTypes.length - minRepeatTerm,\n            tokenizers,\n            tokenData,\n            topRules,\n            dialects,\n            dynamicPrecedences,\n            specialized,\n            tokenPrec: precTable,\n            termNames\n        };\n    }\n    getParser() {\n        let { states, stateData, goto, nodeNames, nodeProps: rawNodeProps, skippedTypes, maxTerm, repeatNodeCount, tokenizers, tokenData, topRules, dialects, dynamicPrecedences, specialized: rawSpecialized, tokenPrec, termNames } = this.prepareParser();\n        let specialized = rawSpecialized.map(v => {\n            if (v instanceof ExternalSpecializer) {\n                let ext = this.options.externalSpecializer(v.ast.id.name, this.termTable);\n                return {\n                    term: v.term.id,\n                    get: (value, stack) => (ext(value, stack) << 1) |\n                        (v.ast.type == \"extend\" ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */),\n                    external: ext,\n                    extend: v.ast.type == \"extend\"\n                };\n            }\n            else {\n                return { term: v.token.id, get: (value) => v.table[value] || -1 };\n            }\n        });\n        return LRParser.deserialize({\n            version: 14 /* File.Version */,\n            states,\n            stateData,\n            goto,\n            nodeNames,\n            maxTerm,\n            repeatNodeCount,\n            nodeProps: rawNodeProps.map(({ prop, terms }) => [this.knownProps[prop].prop, ...terms]),\n            propSources: !this.options.externalPropSource ? undefined\n                : this.ast.externalPropSources.map(s => this.options.externalPropSource(s.id.name)),\n            skippedNodes: skippedTypes,\n            tokenData,\n            tokenizers: tokenizers.map(tok => tok.create()),\n            context: !this.ast.context ? undefined\n                : typeof this.options.contextTracker == \"function\" ? this.options.contextTracker(this.termTable)\n                    : this.options.contextTracker,\n            topRules,\n            dialects,\n            dynamicPrecedences,\n            specialized,\n            tokenPrec,\n            termNames\n        });\n    }\n    getParserFile() {\n        let { states, stateData, goto, nodeNames, nodeProps: rawNodeProps, skippedTypes, maxTerm, repeatNodeCount, tokenizers: rawTokenizers, tokenData, topRules, dialects: rawDialects, dynamicPrecedences, specialized: rawSpecialized, tokenPrec, termNames } = this.prepareParser();\n        let mod = this.options.moduleStyle || \"es\";\n        let gen = \"// This file was generated by lezer-generator. You probably shouldn't edit it.\\n\", head = gen;\n        let imports = {}, imported = Object.create(null);\n        let defined = Object.create(null);\n        for (let word of KEYWORDS)\n            defined[word] = true;\n        let exportName = this.options.exportName || \"parser\";\n        defined[exportName] = true;\n        let getName = (prefix) => {\n            for (let i = 0;; i++) {\n                let id = prefix + (i ? \"_\" + i : \"\");\n                if (!defined[id])\n                    return id;\n            }\n        };\n        let importName = (name, source, prefix = name) => {\n            let spec = name + \" from \" + source;\n            if (imported[spec])\n                return imported[spec];\n            let src = JSON.stringify(source), varName = name;\n            if (name in defined) {\n                varName = getName(prefix);\n                name += `${mod == \"cjs\" ? \":\" : \" as\"} ${varName}`;\n            }\n            defined[varName] = true;\n            (imports[src] || (imports[src] = [])).push(name);\n            return imported[spec] = varName;\n        };\n        let lrParser = importName(\"LRParser\", \"@lezer/lr\");\n        let tokenizers = rawTokenizers.map(tok => tok.createSource(importName));\n        let context = this.ast.context ? importName(this.ast.context.id.name, this.ast.context.source) : null;\n        let nodeProps = rawNodeProps.map(({ prop, terms }) => {\n            let { source } = this.knownProps[prop];\n            let propID = source.from ? importName(source.name, source.from) : JSON.stringify(source.name);\n            return `[${propID}, ${terms.map(serializePropValue).join(\",\")}]`;\n        });\n        function specializationTableString(table) {\n            return \"{__proto__:null,\" + Object.keys(table).map(key => `${/^(\\d+|[a-zA-Z_]\\w*)$/.test(key) ? key : JSON.stringify(key)}:${table[key]}`)\n                .join(\", \") + \"}\";\n        }\n        let specHead = \"\";\n        let specialized = rawSpecialized.map(v => {\n            if (v instanceof ExternalSpecializer) {\n                let name = importName(v.ast.id.name, v.ast.source);\n                let ts = this.options.typeScript ? \": any\" : \"\";\n                return `{term: ${v.term.id}, get: (value${ts}, stack${ts}) => (${name}(value, stack) << 1)${v.ast.type == \"extend\" ? ` | ${1 /* Specialize.Extend */}` : ''}, external: ${name}${v.ast.type == \"extend\" ? ', extend: true' : ''}}`;\n            }\n            else {\n                let tableName = getName(\"spec_\" + v.token.name.replace(/\\W/g, \"\"));\n                defined[tableName] = true;\n                specHead += `const ${tableName} = ${specializationTableString(v.table)}\\n`;\n                let ts = this.options.typeScript ? `: keyof typeof ${tableName}` : \"\";\n                return `{term: ${v.token.id}, get: (value${ts}) => ${tableName}[value] || -1}`;\n            }\n        });\n        let propSources = this.ast.externalPropSources.map(s => importName(s.id.name, s.source));\n        for (let source in imports) {\n            if (mod == \"cjs\")\n                head += `const {${imports[source].join(\", \")}} = require(${source})\\n`;\n            else\n                head += `import {${imports[source].join(\", \")}} from ${source}\\n`;\n        }\n        head += specHead;\n        function serializePropValue(value) {\n            return typeof value != \"string\" || /^(true|false|\\d+(\\.\\d+)?|\\.\\d+)$/.test(value) ? value : JSON.stringify(value);\n        }\n        let dialects = Object.keys(rawDialects).map(d => `${d}: ${rawDialects[d]}`);\n        let parserStr = `${lrParser}.deserialize({\n  version: ${14 /* File.Version */},\n  states: ${encodeArray(states, 0xffffffff)},\n  stateData: ${encodeArray(stateData)},\n  goto: ${encodeArray(goto)},\n  nodeNames: ${JSON.stringify(nodeNames)},\n  maxTerm: ${maxTerm}${context ? `,\n  context: ${context}` : \"\"}${nodeProps.length ? `,\n  nodeProps: [\n    ${nodeProps.join(\",\\n    \")}\n  ]` : \"\"}${propSources.length ? `,\n  propSources: [${propSources.join()}]` : \"\"}${skippedTypes.length ? `,\n  skippedNodes: ${JSON.stringify(skippedTypes)}` : \"\"},\n  repeatNodeCount: ${repeatNodeCount},\n  tokenData: ${encodeArray(tokenData)},\n  tokenizers: [${tokenizers.join(\", \")}],\n  topRules: ${JSON.stringify(topRules)}${dialects.length ? `,\n  dialects: {${dialects.join(\", \")}}` : \"\"}${dynamicPrecedences ? `,\n  dynamicPrecedences: ${JSON.stringify(dynamicPrecedences)}` : \"\"}${specialized.length ? `,\n  specialized: [${specialized.join(\",\")}]` : \"\"},\n  tokenPrec: ${tokenPrec}${this.options.includeNames ? `,\n  termNames: ${JSON.stringify(termNames)}` : ''}\n})`;\n        let terms = [];\n        for (let name in this.termTable) {\n            let id = name;\n            if (KEYWORDS.includes(id))\n                for (let i = 1;; i++) {\n                    id = \"_\".repeat(i) + name;\n                    if (!(id in this.termTable))\n                        break;\n                }\n            else if (!/^[\\w$]+$/.test(name)) {\n                continue;\n            }\n            terms.push(`${id}${mod == \"cjs\" ? \":\" : \" =\"} ${this.termTable[name]}`);\n        }\n        for (let id = 0; id < this.dialects.length; id++)\n            terms.push(`Dialect_${this.dialects[id]}${mod == \"cjs\" ? \":\" : \" =\"} ${id}`);\n        return {\n            parser: head + (mod == \"cjs\" ? `exports.${exportName} = ${parserStr}\\n` : `export const ${exportName} = ${parserStr}\\n`),\n            terms: mod == \"cjs\" ? `${gen}module.exports = {\\n  ${terms.join(\",\\n  \")}\\n}`\n                : `${gen}export const\\n  ${terms.join(\",\\n  \")}\\n`\n        };\n    }\n    gatherNonSkippedNodes() {\n        let seen = Object.create(null);\n        let work = [];\n        let add = (term) => {\n            if (!seen[term.id]) {\n                seen[term.id] = true;\n                work.push(term);\n            }\n        };\n        this.terms.tops.forEach(add);\n        for (let i = 0; i < work.length; i++) {\n            for (let rule of work[i].rules)\n                for (let part of rule.parts)\n                    add(part);\n        }\n        return seen;\n    }\n    gatherNodeProps(nodeTypes) {\n        let notSkipped = this.gatherNonSkippedNodes(), skippedTypes = [];\n        let nodeProps = [];\n        for (let type of nodeTypes) {\n            if (!notSkipped[type.id] && !type.error)\n                skippedTypes.push(type.id);\n            for (let prop in type.props) {\n                let known = this.knownProps[prop];\n                if (!known)\n                    throw new GenError(\"No known prop type for \" + prop);\n                if (known.source.from == null && (known.source.name == \"repeated\" || known.source.name == \"error\"))\n                    continue;\n                let rec = nodeProps.find(r => r.prop == prop);\n                if (!rec)\n                    nodeProps.push(rec = { prop, values: {} });\n                (rec.values[type.props[prop]] || (rec.values[type.props[prop]] = [])).push(type.id);\n            }\n        }\n        return {\n            nodeProps: nodeProps.map(({ prop, values }) => {\n                let terms = [];\n                for (let val in values) {\n                    let ids = values[val];\n                    if (ids.length == 1) {\n                        terms.push(ids[0], val);\n                    }\n                    else {\n                        terms.push(-ids.length);\n                        for (let id of ids)\n                            terms.push(id);\n                        terms.push(val);\n                    }\n                }\n                return { prop, terms };\n            }),\n            skippedTypes\n        };\n    }\n    makeTerminal(name, tag, props) {\n        return this.terms.makeTerminal(this.terms.uniqueName(name), tag, props);\n    }\n    computeForceReductions(states, skipInfo) {\n        // This finds a forced reduction for every state, trying to guard\n        // against cyclic forced reductions, where a given parse stack can\n        // endlessly continue running forced reductions without making any\n        // progress.\n        //\n        // This occurs with length-1 reductions. We never generate\n        // length-0 reductions, and length-2+ reductions always shrink the\n        // stack, so they are guaranteed to make progress.\n        //\n        // If there are states S1 and S2 whose forced reductions reduce\n        // terms T1 and T2 respectively, both with a length of 1, _and_\n        // there is a state S3, which has goto entries T1 -> S2, T2 -> S1,\n        // you can get cyclic reductions. Of course, the cycle may also\n        // contain more than two steps.\n        let reductions = [];\n        let candidates = [];\n        // A map from terms to states that they are mapped to in goto\n        // entries.\n        let gotoEdges = Object.create(null);\n        for (let state of states) {\n            reductions.push(0);\n            for (let edge of state.goto) {\n                let array = gotoEdges[edge.term.id] || (gotoEdges[edge.term.id] = []);\n                let found = array.find(o => o.target == edge.target.id);\n                if (found)\n                    found.parents.push(state.id);\n                else\n                    array.push({ parents: [state.id], target: edge.target.id });\n            }\n            candidates[state.id] = state.set.filter(pos => pos.pos > 0 && !pos.rule.name.top)\n                .sort((a, b) => b.pos - a.pos || a.rule.parts.length - b.rule.parts.length);\n        }\n        // Mapping from state ids to terms that that state has a length-1\n        // forced reduction for.\n        let length1Reductions = Object.create(null);\n        function createsCycle(term, startState, parents = null) {\n            let edges = gotoEdges[term];\n            if (!edges)\n                return false;\n            return edges.some(val => {\n                let parentIntersection = parents ? parents.filter(id => val.parents.includes(id)) : val.parents;\n                if (parentIntersection.length == 0)\n                    return false;\n                if (val.target == startState)\n                    return true;\n                let found = length1Reductions[val.target];\n                return found != null && createsCycle(found, startState, parentIntersection);\n            });\n        }\n        for (let state of states) {\n            if (state.defaultReduce && state.defaultReduce.parts.length > 0) {\n                reductions[state.id] = reduceAction(state.defaultReduce, skipInfo);\n                if (state.defaultReduce.parts.length == 1)\n                    length1Reductions[state.id] = state.defaultReduce.name.id;\n            }\n        }\n        // To avoid painting states that only have one potential forced\n        // reduction into a corner, reduction assignment is done by\n        // candidate size, starting with the states with fewer candidates.\n        for (let setSize = 1;; setSize++) {\n            let done = true;\n            for (let state of states) {\n                if (state.defaultReduce)\n                    continue;\n                let set = candidates[state.id];\n                if (set.length != setSize) {\n                    if (set.length > setSize)\n                        done = false;\n                    continue;\n                }\n                for (let pos of set) {\n                    if (pos.pos != 1 || !createsCycle(pos.rule.name.id, state.id)) {\n                        reductions[state.id] = reduceAction(pos.rule, skipInfo, pos.pos);\n                        if (pos.pos == 1)\n                            length1Reductions[state.id] = pos.rule.name.id;\n                        break;\n                    }\n                }\n            }\n            if (done)\n                break;\n        }\n        return reductions;\n    }\n    substituteArgs(expr, args, params) {\n        if (args.length == 0)\n            return expr;\n        return expr.walk(expr => {\n            let found;\n            if (expr instanceof NameExpression &&\n                (found = params.findIndex(p => p.name == expr.id.name)) > -1) {\n                let arg = args[found];\n                if (expr.args.length) {\n                    if (arg instanceof NameExpression && !arg.args.length)\n                        return new NameExpression(expr.start, arg.id, expr.args);\n                    this.raise(`Passing arguments to a parameter that already has arguments`, expr.start);\n                }\n                return arg;\n            }\n            else if (expr instanceof InlineRuleExpression) {\n                let r = expr.rule, props = this.substituteArgsInProps(r.props, args, params);\n                return props == r.props ? expr :\n                    new InlineRuleExpression(expr.start, new RuleDeclaration(r.start, r.id, props, r.params, r.expr));\n            }\n            else if (expr instanceof SpecializeExpression) {\n                let props = this.substituteArgsInProps(expr.props, args, params);\n                return props == expr.props ? expr :\n                    new SpecializeExpression(expr.start, expr.type, props, expr.token, expr.content);\n            }\n            return expr;\n        });\n    }\n    substituteArgsInProps(props, args, params) {\n        let substituteInValue = (value) => {\n            let result = value;\n            for (let i = 0; i < value.length; i++) {\n                let part = value[i];\n                if (!part.name)\n                    continue;\n                let found = params.findIndex(p => p.name == part.name);\n                if (found < 0)\n                    continue;\n                if (result == value)\n                    result = value.slice();\n                let expr = args[found];\n                if (expr instanceof NameExpression && !expr.args.length)\n                    result[i] = new PropPart(part.start, expr.id.name, null);\n                else if (expr instanceof LiteralExpression)\n                    result[i] = new PropPart(part.start, expr.value, null);\n                else\n                    this.raise(`Trying to interpolate expression '${expr}' into a prop`, part.start);\n            }\n            return result;\n        };\n        let result = props;\n        for (let i = 0; i < props.length; i++) {\n            let prop = props[i], value = substituteInValue(prop.value);\n            if (value != prop.value) {\n                if (result == props)\n                    result = props.slice();\n                result[i] = new Prop(prop.start, prop.at, prop.name, value);\n            }\n        }\n        return result;\n    }\n    conflictsFor(markers) {\n        let here = Conflicts.none, atEnd = Conflicts.none;\n        for (let marker of markers) {\n            if (marker.type == \"ambig\") {\n                here = here.join(new Conflicts(0, [marker.id.name]));\n            }\n            else {\n                let precs = this.ast.precedences;\n                let index = precs ? precs.items.findIndex(item => item.id.name == marker.id.name) : -1;\n                if (index < 0)\n                    this.raise(`Reference to unknown precedence: '${marker.id.name}'`, marker.id.start);\n                let prec = precs.items[index], value = precs.items.length - index;\n                if (prec.type == \"cut\") {\n                    here = here.join(new Conflicts(0, none, value));\n                }\n                else {\n                    here = here.join(new Conflicts(value << 2));\n                    atEnd = atEnd.join(new Conflicts((value << 2) + (prec.type == \"left\" ? 1 : prec.type == \"right\" ? -1 : 0)));\n                }\n            }\n        }\n        return { here, atEnd };\n    }\n    raise(message, pos = 1) {\n        return this.input.raise(message, pos);\n    }\n    warn(message, pos = -1) {\n        let msg = this.input.message(message, pos);\n        if (this.options.warn)\n            this.options.warn(msg);\n        else\n            console.warn(msg);\n    }\n    defineRule(name, choices) {\n        let skip = this.currentSkip[this.currentSkip.length - 1];\n        for (let choice of choices)\n            this.rules.push(new Rule(name, choice.terms, choice.ensureConflicts(), skip));\n    }\n    resolve(expr) {\n        for (let built of this.built)\n            if (built.matches(expr))\n                return [p(built.term)];\n        let found = this.tokens.getToken(expr);\n        if (found)\n            return [p(found)];\n        for (let grp of this.localTokens) {\n            let found = grp.getToken(expr);\n            if (found)\n                return [p(found)];\n        }\n        for (let ext of this.externalTokens) {\n            let found = ext.getToken(expr);\n            if (found)\n                return [p(found)];\n        }\n        for (let ext of this.externalSpecializers) {\n            let found = ext.getToken(expr);\n            if (found)\n                return [p(found)];\n        }\n        let known = this.astRules.find(r => r.rule.id.name == expr.id.name);\n        if (!known)\n            return this.raise(`Reference to undefined rule '${expr.id.name}'`, expr.start);\n        if (known.rule.params.length != expr.args.length)\n            this.raise(`Wrong number or arguments for '${expr.id.name}'`, expr.start);\n        this.used(known.rule.id.name);\n        return [p(this.buildRule(known.rule, expr.args, known.skip))];\n    }\n    // For tree-balancing reasons, repeat expressions X+ have to be\n    // normalized to something like\n    //\n    //     R -> X | R R\n    //\n    // Returns the `R` term.\n    normalizeRepeat(expr) {\n        let known = this.built.find(b => b.matchesRepeat(expr));\n        if (known)\n            return p(known.term);\n        let name = expr.expr.prec < expr.prec ? `(${expr.expr})+` : `${expr.expr}+`;\n        let term = this.terms.makeRepeat(this.terms.uniqueName(name));\n        this.built.push(new BuiltRule(\"+\", [expr.expr], term));\n        this.defineRule(term, this.normalizeExpr(expr.expr).concat(p(term, term)));\n        return p(term);\n    }\n    normalizeSequence(expr) {\n        let result = expr.exprs.map(e => this.normalizeExpr(e));\n        let builder = this;\n        function complete(start, from, endConflicts) {\n            let { here, atEnd } = builder.conflictsFor(expr.markers[from]);\n            if (from == result.length)\n                return [start.withConflicts(start.terms.length, here.join(endConflicts))];\n            let choices = [];\n            for (let choice of result[from]) {\n                for (let full of complete(start.concat(choice).withConflicts(start.terms.length, here), from + 1, endConflicts.join(atEnd)))\n                    choices.push(full);\n            }\n            return choices;\n        }\n        return complete(Parts.none, 0, Conflicts.none);\n    }\n    normalizeExpr(expr) {\n        if (expr instanceof RepeatExpression && expr.kind == \"?\") {\n            return [Parts.none, ...this.normalizeExpr(expr.expr)];\n        }\n        else if (expr instanceof RepeatExpression) {\n            let repeated = this.normalizeRepeat(expr);\n            return expr.kind == \"+\" ? [repeated] : [Parts.none, repeated];\n        }\n        else if (expr instanceof ChoiceExpression) {\n            return expr.exprs.reduce((o, e) => o.concat(this.normalizeExpr(e)), []);\n        }\n        else if (expr instanceof SequenceExpression) {\n            return this.normalizeSequence(expr);\n        }\n        else if (expr instanceof LiteralExpression) {\n            return [p(this.tokens.getLiteral(expr))];\n        }\n        else if (expr instanceof NameExpression) {\n            return this.resolve(expr);\n        }\n        else if (expr instanceof SpecializeExpression) {\n            return [p(this.resolveSpecialization(expr))];\n        }\n        else if (expr instanceof InlineRuleExpression) {\n            return [p(this.buildRule(expr.rule, none, this.currentSkip[this.currentSkip.length - 1], true))];\n        }\n        else {\n            return this.raise(`This type of expression ('${expr}') may not occur in non-token rules`, expr.start);\n        }\n    }\n    buildRule(rule, args, skip, inline = false) {\n        let expr = this.substituteArgs(rule.expr, args, rule.params);\n        let { name: nodeName, props, dynamicPrec, inline: explicitInline, group, exported } = this.nodeInfo(rule.props || none, inline ? \"pg\" : \"pgi\", rule.id.name, args, rule.params, rule.expr);\n        if (exported && rule.params.length)\n            this.warn(`Can't export parameterized rules`, rule.start);\n        if (exported && inline)\n            this.warn(`Can't export inline rule`, rule.start);\n        let name = this.newName(rule.id.name + (args.length ? \"<\" + args.join(\",\") + \">\" : \"\"), nodeName || true, props);\n        if (explicitInline)\n            name.inline = true;\n        if (dynamicPrec)\n            this.registerDynamicPrec(name, dynamicPrec);\n        if ((name.nodeType || exported) && rule.params.length == 0) {\n            if (!nodeName)\n                name.preserve = true;\n            if (!inline)\n                this.namedTerms[exported || rule.id.name] = name;\n        }\n        if (!inline)\n            this.built.push(new BuiltRule(rule.id.name, args, name));\n        this.currentSkip.push(skip);\n        let parts = this.normalizeExpr(expr);\n        if (parts.length > 100 * (expr instanceof ChoiceExpression ? expr.exprs.length : 1))\n            this.warn(`Rule ${rule.id.name} is generating a lot (${parts.length}) of choices.\\n  Consider splitting it up or reducing the amount of ? or | operator uses.`, rule.start);\n        if (/\\brulesize\\b/.test(verbose) && parts.length > 10)\n            console.log(`Rule ${rule.id.name}: ${parts.length} variants`);\n        this.defineRule(name, parts);\n        this.currentSkip.pop();\n        if (group)\n            this.definedGroups.push({ name, group, rule });\n        return name;\n    }\n    nodeInfo(props, \n    // p for dynamic precedence, d for dialect, i for inline, g for group, a for disabling the ignore test for default name\n    allow, defaultName = null, args = none, params = none, expr, defaultProps) {\n        let result = {};\n        let name = defaultName && (allow.indexOf(\"a\") > -1 || !ignored(defaultName)) && !/ /.test(defaultName) ? defaultName : null;\n        let dialect = null, dynamicPrec = 0, inline = false, group = null, exported = null;\n        for (let prop of props) {\n            if (!prop.at) {\n                if (!this.knownProps[prop.name]) {\n                    let builtin = [\"name\", \"dialect\", \"dynamicPrecedence\", \"export\", \"isGroup\"].includes(prop.name)\n                        ? ` (did you mean '@${prop.name}'?)` : \"\";\n                    this.raise(`Unknown prop name '${prop.name}'${builtin}`, prop.start);\n                }\n                result[prop.name] = this.finishProp(prop, args, params);\n            }\n            else if (prop.name == \"name\") {\n                name = this.finishProp(prop, args, params);\n                if (/ /.test(name))\n                    this.raise(`Node names cannot have spaces ('${name}')`, prop.start);\n            }\n            else if (prop.name == \"dialect\") {\n                if (allow.indexOf(\"d\") < 0)\n                    this.raise(\"Can't specify a dialect on non-token rules\", props[0].start);\n                if (prop.value.length != 1 && !prop.value[0].value)\n                    this.raise(\"The '@dialect' rule prop must hold a plain string value\");\n                let dialectID = this.dialects.indexOf(prop.value[0].value);\n                if (dialectID < 0)\n                    this.raise(`Unknown dialect '${prop.value[0].value}'`, prop.value[0].start);\n                dialect = dialectID;\n            }\n            else if (prop.name == \"dynamicPrecedence\") {\n                if (allow.indexOf(\"p\") < 0)\n                    this.raise(\"Dynamic precedence can only be specified on nonterminals\");\n                if (prop.value.length != 1 || !/^-?(?:10|\\d)$/.test(prop.value[0].value))\n                    this.raise(\"The '@dynamicPrecedence' rule prop must hold an integer between -10 and 10\");\n                dynamicPrec = +prop.value[0].value;\n            }\n            else if (prop.name == \"inline\") {\n                if (prop.value.length)\n                    this.raise(\"'@inline' doesn't take a value\", prop.value[0].start);\n                if (allow.indexOf(\"i\") < 0)\n                    this.raise(\"Inline can only be specified on nonterminals\");\n                inline = true;\n            }\n            else if (prop.name == \"isGroup\") {\n                if (allow.indexOf(\"g\") < 0)\n                    this.raise(\"'@isGroup' can only be specified on nonterminals\");\n                group = prop.value.length ? this.finishProp(prop, args, params) : defaultName;\n            }\n            else if (prop.name == \"export\") {\n                if (prop.value.length)\n                    exported = this.finishProp(prop, args, params);\n                else\n                    exported = defaultName;\n            }\n            else {\n                this.raise(`Unknown built-in prop name '@${prop.name}'`, prop.start);\n            }\n        }\n        if (expr && this.ast.autoDelim && (name || hasProps(result))) {\n            let delim = this.findDelimiters(expr);\n            if (delim) {\n                addToProp(delim[0], \"closedBy\", delim[1].nodeName);\n                addToProp(delim[1], \"openedBy\", delim[0].nodeName);\n            }\n        }\n        if (defaultProps && hasProps(defaultProps)) {\n            for (let prop in defaultProps)\n                if (!(prop in result))\n                    result[prop] = defaultProps[prop];\n        }\n        if (hasProps(result) && !name)\n            this.raise(`Node has properties but no name`, props.length ? props[0].start : expr.start);\n        if (inline && (hasProps(result) || dialect || dynamicPrec))\n            this.raise(`Inline nodes can't have props, dynamic precedence, or a dialect`, props[0].start);\n        if (inline && name)\n            name = null;\n        return { name, props: result, dialect, dynamicPrec, inline, group, exported };\n    }\n    finishProp(prop, args, params) {\n        return prop.value.map(part => {\n            if (part.value)\n                return part.value;\n            let pos = params.findIndex(param => param.name == part.name);\n            if (pos < 0)\n                this.raise(`Property refers to '${part.name}', but no parameter by that name is in scope`, part.start);\n            let expr = args[pos];\n            if (expr instanceof NameExpression && !expr.args.length)\n                return expr.id.name;\n            if (expr instanceof LiteralExpression)\n                return expr.value;\n            return this.raise(`Expression '${expr}' can not be used as part of a property value`, part.start);\n        }).join(\"\");\n    }\n    resolveSpecialization(expr) {\n        let type = expr.type;\n        let { name, props, dialect, exported } = this.nodeInfo(expr.props, \"d\");\n        let terminal = this.normalizeExpr(expr.token);\n        if (terminal.length != 1 || terminal[0].terms.length != 1 || !terminal[0].terms[0].terminal)\n            this.raise(`The first argument to '${type}' must resolve to a token`, expr.token.start);\n        let values;\n        if (expr.content instanceof LiteralExpression)\n            values = [expr.content.value];\n        else if ((expr.content instanceof ChoiceExpression) && expr.content.exprs.every(e => e instanceof LiteralExpression))\n            values = expr.content.exprs.map(expr => expr.value);\n        else\n            return this.raise(`The second argument to '${expr.type}' must be a literal or choice of literals`, expr.content.start);\n        let term = terminal[0].terms[0], token = null;\n        let table = this.specialized[term.name] || (this.specialized[term.name] = []);\n        for (let value of values) {\n            let known = table.find(sp => sp.value == value);\n            if (known == null) {\n                if (!token) {\n                    token = this.makeTerminal(term.name + \"/\" + JSON.stringify(value), name, props);\n                    if (dialect != null)\n                        (this.tokens.byDialect[dialect] || (this.tokens.byDialect[dialect] = [])).push(token);\n                }\n                table.push({ value, term: token, type, dialect, name });\n                this.tokenOrigins[token.name] = { spec: term };\n                if (name || exported) {\n                    if (!name)\n                        token.preserve = true;\n                    this.namedTerms[exported || name] = token;\n                }\n            }\n            else {\n                if (known.type != type)\n                    this.raise(`Conflicting specialization types for ${JSON.stringify(value)} of ${term.name} (${type} vs ${known.type})`, expr.start);\n                if (known.dialect != dialect)\n                    this.raise(`Conflicting dialects for specialization ${JSON.stringify(value)} of ${term.name}`, expr.start);\n                if (known.name != name)\n                    this.raise(`Conflicting names for specialization ${JSON.stringify(value)} of ${term.name}`, expr.start);\n                if (token && known.term != token)\n                    this.raise(`Conflicting specialization tokens for ${JSON.stringify(value)} of ${term.name}`, expr.start);\n                token = known.term;\n            }\n        }\n        return token;\n    }\n    findDelimiters(expr) {\n        if (!(expr instanceof SequenceExpression) || expr.exprs.length < 2)\n            return null;\n        let findToken = (expr) => {\n            if (expr instanceof LiteralExpression)\n                return { term: this.tokens.getLiteral(expr), str: expr.value };\n            if (expr instanceof NameExpression && expr.args.length == 0) {\n                let rule = this.ast.rules.find(r => r.id.name == expr.id.name);\n                if (rule)\n                    return findToken(rule.expr);\n                let token = this.tokens.rules.find(r => r.id.name == expr.id.name);\n                if (token && token.expr instanceof LiteralExpression)\n                    return { term: this.tokens.getToken(expr), str: token.expr.value };\n            }\n            return null;\n        };\n        let lastToken = findToken(expr.exprs[expr.exprs.length - 1]);\n        if (!lastToken || !lastToken.term.nodeName)\n            return null;\n        const brackets = [\"()\", \"[]\", \"{}\", \"<>\"];\n        let bracket = brackets.find(b => lastToken.str.indexOf(b[1]) > -1 && lastToken.str.indexOf(b[0]) < 0);\n        if (!bracket)\n            return null;\n        let firstToken = findToken(expr.exprs[0]);\n        if (!firstToken || !firstToken.term.nodeName ||\n            firstToken.str.indexOf(bracket[0]) < 0 || firstToken.str.indexOf(bracket[1]) > -1)\n            return null;\n        return [firstToken.term, lastToken.term];\n    }\n    registerDynamicPrec(term, prec) {\n        this.dynamicRulePrecedences.push({ rule: term, prec });\n        term.preserve = true;\n    }\n    defineGroup(rule, group, ast) {\n        var _a;\n        let recur = [];\n        let getNamed = (rule) => {\n            if (rule.nodeName)\n                return [rule];\n            if (recur.includes(rule))\n                this.raise(`Rule '${ast.id.name}' cannot define a group because it contains a non-named recursive rule ('${rule.name}')`, ast.start);\n            let result = [];\n            recur.push(rule);\n            for (let r of this.rules)\n                if (r.name == rule) {\n                    let names = r.parts.map(getNamed).filter(x => x.length);\n                    if (names.length > 1)\n                        this.raise(`Rule '${ast.id.name}' cannot define a group because some choices produce multiple named nodes`, ast.start);\n                    if (names.length == 1)\n                        for (let n of names[0])\n                            result.push(n);\n                }\n            recur.pop();\n            return result;\n        };\n        for (let name of getNamed(rule))\n            name.props[\"group\"] = (((_a = name.props[\"group\"]) === null || _a === void 0 ? void 0 : _a.split(\" \")) || []).concat(group).sort().join(\" \");\n    }\n    checkGroups() {\n        let groups = Object.create(null), nodeNames = Object.create(null);\n        for (let term of this.terms.terms)\n            if (term.nodeName) {\n                nodeNames[term.nodeName] = true;\n                if (term.props[\"group\"])\n                    for (let group of term.props[\"group\"].split(\" \")) {\n                        (groups[group] || (groups[group] = [])).push(term);\n                    }\n            }\n        let names = Object.keys(groups);\n        for (let i = 0; i < names.length; i++) {\n            let name = names[i], terms = groups[name];\n            if (nodeNames[name])\n                this.warn(`Group name '${name}' conflicts with a node of the same name`);\n            for (let j = i + 1; j < names.length; j++) {\n                let other = groups[names[j]];\n                if (terms.some(t => other.includes(t)) &&\n                    (terms.length > other.length ? other.some(t => !terms.includes(t)) : terms.some(t => !other.includes(t))))\n                    this.warn(`Groups '${name}' and '${names[j]}' overlap without one being a superset of the other`);\n            }\n        }\n    }\n}\nconst MinSharedActions = 5;\nclass FinishStateContext {\n    constructor(tokenizers, data, stateArray, skipData, skipInfo, states, builder) {\n        this.tokenizers = tokenizers;\n        this.data = data;\n        this.stateArray = stateArray;\n        this.skipData = skipData;\n        this.skipInfo = skipInfo;\n        this.states = states;\n        this.builder = builder;\n        this.sharedActions = [];\n    }\n    findSharedActions(state) {\n        if (state.actions.length < MinSharedActions)\n            return null;\n        let found = null;\n        for (let shared of this.sharedActions) {\n            if ((!found || shared.actions.length > found.actions.length) &&\n                shared.actions.every(a => state.actions.some(b => b.eq(a))))\n                found = shared;\n        }\n        if (found)\n            return found;\n        let max = null, scratch = [];\n        for (let i = state.id + 1; i < this.states.length; i++) {\n            let other = this.states[i], fill = 0;\n            if (other.defaultReduce || other.actions.length < MinSharedActions)\n                continue;\n            for (let a of state.actions)\n                for (let b of other.actions)\n                    if (a.eq(b))\n                        scratch[fill++] = a;\n            if (fill >= MinSharedActions && (!max || max.length < fill)) {\n                max = scratch;\n                scratch = [];\n            }\n        }\n        if (!max)\n            return null;\n        let result = { actions: max, addr: this.storeActions(max, -1, null) };\n        this.sharedActions.push(result);\n        return result;\n    }\n    storeActions(actions, skipReduce, shared) {\n        if (skipReduce < 0 && shared && shared.actions.length == actions.length)\n            return shared.addr;\n        let data = [];\n        for (let action of actions) {\n            if (shared && shared.actions.some(a => a.eq(action)))\n                continue;\n            if (action instanceof Shift) {\n                data.push(action.term.id, action.target.id, 0);\n            }\n            else {\n                let code = reduceAction(action.rule, this.skipInfo);\n                if (code != skipReduce)\n                    data.push(action.term.id, code & 65535 /* Action.ValueMask */, code >> 16);\n            }\n        }\n        data.push(65535 /* Seq.End */);\n        if (skipReduce > -1)\n            data.push(2 /* Seq.Other */, skipReduce & 65535 /* Action.ValueMask */, skipReduce >> 16);\n        else if (shared)\n            data.push(1 /* Seq.Next */, shared.addr & 0xffff, shared.addr >> 16);\n        else\n            data.push(0 /* Seq.Done */);\n        return this.data.storeArray(data);\n    }\n    finish(state, isSkip, forcedReduce) {\n        let b = this.builder;\n        let skipID = b.skipRules.indexOf(state.skip);\n        let skipTable = this.skipData[skipID], skipTerms = this.skipInfo[skipID].startTokens;\n        let defaultReduce = state.defaultReduce ? reduceAction(state.defaultReduce, this.skipInfo) : 0;\n        let flags = isSkip ? 1 /* StateFlag.Skipped */ : 0;\n        let skipReduce = -1, shared = null;\n        if (defaultReduce == 0) {\n            if (isSkip)\n                for (const action of state.actions)\n                    if (action instanceof Reduce && action.term.eof)\n                        skipReduce = reduceAction(action.rule, this.skipInfo);\n            if (skipReduce < 0)\n                shared = this.findSharedActions(state);\n        }\n        if (state.set.some(p => p.rule.name.top && p.pos == p.rule.parts.length))\n            flags |= 2 /* StateFlag.Accepting */;\n        let external = [];\n        for (let i = 0; i < state.actions.length + skipTerms.length; i++) {\n            let term = i < state.actions.length ? state.actions[i].term : skipTerms[i - state.actions.length];\n            for (;;) {\n                let orig = b.tokenOrigins[term.name];\n                if (orig && orig.spec) {\n                    term = orig.spec;\n                    continue;\n                }\n                if (orig && (orig.external instanceof ExternalTokenSet))\n                    addToSet(external, orig.external);\n                break;\n            }\n        }\n        let tokenizerMask = 0;\n        for (let i = 0; i < this.tokenizers.length; i++) {\n            let tok = this.tokenizers[i];\n            if (external.includes(tok) || tok.groupID == state.tokenGroup)\n                tokenizerMask |= (1 << i);\n        }\n        let base = state.id * 6 /* ParseState.Size */;\n        this.stateArray[base + 0 /* ParseState.Flags */] = flags;\n        this.stateArray[base + 1 /* ParseState.Actions */] = this.storeActions(defaultReduce ? none : state.actions, skipReduce, shared);\n        this.stateArray[base + 2 /* ParseState.Skip */] = skipTable;\n        this.stateArray[base + 3 /* ParseState.TokenizerMask */] = tokenizerMask;\n        this.stateArray[base + 4 /* ParseState.DefaultReduce */] = defaultReduce;\n        this.stateArray[base + 5 /* ParseState.ForcedReduce */] = forcedReduce;\n    }\n}\nfunction addToProp(term, prop, value) {\n    let cur = term.props[prop];\n    if (!cur || cur.split(\" \").indexOf(value) < 0)\n        term.props[prop] = cur ? cur + \" \" + value : value;\n}\nfunction buildSpecializeTable(spec) {\n    let table = Object.create(null);\n    for (let { value, term, type } of spec) {\n        let code = type == \"specialize\" ? 0 /* Specialize.Specialize */ : 1 /* Specialize.Extend */;\n        table[value] = (term.id << 1) | code;\n    }\n    return table;\n}\nfunction reduceAction(rule, skipInfo, depth = rule.parts.length) {\n    return rule.name.id | 65536 /* Action.ReduceFlag */ |\n        (rule.isRepeatWrap && depth == rule.parts.length ? 131072 /* Action.RepeatFlag */ : 0) |\n        (skipInfo.some(i => i.rule == rule.name) ? 262144 /* Action.StayFlag */ : 0) |\n        (depth << 19 /* Action.ReduceDepthShift */);\n}\nfunction findArray(data, value) {\n    search: for (let i = 0;;) {\n        let next = data.indexOf(value[0], i);\n        if (next == -1 || next + value.length > data.length)\n            break;\n        for (let j = 1; j < value.length; j++) {\n            if (value[j] != data[next + j]) {\n                i = next + 1;\n                continue search;\n            }\n        }\n        return next;\n    }\n    return -1;\n}\nfunction findSkipStates(table, startRules) {\n    let nonSkip = Object.create(null);\n    let work = [];\n    let add = (state) => {\n        if (!nonSkip[state.id]) {\n            nonSkip[state.id] = true;\n            work.push(state);\n        }\n    };\n    for (let state of table)\n        if (state.startRule && startRules.includes(state.startRule))\n            add(state);\n    for (let i = 0; i < work.length; i++) {\n        for (let a of work[i].actions)\n            if (a instanceof Shift)\n                add(a.target);\n        for (let a of work[i].goto)\n            add(a.target);\n    }\n    return (id) => !nonSkip[id];\n}\nclass DataBuilder {\n    constructor() {\n        this.data = [];\n    }\n    storeArray(data) {\n        let found = findArray(this.data, data);\n        if (found > -1)\n            return found;\n        let pos = this.data.length;\n        for (let num of data)\n            this.data.push(num);\n        return pos;\n    }\n    finish() {\n        return Uint16Array.from(this.data);\n    }\n}\n// The goto table maps a start state + a term to a new state, and is\n// used to determine the new state when reducing. Because this allows\n// more more efficient representation and access, unlike the action\n// tables, the goto table is organized by term, with groups of start\n// states that map to a given end state enumerated for each term.\n// Since many terms only have a single valid goto target, this makes\n// it cheaper to look those up.\n//\n// (Unfortunately, though the standard LR parsing mechanism never\n// looks up invalid goto states, the incremental parsing mechanism\n// needs accurate goto information for a state/term pair, so we do\n// need to store state ids even for terms that have only one target.)\n//\n// - First comes the amount of terms in the table\n//\n// - Then, for each term, the offset of the term's data\n//\n// - At these offsets, there's a record for each target state\n//\n//   - Such a record starts with the amount of start states that go to\n//     this target state, shifted one to the left, with the first bit\n//     only set if this is the last record for this term.\n//\n//   - Then follows the target state id\n//\n//   - And then the start state ids\nfunction computeGotoTable(states) {\n    let goto = {};\n    let maxTerm = 0;\n    for (let state of states) {\n        for (let entry of state.goto) {\n            maxTerm = Math.max(entry.term.id, maxTerm);\n            let set = goto[entry.term.id] || (goto[entry.term.id] = {});\n            (set[entry.target.id] || (set[entry.target.id] = [])).push(state.id);\n        }\n    }\n    let data = new DataBuilder;\n    let index = [];\n    let offset = maxTerm + 2; // Offset of the data, taking index size into account\n    for (let term = 0; term <= maxTerm; term++) {\n        let entries = goto[term];\n        if (!entries) {\n            index.push(1);\n            continue;\n        }\n        let termTable = [];\n        let keys = Object.keys(entries);\n        for (let target of keys) {\n            let list = entries[target];\n            termTable.push((target == keys[keys.length - 1] ? 1 : 0) + (list.length << 1));\n            termTable.push(+target);\n            for (let source of list)\n                termTable.push(source);\n        }\n        index.push(data.storeArray(termTable) + offset);\n    }\n    if (index.some(n => n > 0xffff))\n        throw new GenError(\"Goto table too large\");\n    return Uint16Array.from([maxTerm + 1, ...index, ...data.data]);\n}\nclass TokenGroup {\n    constructor(tokens, groupID) {\n        this.tokens = tokens;\n        this.groupID = groupID;\n    }\n    create() { return this.groupID; }\n    createSource() { return String(this.groupID); }\n}\nfunction addToSet(set, value) {\n    if (!set.includes(value))\n        set.push(value);\n}\nfunction buildTokenMasks(groups) {\n    let masks = Object.create(null);\n    for (let group of groups) {\n        let groupMask = 1 << group.groupID;\n        for (let term of group.tokens) {\n            masks[term.id] = (masks[term.id] || 0) | groupMask;\n        }\n    }\n    return masks;\n}\nclass TokenArg {\n    constructor(name, expr, scope) {\n        this.name = name;\n        this.expr = expr;\n        this.scope = scope;\n    }\n}\nclass BuildingRule {\n    constructor(name, start, to, args) {\n        this.name = name;\n        this.start = start;\n        this.to = to;\n        this.args = args;\n    }\n}\nclass TokenSet {\n    constructor(b, ast) {\n        this.b = b;\n        this.ast = ast;\n        this.startState = new State$1;\n        this.built = [];\n        this.building = []; // Used for recursion check\n        this.byDialect = Object.create(null);\n        this.precedenceRelations = [];\n        this.rules = ast ? ast.rules : none;\n        for (let rule of this.rules)\n            b.unique(rule.id);\n    }\n    getToken(expr) {\n        for (let built of this.built)\n            if (built.matches(expr))\n                return built.term;\n        let name = expr.id.name;\n        let rule = this.rules.find(r => r.id.name == name);\n        if (!rule)\n            return null;\n        let { name: nodeName, props, dialect, exported } = this.b.nodeInfo(rule.props, \"d\", name, expr.args, rule.params.length != expr.args.length ? none : rule.params);\n        let term = this.b.makeTerminal(expr.toString(), nodeName, props);\n        if (dialect != null)\n            (this.byDialect[dialect] || (this.byDialect[dialect] = [])).push(term);\n        if ((term.nodeType || exported) && rule.params.length == 0) {\n            if (!term.nodeType)\n                term.preserve = true;\n            this.b.namedTerms[exported || name] = term;\n        }\n        this.buildRule(rule, expr, this.startState, new State$1([term]));\n        this.built.push(new BuiltRule(name, expr.args, term));\n        return term;\n    }\n    buildRule(rule, expr, from, to, args = none) {\n        let name = expr.id.name;\n        if (rule.params.length != expr.args.length)\n            this.b.raise(`Incorrect number of arguments for token '${name}'`, expr.start);\n        let building = this.building.find(b => b.name == name && exprsEq(expr.args, b.args));\n        if (building) {\n            if (building.to == to) {\n                from.nullEdge(building.start);\n                return;\n            }\n            let lastIndex = this.building.length - 1;\n            while (this.building[lastIndex].name != name)\n                lastIndex--;\n            this.b.raise(`Invalid (non-tail) recursion in token rules: ${this.building.slice(lastIndex).map(b => b.name).join(\" -> \")}`, expr.start);\n        }\n        this.b.used(rule.id.name);\n        let start = new State$1;\n        from.nullEdge(start);\n        this.building.push(new BuildingRule(name, start, to, expr.args));\n        this.build(this.b.substituteArgs(rule.expr, expr.args, rule.params), start, to, expr.args.map((e, i) => new TokenArg(rule.params[i].name, e, args)));\n        this.building.pop();\n    }\n    build(expr, from, to, args) {\n        if (expr instanceof NameExpression) {\n            let name = expr.id.name, arg = args.find(a => a.name == name);\n            if (arg)\n                return this.build(arg.expr, from, to, arg.scope);\n            let rule;\n            for (let i = 0, lt = this.b.localTokens; i <= lt.length; i++) {\n                let set = i == lt.length ? this.b.tokens : lt[i];\n                rule = set.rules.find(r => r.id.name == name);\n            }\n            if (!rule)\n                return this.b.raise(`Reference to token rule '${expr.id.name}', which isn't found`, expr.start);\n            this.buildRule(rule, expr, from, to, args);\n        }\n        else if (expr instanceof CharClass) {\n            for (let [a, b] of CharClasses[expr.type])\n                from.edge(a, b, to);\n        }\n        else if (expr instanceof ChoiceExpression) {\n            for (let choice of expr.exprs)\n                this.build(choice, from, to, args);\n        }\n        else if (isEmpty(expr)) {\n            from.nullEdge(to);\n        }\n        else if (expr instanceof SequenceExpression) {\n            let conflict = expr.markers.find(c => c.length > 0);\n            if (conflict)\n                this.b.raise(\"Conflict marker in token expression\", conflict[0].start);\n            for (let i = 0; i < expr.exprs.length; i++) {\n                let next = i == expr.exprs.length - 1 ? to : new State$1;\n                this.build(expr.exprs[i], from, next, args);\n                from = next;\n            }\n        }\n        else if (expr instanceof RepeatExpression) {\n            if (expr.kind == \"*\") {\n                let loop = new State$1;\n                from.nullEdge(loop);\n                this.build(expr.expr, loop, loop, args);\n                loop.nullEdge(to);\n            }\n            else if (expr.kind == \"+\") {\n                let loop = new State$1;\n                this.build(expr.expr, from, loop, args);\n                this.build(expr.expr, loop, loop, args);\n                loop.nullEdge(to);\n            }\n            else { // expr.kind == \"?\"\n                from.nullEdge(to);\n                this.build(expr.expr, from, to, args);\n            }\n        }\n        else if (expr instanceof SetExpression) {\n            for (let [a, b] of expr.inverted ? invertRanges(expr.ranges) : expr.ranges)\n                rangeEdges(from, to, a, b);\n        }\n        else if (expr instanceof LiteralExpression) {\n            for (let i = 0; i < expr.value.length; i++) {\n                let ch = expr.value.charCodeAt(i);\n                let next = i == expr.value.length - 1 ? to : new State$1;\n                from.edge(ch, ch + 1, next);\n                from = next;\n            }\n        }\n        else if (expr instanceof AnyExpression) {\n            let mid = new State$1;\n            from.edge(0, 0xDC00, to);\n            from.edge(0xDC00, MAX_CHAR + 1, to);\n            from.edge(0xD800, 0xDC00, mid);\n            mid.edge(0xDC00, 0xE000, to);\n        }\n        else {\n            return this.b.raise(`Unrecognized expression type in token`, expr.start);\n        }\n    }\n    takePrecedences() {\n        let rel = this.precedenceRelations = [];\n        if (this.ast)\n            for (let group of this.ast.precedences) {\n                let prev = [];\n                for (let item of group.items) {\n                    let level = [];\n                    if (item instanceof NameExpression) {\n                        for (let built of this.built)\n                            if (item.args.length ? built.matches(item) : built.id == item.id.name)\n                                level.push(built.term);\n                    }\n                    else {\n                        let id = JSON.stringify(item.value), found = this.built.find(b => b.id == id);\n                        if (found)\n                            level.push(found.term);\n                    }\n                    if (!level.length)\n                        this.b.warn(`Precedence specified for unknown token ${item}`, item.start);\n                    for (let term of level)\n                        addRel(rel, term, prev);\n                    prev = prev.concat(level);\n                }\n            }\n    }\n    precededBy(a, b) {\n        let found = this.precedenceRelations.find(r => r.term == a);\n        return found && found.after.includes(b);\n    }\n    buildPrecTable(softConflicts) {\n        let precTable = [], rel = this.precedenceRelations.slice();\n        // Add entries for soft-conflicting tokens that are in the\n        // precedence table, to make sure they'll appear in the right\n        // order and don't mess up the longer-wins default rule.\n        for (let { a, b, soft } of softConflicts)\n            if (soft) {\n                if (!rel.some(r => r.term == a) || !rel.some(r => r.term == b))\n                    continue;\n                if (soft < 0)\n                    [a, b] = [b, a]; // Now a is longer than b (and should thus take precedence)\n                addRel(rel, b, [a]);\n                addRel(rel, a, []);\n            }\n        add: while (rel.length) {\n            for (let i = 0; i < rel.length; i++) {\n                let record = rel[i];\n                if (record.after.every(t => precTable.includes(t.id))) {\n                    precTable.push(record.term.id);\n                    if (rel.length == 1)\n                        break add;\n                    rel[i] = rel.pop();\n                    continue add;\n                }\n            }\n            this.b.raise(`Cyclic token precedence relation between ${rel.map(r => r.term).join(\", \")}`);\n        }\n        return precTable;\n    }\n}\nclass MainTokenSet extends TokenSet {\n    constructor() {\n        super(...arguments);\n        this.explicitConflicts = [];\n    }\n    getLiteral(expr) {\n        let id = JSON.stringify(expr.value);\n        for (let built of this.built)\n            if (built.id == id)\n                return built.term;\n        let name = null, props = {}, dialect = null, exported = null;\n        let decl = this.ast ? this.ast.literals.find(l => l.literal == expr.value) : null;\n        if (decl)\n            ({ name, props, dialect, exported } = this.b.nodeInfo(decl.props, \"da\", expr.value));\n        let term = this.b.makeTerminal(id, name, props);\n        if (dialect != null)\n            (this.byDialect[dialect] || (this.byDialect[dialect] = [])).push(term);\n        if (exported)\n            this.b.namedTerms[exported] = term;\n        this.build(expr, this.startState, new State$1([term]), none);\n        this.built.push(new BuiltRule(id, none, term));\n        return term;\n    }\n    takeConflicts() {\n        var _a;\n        let resolve = (expr) => {\n            if (expr instanceof NameExpression) {\n                for (let built of this.built)\n                    if (built.matches(expr))\n                        return built.term;\n            }\n            else {\n                let id = JSON.stringify(expr.value), found = this.built.find(b => b.id == id);\n                if (found)\n                    return found.term;\n            }\n            this.b.warn(`Precedence specified for unknown token ${expr}`, expr.start);\n            return null;\n        };\n        for (let c of ((_a = this.ast) === null || _a === void 0 ? void 0 : _a.conflicts) || []) {\n            let a = resolve(c.a), b = resolve(c.b);\n            if (a && b) {\n                if (a.id < b.id)\n                    [a, b] = [b, a];\n                this.explicitConflicts.push({ a, b });\n            }\n        }\n    }\n    // Token groups are a mechanism for allowing conflicting (matching\n    // overlapping input, without an explicit precedence being given)\n    // tokens to exist in a grammar _if_ they don't occur in the same\n    // place (aren't used in the same states).\n    //\n    // States that use tokens that conflict will raise an error when any\n    // of the conflicting pairs of tokens both occur in that state.\n    // Otherwise, they are assigned a token group, which includes all\n    // the potentially-conflicting tokens they use. If there's already a\n    // group that doesn't have any conflicts with those tokens, that is\n    // reused, otherwise a new group is created.\n    //\n    // So each state has zero or one token groups, and each conflicting\n    // token may belong to one or more groups. Tokens get assigned a\n    // 16-bit bitmask with the groups they belong to set to 1 (all-1s\n    // for non-conflicting tokens). When tokenizing, that mask is\n    // compared to the current state's group (again using all-1s for\n    // group-less states) to determine whether a token is applicable for\n    // this state.\n    //\n    // Extended/specialized tokens are treated as their parent token for\n    // this purpose.\n    buildTokenGroups(states, skipInfo, startID) {\n        let tokens = this.startState.compile();\n        if (tokens.accepting.length)\n            this.b.raise(`Grammar contains zero-length tokens (in '${tokens.accepting[0].name}')`, this.rules.find(r => r.id.name == tokens.accepting[0].name).start);\n        if (/\\btokens\\b/.test(verbose))\n            console.log(tokens.toString());\n        // If there is a precedence specified for the pair, the conflict is resolved\n        let allConflicts = tokens.findConflicts(checkTogether(states, this.b, skipInfo))\n            .filter(({ a, b }) => !this.precededBy(a, b) && !this.precededBy(b, a));\n        for (let { a, b } of this.explicitConflicts) {\n            if (!allConflicts.some(c => c.a == a && c.b == b))\n                allConflicts.push(new Conflict$1(a, b, 0, \"\", \"\"));\n        }\n        let softConflicts = allConflicts.filter(c => c.soft), conflicts = allConflicts.filter(c => !c.soft);\n        let errors = [];\n        let groups = [];\n        for (let state of states) {\n            if (state.defaultReduce || state.tokenGroup > -1)\n                continue;\n            // Find potentially-conflicting terms (in terms) and the things\n            // they conflict with (in conflicts), and raise an error if\n            // there's a token conflict directly in this state.\n            let terms = [], incompatible = [];\n            let skip = skipInfo[this.b.skipRules.indexOf(state.skip)].startTokens;\n            for (let term of skip)\n                if (state.actions.some(a => a.term == term))\n                    this.b.raise(`Use of token ${term.name} conflicts with skip rule`);\n            let stateTerms = [];\n            for (let i = 0; i < state.actions.length + (skip ? skip.length : 0); i++) {\n                let term = i < state.actions.length ? state.actions[i].term : skip[i - state.actions.length];\n                let orig = this.b.tokenOrigins[term.name];\n                if (orig && orig.spec)\n                    term = orig.spec;\n                else if (orig && orig.external)\n                    continue;\n                addToSet(stateTerms, term);\n            }\n            if (stateTerms.length == 0)\n                continue;\n            for (let term of stateTerms) {\n                for (let conflict of conflicts) {\n                    let conflicting = conflict.a == term ? conflict.b : conflict.b == term ? conflict.a : null;\n                    if (!conflicting)\n                        continue;\n                    if (stateTerms.includes(conflicting) && !errors.some(e => e.conflict == conflict)) {\n                        let example = conflict.exampleA ? ` (example: ${JSON.stringify(conflict.exampleA)}${conflict.exampleB ? ` vs ${JSON.stringify(conflict.exampleB)}` : \"\"})` : \"\";\n                        errors.push({\n                            error: `Overlapping tokens ${term.name} and ${conflicting.name} used in same context${example}\\n` +\n                                `After: ${state.set[0].trail()}`,\n                            conflict\n                        });\n                    }\n                    addToSet(terms, term);\n                    addToSet(incompatible, conflicting);\n                }\n            }\n            let tokenGroup = null;\n            for (let group of groups) {\n                if (incompatible.some(term => group.tokens.includes(term)))\n                    continue;\n                for (let term of terms)\n                    addToSet(group.tokens, term);\n                tokenGroup = group;\n                break;\n            }\n            if (!tokenGroup) {\n                tokenGroup = new TokenGroup(terms, groups.length + startID);\n                groups.push(tokenGroup);\n            }\n            state.tokenGroup = tokenGroup.groupID;\n        }\n        if (errors.length)\n            this.b.raise(errors.map(e => e.error).join(\"\\n\\n\"));\n        if (groups.length + startID > 16)\n            this.b.raise(`Too many different token groups (${groups.length}) to represent them as a 16-bit bitfield`);\n        let precTable = this.buildPrecTable(softConflicts);\n        return {\n            tokenGroups: groups,\n            tokenPrec: precTable,\n            tokenData: tokens.toArray(buildTokenMasks(groups), precTable)\n        };\n    }\n}\nclass LocalTokenSet extends TokenSet {\n    constructor(b, ast) {\n        super(b, ast);\n        this.fallback = null;\n        if (ast.fallback)\n            b.unique(ast.fallback.id);\n    }\n    getToken(expr) {\n        let term = null;\n        if (this.ast.fallback && this.ast.fallback.id.name == expr.id.name) {\n            if (expr.args.length)\n                this.b.raise(`Incorrect number of arguments for ${expr.id.name}`, expr.start);\n            if (!this.fallback) {\n                let { name: nodeName, props, exported } = this.b.nodeInfo(this.ast.fallback.props, \"\", expr.id.name, none, none);\n                let term = this.fallback = this.b.makeTerminal(expr.id.name, nodeName, props);\n                if (term.nodeType || exported) {\n                    if (!term.nodeType)\n                        term.preserve = true;\n                    this.b.namedTerms[exported || expr.id.name] = term;\n                }\n                this.b.used(expr.id.name);\n            }\n            term = this.fallback;\n        }\n        else {\n            term = super.getToken(expr);\n        }\n        if (term && !this.b.tokenOrigins[term.name])\n            this.b.tokenOrigins[term.name] = { group: this };\n        return term;\n    }\n    buildLocalGroup(states, skipInfo, id) {\n        let tokens = this.startState.compile();\n        if (tokens.accepting.length)\n            this.b.raise(`Grammar contains zero-length tokens (in '${tokens.accepting[0].name}')`, this.rules.find(r => r.id.name == tokens.accepting[0].name).start);\n        for (let { a, b, exampleA } of tokens.findConflicts(() => true)) {\n            if (!this.precededBy(a, b) && !this.precededBy(b, a))\n                this.b.raise(`Overlapping tokens ${a.name} and ${b.name} in local token group${exampleA ? ` (example: ${JSON.stringify(exampleA)})` : ''}`);\n        }\n        for (let state of states) {\n            if (state.defaultReduce)\n                continue;\n            // See if this state uses any of the tokens in this group, and\n            // if so, make sure it *only* uses tokens from this group.\n            let usesThis = null;\n            let usesOther = skipInfo[this.b.skipRules.indexOf(state.skip)].startTokens[0];\n            for (let { term } of state.actions) {\n                let orig = this.b.tokenOrigins[term.name];\n                if ((orig === null || orig === void 0 ? void 0 : orig.group) == this)\n                    usesThis = term;\n                else\n                    usesOther = term;\n            }\n            if (usesThis) {\n                if (usesOther)\n                    this.b.raise(`Tokens from a local token group used together with other tokens (${usesThis.name} with ${usesOther.name})`);\n                state.tokenGroup = id;\n            }\n        }\n        let precTable = this.buildPrecTable(none);\n        let tokenData = tokens.toArray({ [id]: 65535 /* Seq.End */ }, precTable);\n        let precOffset = tokenData.length;\n        let fullData = new Uint16Array(tokenData.length + precTable.length + 1);\n        fullData.set(tokenData, 0);\n        fullData.set(precTable, precOffset);\n        fullData[fullData.length - 1] = 65535 /* Seq.End */;\n        return {\n            groupID: id,\n            create: () => new LocalTokenGroup(fullData, precOffset, this.fallback ? this.fallback.id : undefined),\n            createSource: importName => `new ${importName(\"LocalTokenGroup\", \"@lezer/lr\")}(${encodeArray(fullData)}, ${precOffset}${this.fallback ? `, ${this.fallback.id}` : ''})`\n        };\n    }\n}\nfunction checkTogether(states, b, skipInfo) {\n    let cache = Object.create(null);\n    function hasTerm(state, term) {\n        return state.actions.some(a => a.term == term) ||\n            skipInfo[b.skipRules.indexOf(state.skip)].startTokens.includes(term);\n    }\n    return (a, b) => {\n        if (a.id < b.id)\n            [a, b] = [b, a];\n        let key = a.id | (b.id << 16), cached = cache[key];\n        if (cached != null)\n            return cached;\n        return cache[key] = states.some(state => hasTerm(state, a) && hasTerm(state, b));\n    };\n}\nfunction invertRanges(ranges) {\n    let pos = 0, result = [];\n    for (let [a, b] of ranges) {\n        if (a > pos)\n            result.push([pos, a]);\n        pos = b;\n    }\n    if (pos <= MAX_CODE)\n        result.push([pos, MAX_CODE + 1]);\n    return result;\n}\nconst ASTRAL = 0x10000, GAP_START = 0xd800, GAP_END = 0xe000, MAX_CODE = 0x10ffff;\nconst LOW_SURR_B = 0xdc00, HIGH_SURR_B = 0xdfff;\n// Create intermediate states for astral characters in a range, if\n// necessary, since the tokenizer acts on UTF16 characters\nfunction rangeEdges(from, to, low, hi) {\n    if (low < ASTRAL) {\n        if (low < GAP_START)\n            from.edge(low, Math.min(hi, GAP_START), to);\n        if (hi > GAP_END)\n            from.edge(Math.max(low, GAP_END), Math.min(hi, MAX_CHAR + 1), to);\n        low = ASTRAL;\n    }\n    if (hi <= ASTRAL)\n        return;\n    let lowStr = String.fromCodePoint(low), hiStr = String.fromCodePoint(hi - 1);\n    let lowA = lowStr.charCodeAt(0), lowB = lowStr.charCodeAt(1);\n    let hiA = hiStr.charCodeAt(0), hiB = hiStr.charCodeAt(1);\n    if (lowA == hiA) { // Share the first char code\n        let hop = new State$1;\n        from.edge(lowA, lowA + 1, hop);\n        hop.edge(lowB, hiB + 1, to);\n    }\n    else {\n        let midStart = lowA, midEnd = hiA;\n        if (lowB > LOW_SURR_B) {\n            midStart++;\n            let hop = new State$1;\n            from.edge(lowA, lowA + 1, hop);\n            hop.edge(lowB, HIGH_SURR_B + 1, to);\n        }\n        if (hiB < HIGH_SURR_B) {\n            midEnd--;\n            let hop = new State$1;\n            from.edge(hiA, hiA + 1, hop);\n            hop.edge(LOW_SURR_B, hiB + 1, to);\n        }\n        if (midStart <= midEnd) {\n            let hop = new State$1;\n            from.edge(midStart, midEnd + 1, hop);\n            hop.edge(LOW_SURR_B, HIGH_SURR_B + 1, to);\n        }\n    }\n}\nfunction isEmpty(expr) {\n    return expr instanceof SequenceExpression && expr.exprs.length == 0;\n}\nfunction gatherExtTokens(b, tokens) {\n    let result = Object.create(null);\n    for (let token of tokens) {\n        b.unique(token.id);\n        let { name, props, dialect } = b.nodeInfo(token.props, \"d\", token.id.name);\n        let term = b.makeTerminal(token.id.name, name, props);\n        if (dialect != null)\n            (b.tokens.byDialect[dialect] || (b.tokens.byDialect[dialect] = [])).push(term);\n        b.namedTerms[token.id.name] = result[token.id.name] = term;\n    }\n    return result;\n}\nfunction findExtToken(b, tokens, expr) {\n    let found = tokens[expr.id.name];\n    if (!found)\n        return null;\n    if (expr.args.length)\n        b.raise(\"External tokens cannot take arguments\", expr.args[0].start);\n    b.used(expr.id.name);\n    return found;\n}\nfunction addRel(rel, term, after) {\n    let found = rel.findIndex(r => r.term == term);\n    if (found < 0)\n        rel.push({ term, after });\n    else\n        rel[found] = { term, after: rel[found].after.concat(after) };\n}\nclass ExternalTokenSet {\n    constructor(b, ast) {\n        this.b = b;\n        this.ast = ast;\n        this.tokens = gatherExtTokens(b, ast.tokens);\n        for (let name in this.tokens)\n            this.b.tokenOrigins[this.tokens[name].name] = { external: this };\n    }\n    getToken(expr) { return findExtToken(this.b, this.tokens, expr); }\n    create() {\n        return this.b.options.externalTokenizer(this.ast.id.name, this.b.termTable);\n    }\n    createSource(importName) {\n        let { source, id: { name } } = this.ast;\n        return importName(name, source);\n    }\n}\nclass ExternalSpecializer {\n    constructor(b, ast) {\n        this.b = b;\n        this.ast = ast;\n        this.term = null;\n        this.tokens = gatherExtTokens(b, ast.tokens);\n    }\n    finish() {\n        let terms = this.b.normalizeExpr(this.ast.token);\n        if (terms.length != 1 || terms[0].terms.length != 1 || !terms[0].terms[0].terminal)\n            this.b.raise(`The token expression to '@external ${this.ast.type}' must resolve to a token`, this.ast.token.start);\n        this.term = terms[0].terms[0];\n        for (let name in this.tokens)\n            this.b.tokenOrigins[this.tokens[name].name] = { spec: this.term, external: this };\n    }\n    getToken(expr) { return findExtToken(this.b, this.tokens, expr); }\n}\nfunction inlineRules(rules, preserve) {\n    for (let pass = 0;; pass++) {\n        let inlinable = Object.create(null), found;\n        if (pass == 0)\n            for (let rule of rules) {\n                if (rule.name.inline && !inlinable[rule.name.name]) {\n                    let group = rules.filter(r => r.name == rule.name);\n                    if (group.some(r => r.parts.includes(rule.name)))\n                        continue;\n                    found = inlinable[rule.name.name] = group;\n                }\n            }\n        for (let i = 0; i < rules.length; i++) {\n            let rule = rules[i];\n            if (!rule.name.interesting && !rule.parts.includes(rule.name) && rule.parts.length < 3 &&\n                !preserve.includes(rule.name) &&\n                (rule.parts.length == 1 || rules.every(other => other.skip == rule.skip || !other.parts.includes(rule.name))) &&\n                !rule.parts.some(p => !!inlinable[p.name]) &&\n                !rules.some((r, j) => j != i && r.name == rule.name))\n                found = inlinable[rule.name.name] = [rule];\n        }\n        if (!found)\n            return rules;\n        let newRules = [];\n        for (let rule of rules) {\n            if (inlinable[rule.name.name])\n                continue;\n            if (!rule.parts.some(p => !!inlinable[p.name])) {\n                newRules.push(rule);\n                continue;\n            }\n            function expand(at, conflicts, parts) {\n                if (at == rule.parts.length) {\n                    newRules.push(new Rule(rule.name, parts, conflicts, rule.skip));\n                    return;\n                }\n                let next = rule.parts[at], replace = inlinable[next.name];\n                if (!replace) {\n                    expand(at + 1, conflicts.concat(rule.conflicts[at + 1]), parts.concat(next));\n                    return;\n                }\n                for (let r of replace)\n                    expand(at + 1, conflicts.slice(0, conflicts.length - 1)\n                        .concat(conflicts[at].join(r.conflicts[0]))\n                        .concat(r.conflicts.slice(1, r.conflicts.length - 1))\n                        .concat(rule.conflicts[at + 1].join(r.conflicts[r.conflicts.length - 1])), parts.concat(r.parts));\n            }\n            expand(0, [rule.conflicts[0]], []);\n        }\n        rules = newRules;\n    }\n}\nfunction mergeRules(rules) {\n    let merged = Object.create(null), found;\n    for (let i = 0; i < rules.length;) {\n        let groupStart = i;\n        let name = rules[i++].name;\n        while (i < rules.length && rules[i].name == name)\n            i++;\n        let size = i - groupStart;\n        if (name.interesting)\n            continue;\n        for (let j = i; j < rules.length;) {\n            let otherStart = j, otherName = rules[j++].name;\n            while (j < rules.length && rules[j].name == otherName)\n                j++;\n            if (j - otherStart != size || otherName.interesting)\n                continue;\n            let match = true;\n            for (let k = 0; k < size && match; k++) {\n                let a = rules[groupStart + k], b = rules[otherStart + k];\n                if (a.cmpNoName(b) != 0)\n                    match = false;\n            }\n            if (match)\n                found = merged[name.name] = otherName;\n        }\n    }\n    if (!found)\n        return rules;\n    let newRules = [];\n    for (let rule of rules)\n        if (!merged[rule.name.name]) {\n            newRules.push(rule.parts.every(p => !merged[p.name]) ? rule :\n                new Rule(rule.name, rule.parts.map(p => merged[p.name] || p), rule.conflicts, rule.skip));\n        }\n    return newRules;\n}\nfunction simplifyRules(rules, preserve) {\n    return mergeRules(inlineRules(rules, preserve));\n}\n/**\nBuild an in-memory parser instance for a given grammar. This is\nmostly useful for testing. If your grammar uses external\ntokenizers, you'll have to provide the `externalTokenizer` option\nfor the returned parser to be able to parse anything.\n*/\nfunction buildParser(text, options = {}) {\n    let builder = new Builder(text, options), parser = builder.getParser();\n    parser.termTable = builder.termTable;\n    return parser;\n}\nconst KEYWORDS = [\"await\", \"break\", \"case\", \"catch\", \"continue\", \"debugger\", \"default\", \"do\", \"else\", \"finally\",\n    \"for\", \"function\", \"if\", \"return\", \"switch\", \"throw\", \"try\", \"var\", \"while\", \"with\",\n    \"null\", \"true\", \"false\", \"instanceof\", \"typeof\", \"void\", \"delete\", \"new\", \"in\", \"this\",\n    \"const\", \"class\", \"extends\", \"export\", \"import\", \"super\", \"enum\", \"implements\", \"interface\",\n    \"let\", \"package\", \"private\", \"protected\", \"public\", \"static\", \"yield\", \"require\"];\n/**\nBuild the code that represents the parser tables for a given\ngrammar description. The `parser` property in the return value\nholds the main file that exports the `Parser` instance. The\n`terms` property holds a declaration file that defines constants\nfor all of the named terms in grammar, holding their ids as value.\nThis is useful when external code, such as a tokenizer, needs to\nbe able to use these ids. It is recommended to run a tree-shaking\nbundler when importing this file, since you usually only need a\nhandful of the many terms in your code.\n*/\nfunction buildParserFile(text, options = {}) {\n    return new Builder(text, options).getParserFile();\n}\nfunction ignored(name) {\n    let first = name[0];\n    return first == \"_\" || first.toUpperCase() != first;\n}\nfunction isExported(rule) {\n    return rule.props.some(p => p.at && p.name == \"export\");\n}\n\nexport { GenError, buildParser, buildParserFile };\n"], "names": [], "sourceRoot": ""}