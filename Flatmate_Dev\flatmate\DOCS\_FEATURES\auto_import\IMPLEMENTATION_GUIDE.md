# Implementation Guide: Auto-Import Folder Feature

## Overview
This guide provides step-by-step instructions for implementing the auto-import folder feature according to the established architecture and task breakdown.

## Prerequisites
- Python 3.8+ environment
- Existing Flatmate application structure
- Access to modify core services and configuration

## Implementation Steps

### Phase 1: Core Service Implementation

#### Step 1: Install Dependencies
```bash
# Navigate to project root
cd C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev

# Install watchdog library
pip install watchdog

# Verify installation
python -c "from watchdog.observers import Observer; print('Watchdog installed successfully')"
```

#### Step 2: Create AutoImportManager Service
Create the service file following the singleton pattern used by other core services:

**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py`

Follow the complete implementation from `TASKS.md` Task 2, ensuring:
- Proper imports from existing core modules
- Singleton pattern implementation
- Thread-safe queue initialization
- Logging integration

#### Step 3: Implement Configuration Loading
Add configuration loading logic to the `__init__` method:
- Read settings from `ConfigKeys.AutoImport.*`
- Set up default paths in Downloads folder
- Create necessary directories
- Validate path permissions

#### Step 4: Implement File System Monitoring
Add the `start()` method implementation:
- Check if feature is enabled
- Start worker thread for file processing
- Initialize watchdog Observer
- Set up event handler for CSV files

#### Step 5: Implement File Processing
Add worker thread and processing methods:
- `_process_files_worker()` - Main worker loop
- `_process_single_file()` - Individual file processing
- Integration with existing `dw_director` pipeline
- File movement to archive/failed folders

### Phase 2: Application Integration

#### Step 6: Update Configuration Defaults
**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\config\keys.py`

Modify the `get_defaults()` method (around line 116-120):
```python
# Auto-import
cls.AutoImport.ENABLED: False,
cls.AutoImport.IMPORT_PATH: str(Path.home() / "Downloads" / "flatmate_imports"),
cls.AutoImport.ARCHIVE_PATH: str(Path.home() / "Downloads" / "flatmate_imports" / "archive"),
cls.AutoImport.FAILED_PATH: str(Path.home() / "Downloads" / "flatmate_imports" / "failed_imports"),
```

#### Step 7: Integrate with Application Lifecycle
**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py`

Add AutoImportManager initialization in `initialize_application()` after line 67:
```python
# 8. Initialize Auto-Import Manager (if enabled)
log.info("\n=== Initializing Auto-Import Manager ===")
main_window.update_splash_message("Setting up auto-import monitoring...")
app.processEvents()

from .core.services.auto_import_manager import AutoImportManager
auto_import_manager = AutoImportManager()
if auto_import_manager.enabled:
    auto_import_manager.start()
    log.info("Auto-import monitoring started")
else:
    log.info("Auto-import is disabled")
```

Add cleanup in application shutdown (if not already handled by daemon threads).

### Phase 3: File Movement Implementation

#### Step 8: Add File Movement Methods
Add these methods to `AutoImportManager` class:

```python
def _move_to_archive(self, file_path: Path) -> None:
    """Move successfully processed file to archive folder"""
    try:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        archived_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
        archive_file = self.archive_path / archived_name
        
        shutil.move(str(file_path), str(archive_file))
        log.info(f"Archived: {file_path.name} -> {archived_name}")
        
    except Exception as e:
        log.error(f"Failed to archive file {file_path.name}: {e}")

def _move_to_failed(self, file_path: Path, error_message: str) -> None:
    """Move failed file to failed imports folder"""
    try:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        failed_name = f"{file_path.stem}_{timestamp}_FAILED{file_path.suffix}"
        failed_file = self.failed_path / failed_name
        
        shutil.move(str(file_path), str(failed_file))
        log.error(f"Moved to failed: {file_path.name} -> {failed_name} (Error: {error_message})")
        
        # Create error log file
        error_log = self.failed_path / f"{failed_name}.error.txt"
        with open(error_log, 'w') as f:
            f.write(f"File: {file_path.name}\n")
            f.write(f"Timestamp: {timestamp}\n")
            f.write(f"Error: {error_message}\n")
        
    except Exception as e:
        log.error(f"Failed to move failed file {file_path.name}: {e}")
```

#### Step 9: Implement Queue Management
Add the queue management method:

```python
def queue_file_for_processing(self, file_path: str) -> None:
    """Add file to processing queue"""
    try:
        self.processing_queue.put(file_path, block=False)
        log.debug(f"Queued for processing: {Path(file_path).name}")
    except queue.Full:
        log.error(f"Processing queue is full - dropping file: {Path(file_path).name}")
```

#### Step 10: Implement Graceful Shutdown
Complete the `stop()` method:

```python
def stop(self) -> None:
    """Stop monitoring and cleanup resources"""
    if not self.running:
        return
    
    log.info("Stopping auto-import monitoring...")
    
    # Stop file system observer
    if self.observer:
        self.observer.stop()
        self.observer.join(timeout=5.0)
    
    # Stop worker thread
    self.running = False
    if self.worker_thread and self.worker_thread.is_alive():
        # Signal shutdown
        try:
            self.processing_queue.put(None, timeout=1.0)
        except queue.Full:
            pass
        
        self.worker_thread.join(timeout=5.0)
    
    log.info("Auto-import monitoring stopped")
```

## Testing Strategy

### Unit Tests
Create test file: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\tests\test_auto_import_manager.py`

Test cases:
- Configuration loading
- File detection and queuing
- Processing pipeline integration
- File movement operations
- Error handling scenarios

### Integration Tests
- End-to-end file processing
- Application startup/shutdown
- Configuration changes
- Multiple file handling

### Manual Testing
1. Enable auto-import in configuration
2. Start application
3. Drop CSV file in import folder
4. Verify file is processed and moved to archive
5. Test with invalid CSV file
6. Verify error handling and failed folder

## Troubleshooting

### Common Issues
1. **Permission Errors**: Ensure import folder has read/write permissions
2. **Watchdog Not Working**: Check if folder exists and is accessible
3. **Files Not Processing**: Verify dw_director integration and CSV format
4. **Memory Issues**: Monitor queue size and processing speed

### Debug Logging
Enable debug logging to trace file processing:
```python
log.debug(f"File detected: {file_path}")
log.debug(f"Queue size: {self.processing_queue.qsize()}")
log.debug(f"Processing result: {result}")
```

## Performance Considerations

### Resource Usage
- Monitor CPU usage during file processing
- Limit queue size to prevent memory issues
- Use daemon threads to prevent blocking shutdown

### Scalability
- Single worker thread handles sequential processing
- Queue prevents overwhelming the system
- Debounce delay handles large file writes

---
*Created: 2025-07-21*
*Implementation order: Follow TASKS.md sequence for optimal results*
