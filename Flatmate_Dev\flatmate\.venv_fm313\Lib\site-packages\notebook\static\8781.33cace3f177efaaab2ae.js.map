{"version": 3, "file": "8781.33cace3f177efaaab2ae.js?v=33cace3f177efaaab2ae", "mappings": ";;;;;;;;;;;;;;;;;;ACAA;AACA;;AAEA;;AAE2D;;AAER;;AAEnD,mBAAO,CAAC,KAAY;AACpB,mBAAO,CAAC,KAAiB;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,QAAQ,qBAAwB;AAChC;AACA;AACA;AACA,uBAAuB,qBAAwB;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,2CAA2C,QAAQ,UAAU,OAAO;AACpE;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAO,CAAC,IAAkC;AAC9C,IAAI,mBAAO,CAAC,IAA4B;AACxC,IAAI,mBAAO,CAAC,KAA2B;AACvC,IAAI,mBAAO,CAAC,KAA6B;AACzC;AACA;;AAEA;AACA;AACA,MAAM,mBAAO,CAAC,KAAyC;AACvD,EAAE,mBAAO,CAAC,KAAqC;AAC/C,EAAE,mBAAO,CAAC,KAAwC;AAClD,EAAE,mBAAO,CAAC,KAA4C;AACtD,EAAE,mBAAO,CAAC,KAAkC;AAC5C,EAAE,mBAAO,CAAC,KAAsC;AAChD,EAAE,mBAAO,CAAC,KAAsC;AAChD;AACA,MAAM,8CAA2D,GAAG,GAAG;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8CAAwD,GAAG,GAAG;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,mBAAO,CAAC,IAAkC;AAChD;AACA,MAAM,8CAAyD,GAAG,GAAG;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8CAAuD,GAAG,GAAG;AACnE;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,mBAAO,CAAC,IAAiC;AAC/C;AACA,MAAM,8CAA0D,GAAG,GAAG;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8CAA8D,GAAG,GAAG;AAC1E;AACA;AACA;AACA,MAAM,8CAA2D,GAAG,GAAG;AACvE;AACA;AACA;AACA;AACA,MAAM,8CAA0D,GAAG,GAAG;AACtE;AACA;AACA;AACA;AACA,MAAM,6CAAoD,GAAG,GAAG;AAChE;AACA;AACA,MAAM,mBAAO,CAAC,KAAkC;AAChD,EAAE,mBAAO,CAAC,KAAmC;AAC7C,EAAE,mBAAO,CAAC,KAA2B;AACrC;AACA,MAAM,8CAAwD,GAAG,GAAG;AACpE;AACA;AACA,MAAM,mBAAO,CAAC,KAAoC;AAClD,EAAE,mBAAO,CAAC,KAA+B;AACzC,EAAE,mBAAO,CAAC,KAA+B;AACzC;AACA,MAAM,8CAAwD,GAAG,GAAG;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,mBAAO,CAAC,KAAqC;AACnD,EAAE,mBAAO,CAAC,KAAgC;AAC1C,EAAE,mBAAO,CAAC,KAAiC;AAC3C,EAAE,mBAAO,CAAC,KAAgC;AAC1C,EAAE,mBAAO,CAAC,KAAmC;AAC7C,EAAE,mBAAO,CAAC,KAAkC;AAC5C,EAAE,mBAAO,CAAC,KAAgD;AAC1D,EAAE,mBAAO,CAAC,KAAmC;AAC7C,EAAE,mBAAO,CAAC,KAAqC;AAC/C,EAAE,mBAAO,CAAC,KAA2B;AACrC;AACA;;AAEA,mBAAmB,6DAAU,2BAA2B;AACxD;AACA;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,KAAwC;AACxD;AACA,MAAM,8CAA2D,GAAG,GAAG;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,mBAAO,CAAC,IAAkC;AAChD;AACA,MAAM,8CAAuD,GAAG,GAAG;AACnE;AACA;AACA,MAAM,mBAAO,CAAC,KAAqC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,IAAgC;AAChD,EAAE,mBAAO,CAAC,KAAoC;AAC9C;AACA,MAAM,8CAAwD,GAAG,GAAG;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,mBAAO,CAAC,IAAkC;AAChD,EAAE,mBAAO,CAAC,KAAoC;AAC9C;AACA,MAAM,8CAAwD,GAAG,GAAG;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,6CAAmD,GAAG,GAAG;AAC/D;AACA;AACA;AACA;AACA,MAAM,8CAAuD,GAAG,GAAG;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8CAAuD,GAAG,GAAG;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8CAA0D,GAAG,GAAG;AACtE;AACA;AACA;AACA,MAAM,mBAAO,CAAC,KAAsC;AACpD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA,yBAAyB,6DAAU;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI,6DAAU;AACd;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,yDAAM;AACjB,UAAU,6DAAU;AACpB;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,6DAAU;AACjC;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;;AAEH;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB,QAAQ;AACxB;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,EAAE,6DAAU,2BAA2B,KAAK,y1GAAy1G,UAAU,wgBAAwgB,eAAe,2hCAA2hC,cAAc,mHAAmH,UAAU,yKAAyK;;;AAGrvK,6BAA6B,6DAAc;AAC3C,sBAAsB,wCAAoD;;AAE1E;AACA,0BAA0B,4CAA+C;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,KAAK,6DAAU;;AAEf;AACA;AACA;;AAEA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnaA,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA;;AAEA,4BAA4B,6BAAmB;AAC/C,wBAAwB,0CAAa;;AAErC,uBAAuB,+BAAa;AACpC;AACA,iBAAiB,uBAAM;AACvB,6BAA6B,8BAAkB;;AAE/C,aAAa,kCAAG,CAAC,mBAAO;;;;AAImF;AAC3G,OAAO,iDAAe,mBAAO,IAAI,mBAAO,UAAU,mBAAO,mBAAmB,EAAC;;;;;;ACzB7E,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAA8I;AAC9I;AACA;;AAEA,IAAI,iBAAO;;AAEX,iBAAO,qBAAqB,6BAAmB;AAC/C,iBAAO,iBAAiB,0CAAa;;AAErC,MAAM,iBAAO,UAAU,+BAAa;AACpC;AACA,iBAAO,UAAU,uBAAM;AACvB,iBAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,gBAAM,GAAG,kCAAG,CAAC,wBAAO,EAAE,iBAAO;;;;AAI+E;AAChH,OAAO,sDAAe,wBAAO,IAAI,wBAAO,UAAU,wBAAO,mBAAmB,EAAC;;;AC1B7E;AACA;AACA;AACA;;AAEgD;AACH;AACK;;AAE9B;AACK;;;;;;;;ACTzB,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA,IAAI,YAAO;;AAEX,YAAO,qBAAqB,6BAAmB;AAC/C,YAAO,iBAAiB,0CAAa;;AAErC,MAAM,YAAO,UAAU,+BAAa;AACpC;AACA,YAAO,UAAU,uBAAM;AACvB,YAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,WAAM,GAAG,kCAAG,CAAC,+CAAO,EAAE,YAAO;;;;AAI0E;AAC3G,OAAO,gFAAe,+CAAO,IAAI,+CAAO,UAAU,+CAAO,mBAAmB,EAAC;;;AC1BvB;AACd;;AAEpB;;;;;;ACFpB,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA,IAAI,kBAAO;;AAEX,kBAAO,qBAAqB,6BAAmB;AAC/C,kBAAO,iBAAiB,0CAAa;;AAErC,MAAM,kBAAO,UAAU,+BAAa;AACpC;AACA,kBAAO,UAAU,uBAAM;AACvB,kBAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,iBAAM,GAAG,kCAAG,CAAC,2CAAO,EAAE,kBAAO;;;;AAI0E;AAC3G,OAAO,4EAAe,2CAAO,IAAI,2CAAO,UAAU,2CAAO,mBAAmB,EAAC;;;AC1BzD;;;;;;ACCpB,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA,IAAI,uCAAO;;AAEX,uCAAO,qBAAqB,6BAAmB;AAC/C,uCAAO,iBAAiB,0CAAa;;AAErC,MAAM,uCAAO,UAAU,+BAAa;AACpC;AACA,uCAAO,UAAU,uBAAM;AACvB,uCAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,sCAAM,GAAG,kCAAG,CAAC,8CAAO,EAAE,uCAAO;;;;AAI0E;AAC3G,OAAO,+EAAe,8CAAO,IAAI,8CAAO,UAAU,8CAAO,mBAAmB,EAAC;;;AC1BzD;;;;;;ACCpB,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA,IAAI,2CAAO;;AAEX,2CAAO,qBAAqB,6BAAmB;AAC/C,2CAAO,iBAAiB,0CAAa;;AAErC,MAAM,2CAAO,UAAU,+BAAa;AACpC;AACA,2CAAO,UAAU,uBAAM;AACvB,2CAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,0CAAM,GAAG,kCAAG,CAAC,kDAAO,EAAE,2CAAO;;;;AAI0E;AAC3G,OAAO,mFAAe,kDAAO,IAAI,kDAAO,UAAU,kDAAO,mBAAmB,EAAC;;;AC1BzD;;;;;;ACCpB,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA,IAAI,iCAAO;;AAEX,iCAAO,qBAAqB,6BAAmB;AAC/C,iCAAO,iBAAiB,0CAAa;;AAErC,MAAM,iCAAO,UAAU,+BAAa;AACpC;AACA,iCAAO,UAAU,uBAAM;AACvB,iCAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,gCAAM,GAAG,kCAAG,CAAC,wCAAO,EAAE,iCAAO;;;;AAI0E;AAC3G,OAAO,yEAAe,wCAAO,IAAI,wCAAO,UAAU,wCAAO,mBAAmB,EAAC;;;AC1BzD;;;;;;ACCpB,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA,IAAI,qCAAO;;AAEX,qCAAO,qBAAqB,6BAAmB;AAC/C,qCAAO,iBAAiB,0CAAa;;AAErC,MAAM,qCAAO,UAAU,+BAAa;AACpC;AACA,qCAAO,UAAU,uBAAM;AACvB,qCAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,oCAAM,GAAG,kCAAG,CAAC,4CAAO,EAAE,qCAAO;;;;AAI0E;AAC3G,OAAO,6EAAe,4CAAO,IAAI,4CAAO,UAAU,4CAAO,mBAAmB,EAAC;;;AC1BzD;;;;;;ACCpB,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA,IAAI,qCAAO;;AAEX,qCAAO,qBAAqB,6BAAmB;AAC/C,qCAAO,iBAAiB,0CAAa;;AAErC,MAAM,qCAAO,UAAU,+BAAa;AACpC;AACA,qCAAO,UAAU,uBAAM;AACvB,qCAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,oCAAM,GAAG,kCAAG,CAAC,4CAAO,EAAE,qCAAO;;;;AAI0E;AAC3G,OAAO,6EAAe,4CAAO,IAAI,4CAAO,UAAU,4CAAO,mBAAmB,EAAC;;;AC1BzD;;;;;;;;ACCpB,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA,IAAI,uBAAO;;AAEX,uBAAO,qBAAqB,6BAAmB;AAC/C,uBAAO,iBAAiB,0CAAa;;AAErC,MAAM,uBAAO,UAAU,+BAAa;AACpC;AACA,uBAAO,UAAU,uBAAM;AACvB,uBAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,sBAAM,GAAG,kCAAG,CAAC,8BAAO,EAAE,uBAAO;;;;AAI0E;AAC3G,OAAO,+DAAe,8BAAO,IAAI,8BAAO,UAAU,8BAAO,mBAAmB,EAAC;;;AC1B7B;;AAE5B;;;;;;ACDpB,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA,IAAI,iCAAO;;AAEX,iCAAO,qBAAqB,6BAAmB;AAC/C,iCAAO,iBAAiB,0CAAa;;AAErC,MAAM,iCAAO,UAAU,+BAAa;AACpC;AACA,iCAAO,UAAU,uBAAM;AACvB,iCAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,gCAAM,GAAG,kCAAG,CAAC,wCAAO,EAAE,iCAAO;;;;AAI0E;AAC3G,OAAO,yEAAe,wCAAO,IAAI,wCAAO,UAAU,wCAAO,mBAAmB,EAAC;;;AC1B7B;;AAED;;AAE3B;;;;;;ACHpB,MAAsI;AACtI,MAA4H;AAC5H,MAAmI;AACnI,MAAsJ;AACtJ,MAA+I;AAC/I,MAA+I;AAC/I,MAAyI;AACzI;AACA;;AAEA,IAAI,gCAAO;;AAEX,gCAAO,qBAAqB,6BAAmB;AAC/C,gCAAO,iBAAiB,0CAAa;;AAErC,MAAM,gCAAO,UAAU,+BAAa;AACpC;AACA,gCAAO,UAAU,uBAAM;AACvB,gCAAO,sBAAsB,8BAAkB;;AAE/C,IAAI,+BAAM,GAAG,kCAAG,CAAC,uCAAO,EAAE,gCAAO;;;;AAI0E;AAC3G,OAAO,wEAAe,uCAAO,IAAI,uCAAO,UAAU,uCAAO,mBAAmB,EAAC;;;AC1B7E;AACA;AACA;AACA;;AAEoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLpB;AACA;;AAEgE;AACV;AACM;AACG;AACI;AACV;AACI;AACA;AACJ;AACD;AACE;AACH;AACI;AACJ;AACE;AACD;AACF;AACE;AACD;AACE;AACI;AACE;AACL;AACD;AACN;AACM;AACP;AACQ;AACD;AACN;AACM;AACP;AACK;AACM;AACF;AACL;AACA;AACK;AACJ;AACL;AACU;AACN;AACM;AACJ;AACD;AACL;AACI;AACI;AACE;AACR;;;;;;;;;;;;;;;;ACpDpD;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,sHAAsH,QAAQ,MAAM,UAAU,UAAU,OAAO,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,UAAU,YAAY,WAAW,OAAO,KAAK,YAAY,OAAO,aAAa,MAAM,UAAU,MAAM,KAAK,YAAY,WAAW,MAAM,YAAY,MAAM,UAAU,2TAA2T,iBAAiB,mBAAmB,GAAG,wBAAwB,iBAAiB,GAAG,oCAAoC,kBAAkB,GAAG,kBAAkB,mBAAmB,kBAAkB,uBAAuB,oBAAoB,GAAG,uCAAuC,wCAAwC,GAAG,sHAAsH,kBAAkB,GAAG,sBAAsB,2EAA2E,GAAG,sEAAsE,kBAAkB,GAAG,qBAAqB;AAC71C;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;ACpDvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,2GAA2G,QAAQ,MAAM,YAAY,aAAa,aAAa,aAAa,OAAO,OAAO,KAAK,KAAK,UAAU,UAAU,YAAY,OAAO,KAAK,YAAY,WAAW,UAAU,UAAU,UAAU,YAAY,OAAO,KAAK,YAAY,aAAa,aAAa,OAAO,KAAK,UAAU,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,OAAO,KAAK,YAAY,aAAa,aAAa,aAAa,OAAO,KAAK,UAAU,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,OAAO,KAAK,YAAY,aAAa,aAAa,OAAO,KAAK,YAAY,OAAO,aAAa,MAAM,YAAY,OAAO,aAAa,MAAM,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,aAAa,aAAa,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,KAAK,YAAY,MAAM,2SAA2S,qCAAqC,mFAAmF,oCAAoC,GAAG,wJAAwJ,cAAc,eAAe,wCAAwC,GAAG,8BAA8B,uBAAuB,WAAW,YAAY,aAAa,cAAc,wCAAwC,GAAG,wBAAwB,4DAA4D,wEAAwE,wCAAwC,GAAG,gBAAgB,kBAAkB,4DAA4D,sBAAsB,uBAAuB,sBAAsB,uBAAuB,sBAAsB,GAAG,yBAAyB,gDAAgD,wCAAwC,wEAAwE,uCAAuC,GAAG,iBAAiB,kBAAkB,gDAAgD,wCAAwC,sBAAsB,uBAAuB,sBAAsB,uBAAuB,4CAA4C,GAAG,iBAAiB,sBAAsB,uBAAuB,4CAA4C,GAAG,wBAAwB,qBAAqB,GAAG,0GAA0G,qBAAqB,GAAG,iIAAiI,uCAAuC,GAAG,+CAA+C,uCAAuC,GAAG,iDAAiD,uBAAuB,wBAAwB,qBAAqB,GAAG,wDAAwD,sBAAsB,GAAG,iCAAiC,qBAAqB,GAAG,+CAA+C,iCAAiC,uBAAuB,KAAK,GAAG,qBAAqB;AACnhH;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;ACrHvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,kHAAkH,QAAQ,OAAO,QAAQ,MAAM,YAAY,OAAO,MAAM,QAAQ,cAAc,OAAO,UAAU,YAAY,aAAa,OAAO,MAAM,UAAU,UAAU,UAAU,UAAU,MAAM,KAAK,YAAY,OAAO,KAAK,YAAY,OAAO,MAAM,UAAU,uiBAAuiB,yCAAyC,GAAG,2OAA2O,kBAAkB,2BAA2B,2CAA2C,GAAG,oFAAoF,kBAAkB,mBAAmB,kBAAkB,eAAe,GAAG,2CAA2C,2BAA2B,GAAG,4CAA4C,0BAA0B,GAAG,wEAAwE,mBAAmB,GAAG,qBAAqB;AACrqD;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;ACvDvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA,iDAAiD,kEAAkE;AACnH;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;ACPvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA;AACA;AACA;AACA,OAAO,kHAAkH,UAAU,uCAAuC,iBAAiB,GAAG,qBAAqB;AACnN;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;ACVvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA,iDAAiD,kEAAkE;AACnH;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;ACPvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,4GAA4G,YAAY,WAAW,MAAM,KAAK,UAAU,YAAY,aAAa,aAAa,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,OAAO,KAAK,UAAU,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU,YAAY,aAAa,aAAa,aAAa,OAAO,KAAK,YAAY,+DAA+D,4BAA4B,eAAe,GAAG,8BAA8B,kBAAkB,wBAAwB,wBAAwB,2CAA2C,GAAG,mCAAmC,sBAAsB,GAAG,+BAA+B,oCAAoC,uCAAuC,yBAAyB,qBAAqB,0BAA0B,sBAAsB,qBAAqB,uBAAuB,GAAG,4BAA4B,kBAAkB,uCAAuC,2CAA2C,oCAAoC,uBAAuB,2BAA2B,qBAAqB,qBAAqB,GAAG,sCAAsC,0BAA0B,GAAG,2CAA2C,kBAAkB,2BAA2B,gCAAgC,4BAA4B,iCAAiC,GAAG,uCAAuC,sBAAsB,GAAG,qBAAqB;AAC1rD;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;;AC5DvC;AACiJ;AACjB;AACgB;AAChJ,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F,0BAA0B,mIAAiC;AAC3D;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,mHAAmH,QAAQ,aAAa,MAAM,aAAa,QAAQ,YAAY,aAAa,WAAW,MAAM,aAAa,MAAM,YAAY,aAAa,OAAO,KAAK,YAAY,aAAa,aAAa,SAAS,KAAK,YAAY,OAAO,KAAK,YAAY,aAAa,aAAa,OAAO,KAAK,YAAY,OAAO,YAAY,QAAQ,YAAY,OAAO,aAAa,MAAM,YAAY,OAAO,OAAO,YAAY,OAAO,OAAO,UAAU,UAAU,YAAY,WAAW,UAAU,MAAM,aAAa,MAAM,YAAY,aAAa,OAAO,MAAM,KAAK,OAAO,UAAU,MAAM,KAAK,YAAY,QAAQ,YAAY,OAAO,MAAM,YAAY,MAAM,MAAM,YAAY,MAAM,YAAY,WAAW,YAAY,OAAO,aAAa,MAAM,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,YAAY,cAAc,aAAa,aAAa,aAAa,OAAO,aAAa,QAAQ,UAAU,YAAY,WAAW,UAAU,UAAU,UAAU,YAAY,OAAO,aAAa,MAAM,UAAU,UAAU,YAAY,aAAa,aAAa,OAAO,KAAK,UAAU,YAAY,WAAW,MAAM,KAAK,UAAU,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,aAAa,WAAW,YAAY,aAAa,WAAW,MAAM,KAAK,UAAU,OAAO,KAAK,KAAK,UAAU,KAAK,KAAK,UAAU,KAAK,MAAM,KAAK,UAAU,UAAU,UAAU,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU,YAAY,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,OAAO,KAAK,YAAY,WAAW,MAAM,KAAK,YAAY,WAAW,UAAU,UAAU,UAAU,UAAU,UAAU,YAAY,aAAa,aAAa,aAAa,OAAO,KAAK,YAAY,aAAa,kUAAkU,gfAAgf,wBAAwB,oCAAoC,iBAAiB,GAAG,+GAA+G,wEAAwE,yEAAyE,GAAG,6DAA6D,4BAA4B,uBAAuB,wEAAwE,oJAAoJ,wCAAwC,GAAG,6DAA6D,yDAAyD,2HAA2H,GAAG,uDAAuD,wCAAwC,GAAG,kLAAkL,sBAAsB,GAAG,gGAAgG,wCAAwC,GAAG,6IAA6I,mDAAmD,GAAG,oGAAoG,iBAAiB,iBAAiB,uBAAuB,WAAW,gBAAgB,GAAG,wFAAwF,sBAAsB,sBAAsB,GAAG,yLAAyL,eAAe,GAAG,+CAA+C,8IAA8I,sCAAsC,KAAK,0IAA0I,kDAAkD,KAAK,GAAG,8GAA8G,sBAAsB,gBAAgB,uBAAuB,GAAG,4EAA4E,kBAAkB,GAAG,wEAAwE,kBAAkB,GAAG,wEAAwE,kBAAkB,GAAG,gEAAgE,wCAAwC,uCAAuC,iIAAiI,yDAAyD,GAAG,wHAAwH,gBAAgB,uBAAuB,WAAW,cAAc,YAAY,aAAa,8DAA8D,GAAG,sGAAsG,mBAAmB,kBAAkB,wBAAwB,uBAAuB,sBAAsB,GAAG,gCAAgC,oBAAoB,qBAAqB,kBAAkB,GAAG,8BAA8B,cAAc,wBAAwB,uCAAuC,oCAAoC,0CAA0C,sDAAsD,kDAAkD,mDAAmD,GAAG,oCAAoC,6CAA6C,GAAG,mCAAmC,4CAA4C,GAAG,mCAAmC,4CAA4C,GAAG,mCAAmC,sCAAsC,GAAG,+BAA+B,wCAAwC,oCAAoC,oBAAoB,uBAAuB,8CAA8C,iBAAiB,GAAG,2CAA2C,oBAAoB,GAAG,yBAAyB,QAAQ,iBAAiB,KAAK,UAAU,iBAAiB,KAAK,GAAG,kBAAkB,oBAAoB,oBAAoB,cAAc,wBAAwB,oCAAoC,0CAA0C,kEAAkE,4BAA4B,qBAAqB,wBAAwB,GAAG,wBAAwB,wCAAwC,GAAG,4BAA4B,oBAAoB,qBAAqB,sBAAsB,wBAAwB,oCAAoC,0CAA0C,kEAAkE,4BAA4B,qBAAqB,wBAAwB,GAAG,kBAAkB,uBAAuB,gBAAgB,GAAG,+BAA+B,uBAAuB,mBAAmB,WAAW,cAAc,mBAAmB,iBAAiB,eAAe,uCAAuC,uBAAuB,wCAAwC,uBAAuB,GAAG,iCAAiC,+BAA+B,wCAAwC,GAAG,qBAAqB;AACtyR;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;AC7RvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO,qHAAqH,YAAY,cAAc,aAAa,iCAAiC,8CAA8C,uCAAuC,sCAAsC,GAAG,qBAAqB;AACvV;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;ACbvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA,iDAAiD,kEAAkE;AACnH;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;ACPvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,+GAA+G,QAAQ,QAAQ,YAAY,WAAW,UAAU,MAAM,OAAO,YAAY,OAAO,KAAK,YAAY,aAAa,aAAa,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,OAAO,aAAa,MAAM,UAAU,MAAM,KAAK,YAAY,OAAO,OAAO,YAAY,OAAO,KAAK,YAAY,WAAW,YAAY,OAAO,KAAK,UAAU,geAAge,8CAA8C,gBAAgB,iBAAiB,GAAG,qNAAqN,kDAAkD,GAAG,8CAA8C,6CAA6C,yBAAyB,wBAAwB,GAAG,wDAAwD,yCAAyC,GAAG,uEAAuE,wBAAwB,GAAG,2DAA2D,wBAAwB,GAAG,yGAAyG,kBAAkB,GAAG,kDAAkD,6CAA6C,GAAG,kIAAkI,4CAA4C,GAAG,4CAA4C,8CAA8C,gBAAgB,iDAAiD,GAAG,wDAAwD,kBAAkB,GAAG,qBAAqB;AAC50E;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;ACtEvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,kGAAkG,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,YAAY,aAAa,aAAa,aAAa,OAAO,KAAK,UAAU,MAAM,KAAK,UAAU,YAAY,aAAa,WAAW,MAAM,KAAK,YAAY,aAAa,OAAO,KAAK,UAAU,MAAM,KAAK,YAAY,aAAa,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU,MAAM,KAAK,YAAY,2CAA2C,iBAAiB,GAAG,kBAAkB,iBAAiB,GAAG,uCAAuC,sBAAsB,qBAAqB,yBAAyB,6CAA6C,GAAG,sCAAsC,iBAAiB,GAAG,kCAAkC,mBAAmB,oCAAoC,uCAAuC,iBAAiB,GAAG,uCAAuC,sBAAsB,uBAAuB,GAAG,mEAAmE,iBAAiB,GAAG,gDAAgD,2BAA2B,4BAA4B,GAAG,+CAA+C,wBAAwB,GAAG,oFAAoF,kBAAkB,GAAG,0CAA0C,wBAAwB,GAAG,qBAAqB;AAC36C;AACA,iEAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;AC1DvC;AACiJ;AACjB;AAChI,8BAA8B,mHAA2B,CAAC,4HAAqC;AAC/F;AACA,iDAAiD,kEAAkE;AACnH;AACA,iEAAe,uBAAuB,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/./build/extraStyle.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/./build/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/style/base.css?2870", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/style/sidepanel.css?8b8c", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application-extension/style/base.css?78a8", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application-extension/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/console-extension/style/base.css?1a24", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/console-extension/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/docmanager-extension/style/base.css?b533", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/docmanager-extension/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/documentsearch-extension/style/base.css?1b32", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/documentsearch-extension/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/help-extension/style/base.css?b991", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/help-extension/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/notebook-extension/style/base.css?bd66", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/notebook-extension/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/terminal-extension/style/base.css?b73f", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/terminal-extension/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree/style/base.css?adfc", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree-extension/style/base.css?aa5d", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree-extension/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/ui-components/style/base.css?7130", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/ui-components/style/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/./build/style.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application-extension/style/base.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/style/base.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/application/style/sidepanel.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/console-extension/style/base.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/docmanager-extension/style/base.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/documentsearch-extension/style/base.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/help-extension/style/base.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/notebook-extension/style/base.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/notebook-extension/style/variables.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/terminal-extension/style/base.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree-extension/style/base.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree/style/base.css", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/ui-components/style/base.css"], "sourcesContent": ["", "// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\n\n// Inspired by: https://github.com/jupyterlab/jupyterlab/blob/master/dev_mode/index.js\n\nimport { PageConfig, URLExt } from '@jupyterlab/coreutils';\n\nimport { PluginRegistry } from '@lumino/coreutils';\n\nrequire('./style.js');\nrequire('./extraStyle.js');\n\nfunction loadScript(url) {\n  return new Promise((resolve, reject) => {\n    const newScript = document.createElement('script');\n    newScript.onerror = reject;\n    newScript.onload = resolve;\n    newScript.async = true;\n    document.head.appendChild(newScript);\n    newScript.src = url;\n  });\n}\nasync function loadComponent(url, scope) {\n  await loadScript(url);\n\n  // From MIT-licensed https://github.com/module-federation/module-federation-examples/blob/af043acd6be1718ee195b2511adf6011fba4233c/advanced-api/dynamic-remotes/app1/src/App.js#L6-L12\n  // eslint-disable-next-line no-undef\n  await __webpack_init_sharing__('default');\n  const container = window._JUPYTERLAB[scope];\n  // Initialize the container, it may provide shared modules and may need ours\n  // eslint-disable-next-line no-undef\n  await container.init(__webpack_share_scopes__.default);\n}\n\nasync function createModule(scope, module) {\n  try {\n    const factory = await window._JUPYTERLAB[scope].get(module);\n    const instance = factory();\n    instance.__scope__ = scope;\n    return instance;\n  } catch (e) {\n    console.warn(\n      `Failed to create module: package: ${scope}; module: ${module}`\n    );\n    throw e;\n  }\n}\n\n/**\n * The main function\n */\nasync function main() {\n  const mimeExtensionsMods = [\n    require('@jupyterlab/javascript-extension'),\n    require('@jupyterlab/json-extension'),\n    require('@jupyterlab/pdf-extension'),\n    require('@jupyterlab/vega5-extension'),\n  ];\n  const mimeExtensions = await Promise.all(mimeExtensionsMods);\n\n  // Load the base plugins available on all pages\n  let baseMods = [\n      require('@jupyter-notebook/application-extension'),\n  require('@jupyter-notebook/console-extension'),\n  require('@jupyter-notebook/docmanager-extension'),\n  require('@jupyter-notebook/documentsearch-extension'),\n  require('@jupyter-notebook/help-extension'),\n  require('@jupyter-notebook/notebook-extension'),\n  require('@jupyter-notebook/terminal-extension'),\n  \n      require('@jupyterlab/application-extension').default.filter(({id}) => [\n       '@jupyterlab/application-extension:commands',\n'@jupyterlab/application-extension:context-menu',\n'@jupyterlab/application-extension:faviconbusy',\n'@jupyterlab/application-extension:router',\n'@jupyterlab/application-extension:top-bar',\n'@jupyterlab/application-extension:top-spacer',\n      ].includes(id)),\n      \n      require('@jupyterlab/apputils-extension').default.filter(({id}) => [\n       '@jupyterlab/apputils-extension:kernels-settings',\n'@jupyterlab/apputils-extension:palette',\n'@jupyterlab/apputils-extension:notification',\n'@jupyterlab/apputils-extension:sanitizer',\n'@jupyterlab/apputils-extension:sessionDialogs',\n'@jupyterlab/apputils-extension:settings',\n'@jupyterlab/apputils-extension:state',\n'@jupyterlab/apputils-extension:themes',\n'@jupyterlab/apputils-extension:themes-palette-menu',\n'@jupyterlab/apputils-extension:toolbar-registry',\n'@jupyterlab/apputils-extension:utilityCommands',\n      ].includes(id)),\n      require('@jupyterlab/codemirror-extension'),\n  \n      require('@jupyterlab/completer-extension').default.filter(({id}) => [\n       '@jupyterlab/completer-extension:base-service',\n'@jupyterlab/completer-extension:inline-completer',\n'@jupyterlab/completer-extension:inline-completer-factory',\n'@jupyterlab/completer-extension:inline-history',\n'@jupyterlab/completer-extension:manager',\n      ].includes(id)),\n      \n      require('@jupyterlab/console-extension').default.filter(({id}) => [\n       '@jupyterlab/console-extension:cell-executor',\n'@jupyterlab/console-extension:completer',\n'@jupyterlab/console-extension:factory',\n'@jupyterlab/console-extension:foreign',\n'@jupyterlab/console-extension:tracker',\n      ].includes(id)),\n      require('@jupyterlab/csvviewer-extension'),\n  \n      require('@jupyterlab/docmanager-extension').default.filter(({id}) => [\n       '@jupyterlab/docmanager-extension:plugin',\n'@jupyterlab/docmanager-extension:download',\n'@jupyterlab/docmanager-extension:contexts',\n'@jupyterlab/docmanager-extension:manager',\n      ].includes(id)),\n      \n      require('@jupyterlab/documentsearch-extension').default.filter(({id}) => [\n       '@jupyterlab/documentsearch-extension:plugin',\n      ].includes(id)),\n      \n      require('@jupyterlab/filebrowser-extension').default.filter(({id}) => [\n       '@jupyterlab/filebrowser-extension:factory',\n'@jupyterlab/filebrowser-extension:default-file-browser',\n      ].includes(id)),\n      \n      require('@jupyterlab/fileeditor-extension').default.filter(({id}) => [\n       '@jupyterlab/fileeditor-extension:plugin',\n'@jupyterlab/fileeditor-extension:widget-factory',\n      ].includes(id)),\n      \n      require('@jupyterlab/help-extension').default.filter(({id}) => [\n       '@jupyterlab/help-extension:resources',\n      ].includes(id)),\n      require('@jupyterlab/htmlviewer-extension'),\n  require('@jupyterlab/imageviewer-extension'),\n  require('@jupyterlab/lsp-extension'),\n  \n      require('@jupyterlab/mainmenu-extension').default.filter(({id}) => [\n       '@jupyterlab/mainmenu-extension:plugin',\n      ].includes(id)),\n      require('@jupyterlab/markedparser-extension'),\n  require('@jupyterlab/mathjax-extension'),\n  require('@jupyterlab/mermaid-extension'),\n  \n      require('@jupyterlab/notebook-extension').default.filter(({id}) => [\n       '@jupyterlab/notebook-extension:cell-executor',\n'@jupyterlab/notebook-extension:code-console',\n'@jupyterlab/notebook-extension:export',\n'@jupyterlab/notebook-extension:factory',\n'@jupyterlab/notebook-extension:tracker',\n'@jupyterlab/notebook-extension:widget-factory',\n      ].includes(id)),\n      require('@jupyterlab/pluginmanager-extension'),\n  require('@jupyterlab/services-extension'),\n  require('@jupyterlab/shortcuts-extension'),\n  require('@jupyterlab/terminal-extension'),\n  require('@jupyterlab/theme-light-extension'),\n  require('@jupyterlab/theme-dark-extension'),\n  require('@jupyterlab/theme-dark-high-contrast-extension'),\n  require('@jupyterlab/translation-extension'),\n  require('@jupyterlab/ui-components-extension'),\n  require('@jupyterlab/hub-extension'),\n  \n  ];\n\n  const page = `/${PageConfig.getOption('notebookPage')}`;\n  switch (page) {\n    // list all the other plugins grouped by page\n    case '/tree': {\n      baseMods = baseMods.concat([\n        require('@jupyterlab/extensionmanager-extension'),\n  \n      require('@jupyterlab/filebrowser-extension').default.filter(({id}) => [\n       '@jupyterlab/filebrowser-extension:browser',\n'@jupyterlab/filebrowser-extension:download',\n'@jupyterlab/filebrowser-extension:file-upload-status',\n'@jupyterlab/filebrowser-extension:open-with',\n'@jupyterlab/filebrowser-extension:search',\n'@jupyterlab/filebrowser-extension:share-file',\n      ].includes(id)),\n      require('@jupyter-notebook/tree-extension'),\n  \n      require('@jupyterlab/running-extension').default.filter(({id}) => [\n       '@jupyterlab/running-extension:plugin',\n      ].includes(id)),\n      require('@jupyterlab/settingeditor-extension'),\n  \n      ]);\n      break;\n    }\n    // list all the other plugins grouped by page\n    case '/notebooks': {\n      baseMods = baseMods.concat([\n        require('@jupyterlab/celltags-extension'),\n  require('@jupyterlab/cell-toolbar-extension'),\n  \n      require('@jupyterlab/debugger-extension').default.filter(({id}) => [\n       '@jupyterlab/debugger-extension:config',\n'@jupyterlab/debugger-extension:main',\n'@jupyterlab/debugger-extension:notebooks',\n'@jupyterlab/debugger-extension:service',\n'@jupyterlab/debugger-extension:sidebar',\n'@jupyterlab/debugger-extension:sources',\n      ].includes(id)),\n      require('@jupyterlab/logconsole-extension'),\n  require('@jupyterlab/metadataform-extension'),\n  \n      require('@jupyterlab/notebook-extension').default.filter(({id}) => [\n       '@jupyterlab/notebook-extension:active-cell-tool',\n'@jupyterlab/notebook-extension:completer',\n'@jupyterlab/notebook-extension:copy-output',\n'@jupyterlab/notebook-extension:metadata-editor',\n'@jupyterlab/notebook-extension:search',\n'@jupyterlab/notebook-extension:toc',\n'@jupyterlab/notebook-extension:tools',\n'@jupyterlab/notebook-extension:update-raw-mimetype',\n      ].includes(id)),\n      \n      require('@jupyterlab/toc-extension').default.filter(({id}) => [\n       '@jupyterlab/toc-extension:registry',\n'@jupyterlab/toc-extension:tracker',\n      ].includes(id)),\n      \n      require('@jupyterlab/tooltip-extension').default.filter(({id}) => [\n       '@jupyterlab/tooltip-extension:manager',\n'@jupyterlab/tooltip-extension:notebooks',\n      ].includes(id)),\n      \n      ]);\n      break;\n    }\n    // list all the other plugins grouped by page\n    case '/consoles': {\n      baseMods = baseMods.concat([\n        \n      require('@jupyterlab/tooltip-extension').default.filter(({id}) => [\n       '@jupyterlab/tooltip-extension:manager',\n'@jupyterlab/tooltip-extension:consoles',\n      ].includes(id)),\n      \n      ]);\n      break;\n    }\n    // list all the other plugins grouped by page\n    case '/edit': {\n      baseMods = baseMods.concat([\n        \n      require('@jupyterlab/fileeditor-extension').default.filter(({id}) => [\n       '@jupyterlab/fileeditor-extension:completer',\n'@jupyterlab/fileeditor-extension:search',\n      ].includes(id)),\n      require('@jupyterlab/markdownviewer-extension'),\n  \n      ]);\n      break;\n    }\n  }\n\n  // populate the list of disabled extensions\n  const disabled = [];\n  const availablePlugins = [];\n\n  /**\n   * Iterate over active plugins in an extension.\n   *\n   * #### Notes\n   * This also populates the disabled\n   */\n  function* activePlugins(extension) {\n    // Handle commonjs or es2015 modules\n    let exports;\n    if (Object.prototype.hasOwnProperty.call(extension, '__esModule')) {\n      exports = extension.default;\n    } else {\n      // CommonJS exports.\n      exports = extension;\n    }\n\n    let plugins = Array.isArray(exports) ? exports : [exports];\n    for (let plugin of plugins) {\n      const isDisabled = PageConfig.Extension.isDisabled(plugin.id);\n      availablePlugins.push({\n        id: plugin.id,\n        description: plugin.description,\n        requires: plugin.requires ?? [],\n        optional: plugin.optional ?? [],\n        provides: plugin.provides ?? null,\n        autoStart: plugin.autoStart,\n        enabled: !isDisabled,\n        extension: extension.__scope__\n      });\n      if (isDisabled) {\n        disabled.push(plugin.id);\n        continue;\n      }\n      yield plugin;\n    }\n  }\n\n  const extension_data = JSON.parse(\n    PageConfig.getOption('federated_extensions')\n  );\n\n  const mods = [];\n  const federatedExtensionPromises = [];\n  const federatedMimeExtensionPromises = [];\n  const federatedStylePromises = [];\n\n  const extensions = await Promise.allSettled(\n    extension_data.map(async data => {\n      await loadComponent(\n        `${URLExt.join(\n          PageConfig.getOption('fullLabextensionsUrl'),\n          data.name,\n          data.load\n        )}`,\n        data.name\n      );\n      return data;\n    })\n  );\n\n  extensions.forEach(p => {\n    if (p.status === 'rejected') {\n      // There was an error loading the component\n      console.error(p.reason);\n      return;\n    }\n\n    const data = p.value;\n    if (data.extension) {\n      federatedExtensionPromises.push(createModule(data.name, data.extension));\n    }\n    if (data.mimeExtension) {\n      federatedMimeExtensionPromises.push(\n        createModule(data.name, data.mimeExtension)\n      );\n    }\n    if (data.style && !PageConfig.Extension.isDisabled(data.name)) {\n      federatedStylePromises.push(createModule(data.name, data.style));\n    }\n  });\n\n  // Add the base frontend extensions\n  const baseFrontendMods = await Promise.all(baseMods);\n  baseFrontendMods.forEach(p => {\n    for (let plugin of activePlugins(p)) {\n      mods.push(plugin);\n    }\n  });\n\n  // Add the federated extensions.\n  const federatedExtensions = await Promise.allSettled(\n    federatedExtensionPromises\n  );\n  federatedExtensions.forEach(p => {\n    if (p.status === 'fulfilled') {\n      for (let plugin of activePlugins(p.value)) {\n        mods.push(plugin);\n      }\n    } else {\n      console.error(p.reason);\n    }\n  });\n\n  // Add the federated mime extensions.\n  const federatedMimeExtensions = await Promise.allSettled(\n    federatedMimeExtensionPromises\n  );\n  federatedMimeExtensions.forEach(p => {\n    if (p.status === 'fulfilled') {\n      for (let plugin of activePlugins(p.value)) {\n        mimeExtensions.push(plugin);\n      }\n    } else {\n      console.error(p.reason);\n    }\n  });\n\n  // Load all federated component styles and log errors for any that do not\n  (await Promise.allSettled(federatedStylePromises))\n    .filter(({ status }) => status === 'rejected')\n    .forEach(({ reason }) => {\n      console.error(reason);\n    });\n\n  // Set the list of base notebook multi-page plugins so the app is aware of all\n  // its built-in plugins even if they are not loaded on the current page.\n  // For example this is useful so the Settings Editor can list the debugger\n  // plugin even if the debugger is only loaded on the notebook page.\n  PageConfig.setOption('allPlugins', '{\"/\":{\"@jupyter-notebook/application-extension\":true,\"@jupyter-notebook/console-extension\":true,\"@jupyter-notebook/docmanager-extension\":true,\"@jupyter-notebook/documentsearch-extension\":true,\"@jupyter-notebook/help-extension\":true,\"@jupyter-notebook/notebook-extension\":true,\"@jupyter-notebook/terminal-extension\":true,\"@jupyterlab/application-extension\":[\"@jupyterlab/application-extension:commands\",\"@jupyterlab/application-extension:context-menu\",\"@jupyterlab/application-extension:faviconbusy\",\"@jupyterlab/application-extension:router\",\"@jupyterlab/application-extension:top-bar\",\"@jupyterlab/application-extension:top-spacer\"],\"@jupyterlab/apputils-extension\":[\"@jupyterlab/apputils-extension:kernels-settings\",\"@jupyterlab/apputils-extension:palette\",\"@jupyterlab/apputils-extension:notification\",\"@jupyterlab/apputils-extension:sanitizer\",\"@jupyterlab/apputils-extension:sessionDialogs\",\"@jupyterlab/apputils-extension:settings\",\"@jupyterlab/apputils-extension:state\",\"@jupyterlab/apputils-extension:themes\",\"@jupyterlab/apputils-extension:themes-palette-menu\",\"@jupyterlab/apputils-extension:toolbar-registry\",\"@jupyterlab/apputils-extension:utilityCommands\"],\"@jupyterlab/codemirror-extension\":true,\"@jupyterlab/completer-extension\":[\"@jupyterlab/completer-extension:base-service\",\"@jupyterlab/completer-extension:inline-completer\",\"@jupyterlab/completer-extension:inline-completer-factory\",\"@jupyterlab/completer-extension:inline-history\",\"@jupyterlab/completer-extension:manager\"],\"@jupyterlab/console-extension\":[\"@jupyterlab/console-extension:cell-executor\",\"@jupyterlab/console-extension:completer\",\"@jupyterlab/console-extension:factory\",\"@jupyterlab/console-extension:foreign\",\"@jupyterlab/console-extension:tracker\"],\"@jupyterlab/csvviewer-extension\":true,\"@jupyterlab/docmanager-extension\":[\"@jupyterlab/docmanager-extension:plugin\",\"@jupyterlab/docmanager-extension:download\",\"@jupyterlab/docmanager-extension:contexts\",\"@jupyterlab/docmanager-extension:manager\"],\"@jupyterlab/documentsearch-extension\":[\"@jupyterlab/documentsearch-extension:plugin\"],\"@jupyterlab/filebrowser-extension\":[\"@jupyterlab/filebrowser-extension:factory\",\"@jupyterlab/filebrowser-extension:default-file-browser\"],\"@jupyterlab/fileeditor-extension\":[\"@jupyterlab/fileeditor-extension:plugin\",\"@jupyterlab/fileeditor-extension:widget-factory\"],\"@jupyterlab/help-extension\":[\"@jupyterlab/help-extension:resources\"],\"@jupyterlab/htmlviewer-extension\":true,\"@jupyterlab/imageviewer-extension\":true,\"@jupyterlab/lsp-extension\":true,\"@jupyterlab/mainmenu-extension\":[\"@jupyterlab/mainmenu-extension:plugin\"],\"@jupyterlab/markedparser-extension\":true,\"@jupyterlab/mathjax-extension\":true,\"@jupyterlab/mermaid-extension\":true,\"@jupyterlab/notebook-extension\":[\"@jupyterlab/notebook-extension:cell-executor\",\"@jupyterlab/notebook-extension:code-console\",\"@jupyterlab/notebook-extension:export\",\"@jupyterlab/notebook-extension:factory\",\"@jupyterlab/notebook-extension:tracker\",\"@jupyterlab/notebook-extension:widget-factory\"],\"@jupyterlab/pluginmanager-extension\":true,\"@jupyterlab/services-extension\":true,\"@jupyterlab/shortcuts-extension\":true,\"@jupyterlab/terminal-extension\":true,\"@jupyterlab/theme-light-extension\":true,\"@jupyterlab/theme-dark-extension\":true,\"@jupyterlab/theme-dark-high-contrast-extension\":true,\"@jupyterlab/translation-extension\":true,\"@jupyterlab/ui-components-extension\":true,\"@jupyterlab/hub-extension\":true},\"/tree\":{\"@jupyterlab/extensionmanager-extension\":true,\"@jupyterlab/filebrowser-extension\":[\"@jupyterlab/filebrowser-extension:browser\",\"@jupyterlab/filebrowser-extension:download\",\"@jupyterlab/filebrowser-extension:file-upload-status\",\"@jupyterlab/filebrowser-extension:open-with\",\"@jupyterlab/filebrowser-extension:search\",\"@jupyterlab/filebrowser-extension:share-file\"],\"@jupyter-notebook/tree-extension\":true,\"@jupyterlab/running-extension\":[\"@jupyterlab/running-extension:plugin\"],\"@jupyterlab/settingeditor-extension\":true},\"/notebooks\":{\"@jupyterlab/celltags-extension\":true,\"@jupyterlab/cell-toolbar-extension\":true,\"@jupyterlab/debugger-extension\":[\"@jupyterlab/debugger-extension:config\",\"@jupyterlab/debugger-extension:main\",\"@jupyterlab/debugger-extension:notebooks\",\"@jupyterlab/debugger-extension:service\",\"@jupyterlab/debugger-extension:sidebar\",\"@jupyterlab/debugger-extension:sources\"],\"@jupyterlab/logconsole-extension\":true,\"@jupyterlab/metadataform-extension\":true,\"@jupyterlab/notebook-extension\":[\"@jupyterlab/notebook-extension:active-cell-tool\",\"@jupyterlab/notebook-extension:completer\",\"@jupyterlab/notebook-extension:copy-output\",\"@jupyterlab/notebook-extension:metadata-editor\",\"@jupyterlab/notebook-extension:search\",\"@jupyterlab/notebook-extension:toc\",\"@jupyterlab/notebook-extension:tools\",\"@jupyterlab/notebook-extension:update-raw-mimetype\"],\"@jupyterlab/toc-extension\":[\"@jupyterlab/toc-extension:registry\",\"@jupyterlab/toc-extension:tracker\"],\"@jupyterlab/tooltip-extension\":[\"@jupyterlab/tooltip-extension:manager\",\"@jupyterlab/tooltip-extension:notebooks\"]},\"/consoles\":{\"@jupyterlab/tooltip-extension\":[\"@jupyterlab/tooltip-extension:manager\",\"@jupyterlab/tooltip-extension:consoles\"]},\"/edit\":{\"@jupyterlab/fileeditor-extension\":[\"@jupyterlab/fileeditor-extension:completer\",\"@jupyterlab/fileeditor-extension:search\"],\"@jupyterlab/markdownviewer-extension\":true}}');\n\n\n  const pluginRegistry = new PluginRegistry();\n  const NotebookApp = require('@jupyter-notebook/application').NotebookApp;\n\n  pluginRegistry.registerPlugins(mods);\n  const IServiceManager = require('@jupyterlab/services').IServiceManager;\n  const serviceManager = await pluginRegistry.resolveRequiredService(IServiceManager);\n\n  const app = new NotebookApp({\n    pluginRegistry,\n    serviceManager,\n    mimeExtensions,\n    availablePlugins\n  });\n\n  // Expose global app instance when in dev mode or when toggled explicitly.\n  const exposeAppInBrowser =\n    (PageConfig.getOption('exposeAppInBrowser') || '').toLowerCase() === 'true';\n\n  if (exposeAppInBrowser) {\n    window.jupyterapp = app;\n  }\n\n  await app.start();\n}\n\nwindow.addEventListener('load', main);\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./sidepanel.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./sidepanel.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n\nimport '@jupyterlab/application/style/index.js';\nimport '@jupyterlab/mainmenu/style/index.js';\nimport '@jupyterlab/ui-components/style/index.js';\n\nimport './base.css';\nimport './sidepanel.css';\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "import '@jupyter-notebook/application/style/index.js';\nimport '@lumino/widgets/style/index.js';\n\nimport './base.css';\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "import './base.css';\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "import './base.css';\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "import './base.css';\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "import './base.css';\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "import './base.css';\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "import './base.css';\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "import '@jupyterlab/filebrowser/style/index.js';\n\nimport './base.css';\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "import '@jupyterlab/filebrowser/style/index.js';\n\nimport '@jupyter-notebook/tree/style/index.js';\n\nimport './base.css';\n", "\n      import API from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../node_modules/@jupyterlab/builder/node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\n\n      options.insert = insertFn.bind(null, \"head\");\n    \noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./base.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n\nimport './base.css';\n", "/* This is a generated file of CSS imports */\n/* It was generated by @jupyterlab/builder in Build.ensureAssets() */\n\nimport '@jupyter-notebook/application-extension/style/index.js';\nimport '@jupyter-notebook/application/style/index.js';\nimport '@jupyter-notebook/console-extension/style/index.js';\nimport '@jupyter-notebook/docmanager-extension/style/index.js';\nimport '@jupyter-notebook/documentsearch-extension/style/index.js';\nimport '@jupyter-notebook/help-extension/style/index.js';\nimport '@jupyter-notebook/notebook-extension/style/index.js';\nimport '@jupyter-notebook/terminal-extension/style/index.js';\nimport '@jupyter-notebook/tree-extension/style/index.js';\nimport '@jupyter-notebook/ui-components/style/index.js';\nimport '@jupyterlab/application-extension/style/index.js';\nimport '@jupyterlab/apputils-extension/style/index.js';\nimport '@jupyterlab/cell-toolbar-extension/style/index.js';\nimport '@jupyterlab/celltags-extension/style/index.js';\nimport '@jupyterlab/codemirror-extension/style/index.js';\nimport '@jupyterlab/completer-extension/style/index.js';\nimport '@jupyterlab/console-extension/style/index.js';\nimport '@jupyterlab/csvviewer-extension/style/index.js';\nimport '@jupyterlab/debugger-extension/style/index.js';\nimport '@jupyterlab/docmanager-extension/style/index.js';\nimport '@jupyterlab/documentsearch-extension/style/index.js';\nimport '@jupyterlab/extensionmanager-extension/style/index.js';\nimport '@jupyterlab/filebrowser-extension/style/index.js';\nimport '@jupyterlab/fileeditor-extension/style/index.js';\nimport '@jupyterlab/help-extension/style/index.js';\nimport '@jupyterlab/htmlviewer-extension/style/index.js';\nimport '@jupyterlab/hub-extension/style/index.js';\nimport '@jupyterlab/imageviewer-extension/style/index.js';\nimport '@jupyterlab/javascript-extension/style/index.js';\nimport '@jupyterlab/json-extension/style/index.js';\nimport '@jupyterlab/logconsole-extension/style/index.js';\nimport '@jupyterlab/lsp-extension/style/index.js';\nimport '@jupyterlab/mainmenu-extension/style/index.js';\nimport '@jupyterlab/markdownviewer-extension/style/index.js';\nimport '@jupyterlab/markedparser-extension/style/index.js';\nimport '@jupyterlab/mathjax-extension/style/index.js';\nimport '@jupyterlab/mermaid-extension/style/index.js';\nimport '@jupyterlab/metadataform-extension/style/index.js';\nimport '@jupyterlab/notebook-extension/style/index.js';\nimport '@jupyterlab/pdf-extension/style/index.js';\nimport '@jupyterlab/pluginmanager-extension/style/index.js';\nimport '@jupyterlab/running-extension/style/index.js';\nimport '@jupyterlab/settingeditor-extension/style/index.js';\nimport '@jupyterlab/shortcuts-extension/style/index.js';\nimport '@jupyterlab/terminal-extension/style/index.js';\nimport '@jupyterlab/toc-extension/style/index.js';\nimport '@jupyterlab/tooltip-extension/style/index.js';\nimport '@jupyterlab/translation-extension/style/index.js';\nimport '@jupyterlab/ui-components-extension/style/index.js';\nimport '@jupyterlab/vega5-extension/style/index.js';\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n|\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n\n.jp-NotebookSpacer {\n  flex-grow: 1;\n  flex-shrink: 1;\n}\n\n.jp-MainAreaWidget {\n  height: 100%;\n}\n\n.jp-Toolbar > .jp-Toolbar-item {\n  height: unset;\n}\n\n#jp-UserMenu {\n  flex: 0 0 auto;\n  display: flex;\n  text-align: center;\n  margin-top: 8px;\n}\n\n.jp-MimeDocument .jp-RenderedJSON {\n  background: var(--jp-layout-color0);\n}\n\n/* Hide the stub toolbar that appears above terminals and documents */\n\n.jp-MainAreaWidget > .jp-Toolbar-micro {\n  display: none;\n}\n\n#jp-NotebookLogo {\n  /* bring logo to the front so it is selectable by tab*/\n  z-index: 10;\n}\n\n/* Hide the notification status item */\n.jp-Notification-Status {\n  display: none;\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./../packages/application-extension/style/base.css\"],\"names\":[],\"mappings\":\"AAAA;;;;8EAI8E;;AAE9E;EACE,YAAY;EACZ,cAAc;AAChB;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,cAAc;EACd,aAAa;EACb,kBAAkB;EAClB,eAAe;AACjB;;AAEA;EACE,mCAAmC;AACrC;;AAEA,qEAAqE;;AAErE;EACE,aAAa;AACf;;AAEA;EACE,sDAAsD;EACtD,WAAW;AACb;;AAEA,sCAAsC;AACtC;EACE,aAAa;AACf\",\"sourcesContent\":[\"/*-----------------------------------------------------------------------------\\n| Copyright (c) Jupyter Development Team.\\n|\\n| Distributed under the terms of the Modified BSD License.\\n|----------------------------------------------------------------------------*/\\n\\n.jp-NotebookSpacer {\\n  flex-grow: 1;\\n  flex-shrink: 1;\\n}\\n\\n.jp-MainAreaWidget {\\n  height: 100%;\\n}\\n\\n.jp-Toolbar > .jp-Toolbar-item {\\n  height: unset;\\n}\\n\\n#jp-UserMenu {\\n  flex: 0 0 auto;\\n  display: flex;\\n  text-align: center;\\n  margin-top: 8px;\\n}\\n\\n.jp-MimeDocument .jp-RenderedJSON {\\n  background: var(--jp-layout-color0);\\n}\\n\\n/* Hide the stub toolbar that appears above terminals and documents */\\n\\n.jp-MainAreaWidget > .jp-Toolbar-micro {\\n  display: none;\\n}\\n\\n#jp-NotebookLogo {\\n  /* bring logo to the front so it is selectable by tab*/\\n  z-index: 10;\\n}\\n\\n/* Hide the notification status item */\\n.jp-Notification-Status {\\n  display: none;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n\n:root {\n  --jp-private-topbar-height: 28px;\n  /* Override the layout-2 color for the dark theme */\n  --md-grey-800: #323232;\n  --jp-notebook-max-width: 1200px;\n}\n\n/*\n  Override the default background\n  See https://github.com/jupyterlab/jupyterlab/pull/16519 for more information\n*/\nbody.jp-ThemedContainer {\n  margin: 0;\n  padding: 0;\n  background: var(--jp-layout-color2);\n}\n\n#main.jp-ThemedContainer {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: var(--jp-layout-color2);\n}\n\n#top-panel-wrapper {\n  min-height: calc(1.5 * var(--jp-private-topbar-height));\n  border-bottom: var(--jp-border-width) solid var(--jp-border-color0);\n  background: var(--jp-layout-color1);\n}\n\n#top-panel {\n  display: flex;\n  min-height: calc(1.5 * var(--jp-private-topbar-height));\n  padding-left: 5px;\n  padding-right: 5px;\n  margin-left: auto;\n  margin-right: auto;\n  max-width: 1200px;\n}\n\n#menu-panel-wrapper {\n  min-height: var(--jp-private-topbar-height);\n  background: var(--jp-layout-color1);\n  border-bottom: var(--jp-border-width) solid var(--jp-border-color0);\n  box-shadow: var(--jp-elevation-z1);\n}\n\n#menu-panel {\n  display: flex;\n  min-height: var(--jp-private-topbar-height);\n  background: var(--jp-layout-color1);\n  padding-left: 5px;\n  padding-right: 5px;\n  margin-left: auto;\n  margin-right: auto;\n  max-width: var(--jp-notebook-max-width);\n}\n\n#main-panel {\n  margin-left: auto;\n  margin-right: auto;\n  max-width: var(--jp-notebook-max-width);\n}\n\n#spacer-widget-top {\n  min-height: 16px;\n}\n\n/* Only edit pages should have a bottom space */\n\nbody[data-notebook='edit'] #spacer-widget-bottom {\n  min-height: 16px;\n}\n\n/* Special case notebooks as document oriented pages */\n\n[data-notebook]:not(body[data-notebook='notebooks']) #main-panel {\n  box-shadow: var(--jp-elevation-z4);\n}\n\n.jp-TreePanel > .lm-TabPanel-stackedPanel {\n  box-shadow: var(--jp-elevation-z4);\n}\n\nbody[data-notebook='notebooks'] #main-panel {\n  margin-left: unset;\n  margin-right: unset;\n  max-width: unset;\n}\n\nbody[data-notebook='notebooks'] #spacer-widget-top {\n  min-height: unset;\n}\n\n#main-panel > .jp-TreePanel {\n  padding: 0px 5px;\n}\n\n@media only screen and (max-width: 760px) {\n  #main-panel > .jp-TreePanel {\n    margin: 0px -5px;\n  }\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./../packages/application/style/base.css\"],\"names\":[],\"mappings\":\"AAAA;;;8EAG8E;;AAE9E;EACE,gCAAgC;EAChC,mDAAmD;EACnD,sBAAsB;EACtB,+BAA+B;AACjC;;AAEA;;;CAGC;AACD;EACE,SAAS;EACT,UAAU;EACV,mCAAmC;AACrC;;AAEA;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,mCAAmC;AACrC;;AAEA;EACE,uDAAuD;EACvD,mEAAmE;EACnE,mCAAmC;AACrC;;AAEA;EACE,aAAa;EACb,uDAAuD;EACvD,iBAAiB;EACjB,kBAAkB;EAClB,iBAAiB;EACjB,kBAAkB;EAClB,iBAAiB;AACnB;;AAEA;EACE,2CAA2C;EAC3C,mCAAmC;EACnC,mEAAmE;EACnE,kCAAkC;AACpC;;AAEA;EACE,aAAa;EACb,2CAA2C;EAC3C,mCAAmC;EACnC,iBAAiB;EACjB,kBAAkB;EAClB,iBAAiB;EACjB,kBAAkB;EAClB,uCAAuC;AACzC;;AAEA;EACE,iBAAiB;EACjB,kBAAkB;EAClB,uCAAuC;AACzC;;AAEA;EACE,gBAAgB;AAClB;;AAEA,+CAA+C;;AAE/C;EACE,gBAAgB;AAClB;;AAEA,sDAAsD;;AAEtD;EACE,kCAAkC;AACpC;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE,kBAAkB;EAClB,mBAAmB;EACnB,gBAAgB;AAClB;;AAEA;EACE,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE;IACE,gBAAgB;EAClB;AACF\",\"sourcesContent\":[\"/*-----------------------------------------------------------------------------\\n| Copyright (c) Jupyter Development Team.\\n| Distributed under the terms of the Modified BSD License.\\n|----------------------------------------------------------------------------*/\\n\\n:root {\\n  --jp-private-topbar-height: 28px;\\n  /* Override the layout-2 color for the dark theme */\\n  --md-grey-800: #323232;\\n  --jp-notebook-max-width: 1200px;\\n}\\n\\n/*\\n  Override the default background\\n  See https://github.com/jupyterlab/jupyterlab/pull/16519 for more information\\n*/\\nbody.jp-ThemedContainer {\\n  margin: 0;\\n  padding: 0;\\n  background: var(--jp-layout-color2);\\n}\\n\\n#main.jp-ThemedContainer {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: var(--jp-layout-color2);\\n}\\n\\n#top-panel-wrapper {\\n  min-height: calc(1.5 * var(--jp-private-topbar-height));\\n  border-bottom: var(--jp-border-width) solid var(--jp-border-color0);\\n  background: var(--jp-layout-color1);\\n}\\n\\n#top-panel {\\n  display: flex;\\n  min-height: calc(1.5 * var(--jp-private-topbar-height));\\n  padding-left: 5px;\\n  padding-right: 5px;\\n  margin-left: auto;\\n  margin-right: auto;\\n  max-width: 1200px;\\n}\\n\\n#menu-panel-wrapper {\\n  min-height: var(--jp-private-topbar-height);\\n  background: var(--jp-layout-color1);\\n  border-bottom: var(--jp-border-width) solid var(--jp-border-color0);\\n  box-shadow: var(--jp-elevation-z1);\\n}\\n\\n#menu-panel {\\n  display: flex;\\n  min-height: var(--jp-private-topbar-height);\\n  background: var(--jp-layout-color1);\\n  padding-left: 5px;\\n  padding-right: 5px;\\n  margin-left: auto;\\n  margin-right: auto;\\n  max-width: var(--jp-notebook-max-width);\\n}\\n\\n#main-panel {\\n  margin-left: auto;\\n  margin-right: auto;\\n  max-width: var(--jp-notebook-max-width);\\n}\\n\\n#spacer-widget-top {\\n  min-height: 16px;\\n}\\n\\n/* Only edit pages should have a bottom space */\\n\\nbody[data-notebook='edit'] #spacer-widget-bottom {\\n  min-height: 16px;\\n}\\n\\n/* Special case notebooks as document oriented pages */\\n\\n[data-notebook]:not(body[data-notebook='notebooks']) #main-panel {\\n  box-shadow: var(--jp-elevation-z4);\\n}\\n\\n.jp-TreePanel > .lm-TabPanel-stackedPanel {\\n  box-shadow: var(--jp-elevation-z4);\\n}\\n\\nbody[data-notebook='notebooks'] #main-panel {\\n  margin-left: unset;\\n  margin-right: unset;\\n  max-width: unset;\\n}\\n\\nbody[data-notebook='notebooks'] #spacer-widget-top {\\n  min-height: unset;\\n}\\n\\n#main-panel > .jp-TreePanel {\\n  padding: 0px 5px;\\n}\\n\\n@media only screen and (max-width: 760px) {\\n  #main-panel > .jp-TreePanel {\\n    margin: 0px -5px;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n| Distributed under the terms of the Modified BSD License.\n|\n| Adapted from JupyterLab's packages/application/style/sidepanel.css.\n|----------------------------------------------------------------------------*/\n\n/*-----------------------------------------------------------------------------\n| Variables\n|----------------------------------------------------------------------------*/\n\n:root {\n  --jp-private-sidebar-tab-width: 32px;\n}\n\n/*-----------------------------------------------------------------------------\n| SideBar\n|----------------------------------------------------------------------------*/\n\n/* Stack panels */\n\n#jp-right-stack,\n#jp-left-stack {\n  display: flex;\n  flex-direction: column;\n  min-width: var(--jp-sidebar-min-width);\n}\n\n#jp-left-stack .jp-SidePanel-collapse,\n#jp-right-stack .jp-SidePanel-collapse {\n  display: flex;\n  flex: 0 0 auto;\n  min-height: 0;\n  padding: 0;\n}\n\n#jp-left-stack .jp-SidePanel-collapse {\n  justify-content: right;\n}\n\n#jp-right-stack .jp-SidePanel-collapse {\n  justify-content: left;\n}\n\n#jp-left-stack .lm-StackedPanel,\n#jp-right-stack .lm-StackedPanel {\n  flex: 1 1 auto;\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./../packages/application/style/sidepanel.css\"],\"names\":[],\"mappings\":\"AAAA;;;;;8EAK8E;;AAE9E;;8EAE8E;;AAE9E;EACE,oCAAoC;AACtC;;AAEA;;8EAE8E;;AAE9E,iBAAiB;;AAEjB;;EAEE,aAAa;EACb,sBAAsB;EACtB,sCAAsC;AACxC;;AAEA;;EAEE,aAAa;EACb,cAAc;EACd,aAAa;EACb,UAAU;AACZ;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,qBAAqB;AACvB;;AAEA;;EAEE,cAAc;AAChB\",\"sourcesContent\":[\"/*-----------------------------------------------------------------------------\\n| Copyright (c) Jupyter Development Team.\\n| Distributed under the terms of the Modified BSD License.\\n|\\n| Adapted from JupyterLab's packages/application/style/sidepanel.css.\\n|----------------------------------------------------------------------------*/\\n\\n/*-----------------------------------------------------------------------------\\n| Variables\\n|----------------------------------------------------------------------------*/\\n\\n:root {\\n  --jp-private-sidebar-tab-width: 32px;\\n}\\n\\n/*-----------------------------------------------------------------------------\\n| SideBar\\n|----------------------------------------------------------------------------*/\\n\\n/* Stack panels */\\n\\n#jp-right-stack,\\n#jp-left-stack {\\n  display: flex;\\n  flex-direction: column;\\n  min-width: var(--jp-sidebar-min-width);\\n}\\n\\n#jp-left-stack .jp-SidePanel-collapse,\\n#jp-right-stack .jp-SidePanel-collapse {\\n  display: flex;\\n  flex: 0 0 auto;\\n  min-height: 0;\\n  padding: 0;\\n}\\n\\n#jp-left-stack .jp-SidePanel-collapse {\\n  justify-content: right;\\n}\\n\\n#jp-right-stack .jp-SidePanel-collapse {\\n  justify-content: left;\\n}\\n\\n#jp-left-stack .lm-StackedPanel,\\n#jp-right-stack .lm-StackedPanel {\\n  flex: 1 1 auto;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, ``, \"\",{\"version\":3,\"sources\":[],\"names\":[],\"mappings\":\"\",\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.jp-Document {\n  height: 100%;\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./../packages/docmanager-extension/style/base.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,YAAY;AACd\",\"sourcesContent\":[\".jp-Document {\\n  height: 100%;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, ``, \"\",{\"version\":3,\"sources\":[],\"names\":[],\"mappings\":\"\",\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.jp-AboutNotebook .jp-Dialog-header {\n  justify-content: center;\n  padding: 0;\n}\n\n.jp-AboutNotebook-header {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  padding: var(--jp-flat-button-padding);\n}\n\n.jp-AboutNotebook-header-text {\n  margin-left: 16px;\n}\n\n.jp-AboutNotebook-version {\n  color: var(--jp-ui-font-color1);\n  font-size: var(--jp-ui-font-size1);\n  padding-bottom: 30px;\n  font-weight: 400;\n  letter-spacing: 0.4px;\n  line-height: 1.12;\n  min-width: 360px;\n  text-align: center;\n}\n\n.jp-AboutNotebook-body {\n  display: flex;\n  font-size: var(--jp-ui-font-size2);\n  padding: var(--jp-flat-button-padding);\n  color: var(--jp-ui-font-color1);\n  text-align: center;\n  flex-direction: column;\n  min-width: 360px;\n  overflow: hidden;\n}\n\n.jp-AboutNotebook-about-body pre {\n  white-space: pre-wrap;\n}\n\n.jp-AboutNotebook-about-externalLinks {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: flex-start;\n  color: var(--jp-warn-color0);\n}\n\n.jp-AboutNotebook-about-copyright {\n  padding-top: 25px;\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./../packages/help-extension/style/base.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,uBAAuB;EACvB,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,sCAAsC;AACxC;;AAEA;EACE,iBAAiB;AACnB;;AAEA;EACE,+BAA+B;EAC/B,kCAAkC;EAClC,oBAAoB;EACpB,gBAAgB;EAChB,qBAAqB;EACrB,iBAAiB;EACjB,gBAAgB;EAChB,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,kCAAkC;EAClC,sCAAsC;EACtC,+BAA+B;EAC/B,kBAAkB;EAClB,sBAAsB;EACtB,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,qBAAqB;AACvB;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,2BAA2B;EAC3B,uBAAuB;EACvB,4BAA4B;AAC9B;;AAEA;EACE,iBAAiB;AACnB\",\"sourcesContent\":[\".jp-AboutNotebook .jp-Dialog-header {\\n  justify-content: center;\\n  padding: 0;\\n}\\n\\n.jp-AboutNotebook-header {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  padding: var(--jp-flat-button-padding);\\n}\\n\\n.jp-AboutNotebook-header-text {\\n  margin-left: 16px;\\n}\\n\\n.jp-AboutNotebook-version {\\n  color: var(--jp-ui-font-color1);\\n  font-size: var(--jp-ui-font-size1);\\n  padding-bottom: 30px;\\n  font-weight: 400;\\n  letter-spacing: 0.4px;\\n  line-height: 1.12;\\n  min-width: 360px;\\n  text-align: center;\\n}\\n\\n.jp-AboutNotebook-body {\\n  display: flex;\\n  font-size: var(--jp-ui-font-size2);\\n  padding: var(--jp-flat-button-padding);\\n  color: var(--jp-ui-font-color1);\\n  text-align: center;\\n  flex-direction: column;\\n  min-width: 360px;\\n  overflow: hidden;\\n}\\n\\n.jp-AboutNotebook-about-body pre {\\n  white-space: pre-wrap;\\n}\\n\\n.jp-AboutNotebook-about-externalLinks {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  align-items: flex-start;\\n  color: var(--jp-warn-color0);\\n}\\n\\n.jp-AboutNotebook-about-copyright {\\n  padding-top: 25px;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nimport ___CSS_LOADER_AT_RULE_IMPORT_0___ from \"-!../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/cjs.js!./variables.css\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n___CSS_LOADER_EXPORT___.i(___CSS_LOADER_AT_RULE_IMPORT_0___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n|\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n\n/**\n  Document oriented look for the notebook.\n  This includes changes to the look and feel of the JupyterLab Notebook\n  component like:\n  - scrollbar to the right of the page\n  - drop shadow on the notebook\n  - smaller empty space at the bottom of the notebook\n  - compact view on mobile\n*/\n\n/* Make the notebook take up the full width of the page when jp-mod-fullwidth is set */\n\nbody[data-notebook='notebooks']\n  .jp-NotebookPanel.jp-mod-fullwidth\n  .jp-WindowedPanel-outer {\n  padding-left: unset;\n  padding-right: unset !important;\n  width: unset;\n}\n\n/* Keep the notebook centered on the page */\n\nbody[data-notebook='notebooks'] .jp-NotebookPanel-toolbar {\n  padding-left: calc(calc(100% - var(--jp-notebook-max-width)) * 0.5);\n  padding-right: calc(calc(100% - var(--jp-notebook-max-width)) * 0.5);\n}\n\nbody[data-notebook='notebooks'] .jp-WindowedPanel-outer {\n  width: unset !important;\n  padding-top: unset;\n  padding-left: calc(calc(100% - var(--jp-notebook-max-width)) * 0.5);\n  padding-right: calc(\n    calc(\n        100% - var(--jp-notebook-max-width) - var(--jp-notebook-padding-offset)\n      ) * 0.5\n  ) !important;\n  background: var(--jp-layout-color2);\n}\n\nbody[data-notebook='notebooks'] .jp-WindowedPanel-inner {\n  margin-top: var(--jp-notebook-toolbar-margin-bottom);\n  /* Adjustments for the extra top and bottom notebook padding */\n  margin-bottom: calc(4 * var(--jp-notebook-padding));\n}\n\nbody[data-notebook='notebooks'] .jp-Notebook-cell {\n  background: var(--jp-layout-color0);\n}\n\n/* Empty space at the bottom of the notebook (similar to classic) */\nbody[data-notebook='notebooks']\n  .jp-Notebook.jp-mod-scrollPastEnd\n  .jp-WindowedPanel-outer::after {\n  min-height: 100px;\n}\n\n/* Fix background colors */\n\nbody[data-notebook='notebooks'] .jp-WindowedPanel-outer > * {\n  background: var(--jp-layout-color0);\n}\n\nbody[data-notebook='notebooks']\n  .jp-Notebook.jp-mod-commandMode\n  .jp-Cell.jp-mod-active.jp-mod-selected:not(.jp-mod-multiSelected) {\n  background: var(--jp-layout-color0) !important;\n}\n\nbody[data-notebook='notebooks']\n  .jp-Notebook\n  .jp-Notebook-cell:not(:first-child)::before {\n  content: ' ';\n  height: 100%;\n  position: absolute;\n  top: 0;\n  width: 11px;\n}\n\n/* Cell toolbar adjustments */\n\nbody[data-notebook='notebooks'] .jp-cell-toolbar {\n  background: unset;\n  box-shadow: unset;\n}\n\n/** first code cell on mobile\n    (keep the selector above the media query)\n*/\nbody[data-notebook='notebooks']\n  .jp-CodeCell[data-windowed-list-index='0']\n  .jp-cell-toolbar {\n  top: unset;\n}\n\n@media only screen and (max-width: 760px) {\n  /* first code cell on mobile */\n  body[data-notebook='notebooks']\n    .jp-CodeCell[data-windowed-list-index='0']\n    .jp-cell-toolbar {\n    top: var(--jp-notebook-padding);\n  }\n\n  body[data-notebook='notebooks'] .jp-MarkdownCell .jp-cell-toolbar,\n  body[data-notebook='notebooks'] .jp-RawCell .jp-cell-toolbar {\n    top: calc(0.5 * var(--jp-notebook-padding));\n  }\n}\n\n/* Tweak the notebook footer (to add a new cell) */\nbody[data-notebook='notebooks'] .jp-Notebook-footer {\n  background: unset;\n  width: 100%;\n  margin-left: unset;\n}\n\n/* Mobile View */\n\nbody[data-format='mobile'] .jp-NotebookCheckpoint {\n  display: none;\n}\n\nbody[data-format='mobile'] .jp-WindowedPanel-outer > *:first-child {\n  margin-top: 0;\n}\n\nbody[data-format='mobile'] .jp-ToolbarButton .jp-DebuggerBugButton {\n  display: none;\n}\n\nbody[data-notebook='notebooks'] .jp-WindowedPanel-viewport {\n  background: var(--jp-layout-color0);\n  box-shadow: var(--jp-elevation-z4);\n\n  /* Extra padding at the top and bottom so the notebook looks nicer */\n  padding-top: calc(2 * var(--jp-notebook-padding));\n  padding-bottom: calc(2 * var(--jp-notebook-padding));\n}\n\n/* Notebook box shadow */\n\nbody[data-notebook='notebooks']\n  .jp-Notebook\n  > *:first-child:last-child::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  box-shadow: 0px 0px 12px 1px var(--jp-shadow-umbra-color);\n}\n\n/* Additional customizations of the components on the notebook page */\n\n.jp-NotebookKernelLogo {\n  flex: 0 0 auto;\n  display: flex;\n  align-items: center;\n  text-align: center;\n  margin-right: 8px;\n}\n\n.jp-NotebookKernelLogo img {\n  max-width: 28px;\n  max-height: 28px;\n  display: flex;\n}\n\n.jp-NotebookKernelStatus {\n  margin: 0;\n  font-weight: normal;\n  font-size: var(--jp-ui-font-size1);\n  color: var(--jp-ui-font-color0);\n  font-family: var(--jp-ui-font-family);\n  line-height: var(--jp-private-title-panel-height);\n  padding-left: var(--jp-kernel-status-padding);\n  padding-right: var(--jp-kernel-status-padding);\n}\n\n.jp-NotebookKernelStatus-error {\n  background-color: var(--jp-error-color0);\n}\n\n.jp-NotebookKernelStatus-warn {\n  background-color: var(--jp-warn-color0);\n}\n\n.jp-NotebookKernelStatus-info {\n  background-color: var(--jp-info-color0);\n}\n\n.jp-NotebookKernelStatus-fade {\n  animation: 0.5s fade-out forwards;\n}\n\n.jp-NotebookTrustedStatus {\n  background: var(--jp-layout-color1);\n  color: var(--jp-ui-font-color1);\n  margin-top: 4px;\n  margin-bottom: 4px;\n  border: solid 1px var(--jp-border-color2);\n  cursor: help;\n}\n\n.jp-NotebookTrustedStatus-not-trusted {\n  cursor: pointer;\n}\n\n@keyframes fade-out {\n  0% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n\n#jp-title h1 {\n  cursor: pointer;\n  font-size: 18px;\n  margin: 0;\n  font-weight: normal;\n  color: var(--jp-ui-font-color0);\n  font-family: var(--jp-ui-font-family);\n  line-height: calc(1.5 * var(--jp-private-title-panel-height));\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n#jp-title h1:hover {\n  background: var(--jp-layout-color2);\n}\n\n.jp-NotebookCheckpoint {\n  font-size: 14px;\n  margin-left: 5px;\n  margin-right: 5px;\n  font-weight: normal;\n  color: var(--jp-ui-font-color0);\n  font-family: var(--jp-ui-font-family);\n  line-height: calc(1.5 * var(--jp-private-title-panel-height));\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.jp-skiplink {\n  position: absolute;\n  top: -100em;\n}\n\n.jp-skiplink:focus-within {\n  position: absolute;\n  z-index: 10000;\n  top: 0;\n  left: 46%;\n  margin: 0 auto;\n  padding: 1em;\n  width: 15%;\n  box-shadow: var(--jp-elevation-z4);\n  border-radius: 4px;\n  background: var(--jp-layout-color0);\n  text-align: center;\n}\n\n.jp-skiplink:focus-within a {\n  text-decoration: underline;\n  color: var(--jp-content-link-color);\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./../packages/notebook-extension/style/base.css\"],\"names\":[],\"mappings\":\"AAAA;;;;8EAI8E;;AAI9E;;;;;;;;CAQC;;AAED,sFAAsF;;AAEtF;;;EAGE,mBAAmB;EACnB,+BAA+B;EAC/B,YAAY;AACd;;AAEA,2CAA2C;;AAE3C;EACE,mEAAmE;EACnE,oEAAoE;AACtE;;AAEA;EACE,uBAAuB;EACvB,kBAAkB;EAClB,mEAAmE;EACnE;;;;cAIY;EACZ,mCAAmC;AACrC;;AAEA;EACE,oDAAoD;EACpD,8DAA8D;EAC9D,mDAAmD;AACrD;;AAEA;EACE,mCAAmC;AACrC;;AAEA,mEAAmE;AACnE;;;EAGE,iBAAiB;AACnB;;AAEA,0BAA0B;;AAE1B;EACE,mCAAmC;AACrC;;AAEA;;;EAGE,8CAA8C;AAChD;;AAEA;;;EAGE,YAAY;EACZ,YAAY;EACZ,kBAAkB;EAClB,MAAM;EACN,WAAW;AACb;;AAEA,6BAA6B;;AAE7B;EACE,iBAAiB;EACjB,iBAAiB;AACnB;;AAEA;;CAEC;AACD;;;EAGE,UAAU;AACZ;;AAEA;EACE,8BAA8B;EAC9B;;;IAGE,+BAA+B;EACjC;;EAEA;;IAEE,2CAA2C;EAC7C;AACF;;AAEA,kDAAkD;AAClD;EACE,iBAAiB;EACjB,WAAW;EACX,kBAAkB;AACpB;;AAEA,gBAAgB;;AAEhB;EACE,aAAa;AACf;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,mCAAmC;EACnC,kCAAkC;;EAElC,oEAAoE;EACpE,iDAAiD;EACjD,oDAAoD;AACtD;;AAEA,wBAAwB;;AAExB;;;EAGE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,SAAS;EACT,OAAO;EACP,QAAQ;EACR,yDAAyD;AAC3D;;AAEA,qEAAqE;;AAErE;EACE,cAAc;EACd,aAAa;EACb,mBAAmB;EACnB,kBAAkB;EAClB,iBAAiB;AACnB;;AAEA;EACE,eAAe;EACf,gBAAgB;EAChB,aAAa;AACf;;AAEA;EACE,SAAS;EACT,mBAAmB;EACnB,kCAAkC;EAClC,+BAA+B;EAC/B,qCAAqC;EACrC,iDAAiD;EACjD,6CAA6C;EAC7C,8CAA8C;AAChD;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,uCAAuC;AACzC;;AAEA;EACE,uCAAuC;AACzC;;AAEA;EACE,iCAAiC;AACnC;;AAEA;EACE,mCAAmC;EACnC,+BAA+B;EAC/B,eAAe;EACf,kBAAkB;EAClB,yCAAyC;EACzC,YAAY;AACd;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE,eAAe;EACf,eAAe;EACf,SAAS;EACT,mBAAmB;EACnB,+BAA+B;EAC/B,qCAAqC;EACrC,6DAA6D;EAC7D,uBAAuB;EACvB,gBAAgB;EAChB,mBAAmB;AACrB;;AAEA;EACE,mCAAmC;AACrC;;AAEA;EACE,eAAe;EACf,gBAAgB;EAChB,iBAAiB;EACjB,mBAAmB;EACnB,+BAA+B;EAC/B,qCAAqC;EACrC,6DAA6D;EAC7D,uBAAuB;EACvB,gBAAgB;EAChB,mBAAmB;AACrB;;AAEA;EACE,kBAAkB;EAClB,WAAW;AACb;;AAEA;EACE,kBAAkB;EAClB,cAAc;EACd,MAAM;EACN,SAAS;EACT,cAAc;EACd,YAAY;EACZ,UAAU;EACV,kCAAkC;EAClC,kBAAkB;EAClB,mCAAmC;EACnC,kBAAkB;AACpB;;AAEA;EACE,0BAA0B;EAC1B,mCAAmC;AACrC\",\"sourcesContent\":[\"/*-----------------------------------------------------------------------------\\n| Copyright (c) Jupyter Development Team.\\n|\\n| Distributed under the terms of the Modified BSD License.\\n|----------------------------------------------------------------------------*/\\n\\n@import './variables.css';\\n\\n/**\\n  Document oriented look for the notebook.\\n  This includes changes to the look and feel of the JupyterLab Notebook\\n  component like:\\n  - scrollbar to the right of the page\\n  - drop shadow on the notebook\\n  - smaller empty space at the bottom of the notebook\\n  - compact view on mobile\\n*/\\n\\n/* Make the notebook take up the full width of the page when jp-mod-fullwidth is set */\\n\\nbody[data-notebook='notebooks']\\n  .jp-NotebookPanel.jp-mod-fullwidth\\n  .jp-WindowedPanel-outer {\\n  padding-left: unset;\\n  padding-right: unset !important;\\n  width: unset;\\n}\\n\\n/* Keep the notebook centered on the page */\\n\\nbody[data-notebook='notebooks'] .jp-NotebookPanel-toolbar {\\n  padding-left: calc(calc(100% - var(--jp-notebook-max-width)) * 0.5);\\n  padding-right: calc(calc(100% - var(--jp-notebook-max-width)) * 0.5);\\n}\\n\\nbody[data-notebook='notebooks'] .jp-WindowedPanel-outer {\\n  width: unset !important;\\n  padding-top: unset;\\n  padding-left: calc(calc(100% - var(--jp-notebook-max-width)) * 0.5);\\n  padding-right: calc(\\n    calc(\\n        100% - var(--jp-notebook-max-width) - var(--jp-notebook-padding-offset)\\n      ) * 0.5\\n  ) !important;\\n  background: var(--jp-layout-color2);\\n}\\n\\nbody[data-notebook='notebooks'] .jp-WindowedPanel-inner {\\n  margin-top: var(--jp-notebook-toolbar-margin-bottom);\\n  /* Adjustments for the extra top and bottom notebook padding */\\n  margin-bottom: calc(4 * var(--jp-notebook-padding));\\n}\\n\\nbody[data-notebook='notebooks'] .jp-Notebook-cell {\\n  background: var(--jp-layout-color0);\\n}\\n\\n/* Empty space at the bottom of the notebook (similar to classic) */\\nbody[data-notebook='notebooks']\\n  .jp-Notebook.jp-mod-scrollPastEnd\\n  .jp-WindowedPanel-outer::after {\\n  min-height: 100px;\\n}\\n\\n/* Fix background colors */\\n\\nbody[data-notebook='notebooks'] .jp-WindowedPanel-outer > * {\\n  background: var(--jp-layout-color0);\\n}\\n\\nbody[data-notebook='notebooks']\\n  .jp-Notebook.jp-mod-commandMode\\n  .jp-Cell.jp-mod-active.jp-mod-selected:not(.jp-mod-multiSelected) {\\n  background: var(--jp-layout-color0) !important;\\n}\\n\\nbody[data-notebook='notebooks']\\n  .jp-Notebook\\n  .jp-Notebook-cell:not(:first-child)::before {\\n  content: ' ';\\n  height: 100%;\\n  position: absolute;\\n  top: 0;\\n  width: 11px;\\n}\\n\\n/* Cell toolbar adjustments */\\n\\nbody[data-notebook='notebooks'] .jp-cell-toolbar {\\n  background: unset;\\n  box-shadow: unset;\\n}\\n\\n/** first code cell on mobile\\n    (keep the selector above the media query)\\n*/\\nbody[data-notebook='notebooks']\\n  .jp-CodeCell[data-windowed-list-index='0']\\n  .jp-cell-toolbar {\\n  top: unset;\\n}\\n\\n@media only screen and (max-width: 760px) {\\n  /* first code cell on mobile */\\n  body[data-notebook='notebooks']\\n    .jp-CodeCell[data-windowed-list-index='0']\\n    .jp-cell-toolbar {\\n    top: var(--jp-notebook-padding);\\n  }\\n\\n  body[data-notebook='notebooks'] .jp-MarkdownCell .jp-cell-toolbar,\\n  body[data-notebook='notebooks'] .jp-RawCell .jp-cell-toolbar {\\n    top: calc(0.5 * var(--jp-notebook-padding));\\n  }\\n}\\n\\n/* Tweak the notebook footer (to add a new cell) */\\nbody[data-notebook='notebooks'] .jp-Notebook-footer {\\n  background: unset;\\n  width: 100%;\\n  margin-left: unset;\\n}\\n\\n/* Mobile View */\\n\\nbody[data-format='mobile'] .jp-NotebookCheckpoint {\\n  display: none;\\n}\\n\\nbody[data-format='mobile'] .jp-WindowedPanel-outer > *:first-child {\\n  margin-top: 0;\\n}\\n\\nbody[data-format='mobile'] .jp-ToolbarButton .jp-DebuggerBugButton {\\n  display: none;\\n}\\n\\nbody[data-notebook='notebooks'] .jp-WindowedPanel-viewport {\\n  background: var(--jp-layout-color0);\\n  box-shadow: var(--jp-elevation-z4);\\n\\n  /* Extra padding at the top and bottom so the notebook looks nicer */\\n  padding-top: calc(2 * var(--jp-notebook-padding));\\n  padding-bottom: calc(2 * var(--jp-notebook-padding));\\n}\\n\\n/* Notebook box shadow */\\n\\nbody[data-notebook='notebooks']\\n  .jp-Notebook\\n  > *:first-child:last-child::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  box-shadow: 0px 0px 12px 1px var(--jp-shadow-umbra-color);\\n}\\n\\n/* Additional customizations of the components on the notebook page */\\n\\n.jp-NotebookKernelLogo {\\n  flex: 0 0 auto;\\n  display: flex;\\n  align-items: center;\\n  text-align: center;\\n  margin-right: 8px;\\n}\\n\\n.jp-NotebookKernelLogo img {\\n  max-width: 28px;\\n  max-height: 28px;\\n  display: flex;\\n}\\n\\n.jp-NotebookKernelStatus {\\n  margin: 0;\\n  font-weight: normal;\\n  font-size: var(--jp-ui-font-size1);\\n  color: var(--jp-ui-font-color0);\\n  font-family: var(--jp-ui-font-family);\\n  line-height: var(--jp-private-title-panel-height);\\n  padding-left: var(--jp-kernel-status-padding);\\n  padding-right: var(--jp-kernel-status-padding);\\n}\\n\\n.jp-NotebookKernelStatus-error {\\n  background-color: var(--jp-error-color0);\\n}\\n\\n.jp-NotebookKernelStatus-warn {\\n  background-color: var(--jp-warn-color0);\\n}\\n\\n.jp-NotebookKernelStatus-info {\\n  background-color: var(--jp-info-color0);\\n}\\n\\n.jp-NotebookKernelStatus-fade {\\n  animation: 0.5s fade-out forwards;\\n}\\n\\n.jp-NotebookTrustedStatus {\\n  background: var(--jp-layout-color1);\\n  color: var(--jp-ui-font-color1);\\n  margin-top: 4px;\\n  margin-bottom: 4px;\\n  border: solid 1px var(--jp-border-color2);\\n  cursor: help;\\n}\\n\\n.jp-NotebookTrustedStatus-not-trusted {\\n  cursor: pointer;\\n}\\n\\n@keyframes fade-out {\\n  0% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0;\\n  }\\n}\\n\\n#jp-title h1 {\\n  cursor: pointer;\\n  font-size: 18px;\\n  margin: 0;\\n  font-weight: normal;\\n  color: var(--jp-ui-font-color0);\\n  font-family: var(--jp-ui-font-family);\\n  line-height: calc(1.5 * var(--jp-private-title-panel-height));\\n  text-overflow: ellipsis;\\n  overflow: hidden;\\n  white-space: nowrap;\\n}\\n\\n#jp-title h1:hover {\\n  background: var(--jp-layout-color2);\\n}\\n\\n.jp-NotebookCheckpoint {\\n  font-size: 14px;\\n  margin-left: 5px;\\n  margin-right: 5px;\\n  font-weight: normal;\\n  color: var(--jp-ui-font-color0);\\n  font-family: var(--jp-ui-font-family);\\n  line-height: calc(1.5 * var(--jp-private-title-panel-height));\\n  text-overflow: ellipsis;\\n  overflow: hidden;\\n  white-space: nowrap;\\n}\\n\\n.jp-skiplink {\\n  position: absolute;\\n  top: -100em;\\n}\\n\\n.jp-skiplink:focus-within {\\n  position: absolute;\\n  z-index: 10000;\\n  top: 0;\\n  left: 46%;\\n  margin: 0 auto;\\n  padding: 1em;\\n  width: 15%;\\n  box-shadow: var(--jp-elevation-z4);\\n  border-radius: 4px;\\n  background: var(--jp-layout-color0);\\n  text-align: center;\\n}\\n\\n.jp-skiplink:focus-within a {\\n  text-decoration: underline;\\n  color: var(--jp-content-link-color);\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `:root {\n  --jp-notebook-toolbar-margin-bottom: 20px;\n  --jp-notebook-padding-offset: 20px;\n\n  --jp-kernel-status-padding: 5px;\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./../packages/notebook-extension/style/variables.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,yCAAyC;EACzC,kCAAkC;;EAElC,+BAA+B;AACjC\",\"sourcesContent\":[\":root {\\n  --jp-notebook-toolbar-margin-bottom: 20px;\\n  --jp-notebook-padding-offset: 20px;\\n\\n  --jp-kernel-status-padding: 5px;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, ``, \"\",{\"version\":3,\"sources\":[],\"names\":[],\"mappings\":\"\",\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `/*-----------------------------------------------------------------------------\n| Copyright (c) Jupyter Development Team.\n|\n| Distributed under the terms of the Modified BSD License.\n|----------------------------------------------------------------------------*/\n\n.jp-FileBrowser-toolbar .jp-Toolbar-item.jp-DropdownMenu,\n.jp-FileBrowser-toolbar .jp-Toolbar-item.jp-ToolbarButton,\n.jp-FileBrowser-toolbar .jp-Toolbar-item.jp-CommandToolbarButton {\n  border: solid 1px var(--jp-border-color2);\n  margin: 1px;\n  padding: 0px;\n}\n\n.jp-FileBrowser-toolbar > .jp-Toolbar-item.jp-ToolbarButton:hover,\n.jp-FileBrowser-toolbar > .jp-Toolbar-item.jp-CommandToolbarButton:hover,\n.jp-FileBrowser-toolbar > .jp-Toolbar-item.jp-DropdownMenu:hover {\n  background: var(--neutral-fill-stealth-hover);\n}\n\n.jp-FileBrowser-toolbar .lm-MenuBar-item {\n  height: var(--jp-private-toolbar-height);\n  display: inline-flex;\n  align-items: center;\n}\n\n.jp-FileBrowser-toolbar .jp-ToolbarButtonComponent {\n  height: var(--jp-flat-button-height);\n}\n\n.jp-FileBrowser-toolbar jp-button.jp-ToolbarButtonComponent:hover {\n  background: inherit;\n}\n\n.jp-DirListing-content .jp-DirListing-checkboxWrapper {\n  visibility: visible;\n}\n\n/* Action buttons */\n\n.jp-FileBrowser-toolbar > .jp-FileAction > .jp-ToolbarButtonComponent > svg {\n  display: none;\n}\n\n.jp-FileBrowser-toolbar > #fileAction-delete {\n  background-color: var(--jp-error-color1);\n}\n\n.jp-FileBrowser-toolbar\n  .jp-ToolbarButtonComponent[data-command='filebrowser:delete']\n  .jp-ToolbarButtonComponent-label {\n  color: var(--jp-ui-inverse-font-color1);\n}\n\n.jp-FileBrowser-toolbar .jp-FileAction {\n  border: solid 1px var(--jp-border-color2);\n  margin: 1px;\n  min-height: var(--jp-private-toolbar-height);\n}\n\nbody[data-format='mobile'] #fileAction-placeholder {\n  display: none;\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./../packages/tree-extension/style/base.css\"],\"names\":[],\"mappings\":\"AAAA;;;;8EAI8E;;AAE9E;;;EAGE,yCAAyC;EACzC,WAAW;EACX,YAAY;AACd;;AAEA;;;EAGE,6CAA6C;AAC/C;;AAEA;EACE,wCAAwC;EACxC,oBAAoB;EACpB,mBAAmB;AACrB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,mBAAmB;;AAEnB;EACE,aAAa;AACf;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;;;EAGE,uCAAuC;AACzC;;AAEA;EACE,yCAAyC;EACzC,WAAW;EACX,4CAA4C;AAC9C;;AAEA;EACE,aAAa;AACf\",\"sourcesContent\":[\"/*-----------------------------------------------------------------------------\\n| Copyright (c) Jupyter Development Team.\\n|\\n| Distributed under the terms of the Modified BSD License.\\n|----------------------------------------------------------------------------*/\\n\\n.jp-FileBrowser-toolbar .jp-Toolbar-item.jp-DropdownMenu,\\n.jp-FileBrowser-toolbar .jp-Toolbar-item.jp-ToolbarButton,\\n.jp-FileBrowser-toolbar .jp-Toolbar-item.jp-CommandToolbarButton {\\n  border: solid 1px var(--jp-border-color2);\\n  margin: 1px;\\n  padding: 0px;\\n}\\n\\n.jp-FileBrowser-toolbar > .jp-Toolbar-item.jp-ToolbarButton:hover,\\n.jp-FileBrowser-toolbar > .jp-Toolbar-item.jp-CommandToolbarButton:hover,\\n.jp-FileBrowser-toolbar > .jp-Toolbar-item.jp-DropdownMenu:hover {\\n  background: var(--neutral-fill-stealth-hover);\\n}\\n\\n.jp-FileBrowser-toolbar .lm-MenuBar-item {\\n  height: var(--jp-private-toolbar-height);\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.jp-FileBrowser-toolbar .jp-ToolbarButtonComponent {\\n  height: var(--jp-flat-button-height);\\n}\\n\\n.jp-FileBrowser-toolbar jp-button.jp-ToolbarButtonComponent:hover {\\n  background: inherit;\\n}\\n\\n.jp-DirListing-content .jp-DirListing-checkboxWrapper {\\n  visibility: visible;\\n}\\n\\n/* Action buttons */\\n\\n.jp-FileBrowser-toolbar > .jp-FileAction > .jp-ToolbarButtonComponent > svg {\\n  display: none;\\n}\\n\\n.jp-FileBrowser-toolbar > #fileAction-delete {\\n  background-color: var(--jp-error-color1);\\n}\\n\\n.jp-FileBrowser-toolbar\\n  .jp-ToolbarButtonComponent[data-command='filebrowser:delete']\\n  .jp-ToolbarButtonComponent-label {\\n  color: var(--jp-ui-inverse-font-color1);\\n}\\n\\n.jp-FileBrowser-toolbar .jp-FileAction {\\n  border: solid 1px var(--jp-border-color2);\\n  margin: 1px;\\n  min-height: var(--jp-private-toolbar-height);\\n}\\n\\nbody[data-format='mobile'] #fileAction-placeholder {\\n  display: none;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.jp-FileBrowser {\n  height: 100%;\n}\n\n.lm-TabPanel {\n  height: 100%;\n}\n\n.jp-TreePanel .lm-TabPanel-tabBar {\n  overflow: visible;\n  min-height: 32px;\n  border-bottom: unset;\n  height: var(--jp-private-toolbar-height);\n}\n\n.jp-TreePanel .lm-TabBar-content {\n  height: 100%;\n}\n\n.jp-TreePanel .lm-TabBar-tab {\n  flex: 0 1 auto;\n  color: var(--jp-ui-font-color0);\n  font-size: var(--jp-ui-font-size1);\n  height: 100%;\n}\n\n.jp-TreePanel .lm-TabBar-tabLabel {\n  padding-left: 5px;\n  padding-right: 5px;\n}\n\n.jp-FileBrowser-toolbar.jp-Toolbar .jp-ToolbarButtonComponent {\n  width: unset;\n}\n\n.jp-FileBrowser-toolbar > .jp-Toolbar-item {\n  flex-direction: column;\n  justify-content: center;\n}\n\n.jp-DropdownMenu .lm-MenuBar-itemIcon svg {\n  vertical-align: sub;\n}\n\njp-button[data-command='filebrowser:refresh'] .jp-ToolbarButtonComponent-label {\n  display: none;\n}\n\n.jp-TreePanel .lm-TabBar-tabIcon svg {\n  vertical-align: sub;\n}\n`, \"\",{\"version\":3,\"sources\":[\"webpack://./../packages/tree/style/base.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,YAAY;AACd;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,iBAAiB;EACjB,gBAAgB;EAChB,oBAAoB;EACpB,wCAAwC;AAC1C;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,cAAc;EACd,+BAA+B;EAC/B,kCAAkC;EAClC,YAAY;AACd;;AAEA;EACE,iBAAiB;EACjB,kBAAkB;AACpB;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,sBAAsB;EACtB,uBAAuB;AACzB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,mBAAmB;AACrB\",\"sourcesContent\":[\".jp-FileBrowser {\\n  height: 100%;\\n}\\n\\n.lm-TabPanel {\\n  height: 100%;\\n}\\n\\n.jp-TreePanel .lm-TabPanel-tabBar {\\n  overflow: visible;\\n  min-height: 32px;\\n  border-bottom: unset;\\n  height: var(--jp-private-toolbar-height);\\n}\\n\\n.jp-TreePanel .lm-TabBar-content {\\n  height: 100%;\\n}\\n\\n.jp-TreePanel .lm-TabBar-tab {\\n  flex: 0 1 auto;\\n  color: var(--jp-ui-font-color0);\\n  font-size: var(--jp-ui-font-size1);\\n  height: 100%;\\n}\\n\\n.jp-TreePanel .lm-TabBar-tabLabel {\\n  padding-left: 5px;\\n  padding-right: 5px;\\n}\\n\\n.jp-FileBrowser-toolbar.jp-Toolbar .jp-ToolbarButtonComponent {\\n  width: unset;\\n}\\n\\n.jp-FileBrowser-toolbar > .jp-Toolbar-item {\\n  flex-direction: column;\\n  justify-content: center;\\n}\\n\\n.jp-DropdownMenu .lm-MenuBar-itemIcon svg {\\n  vertical-align: sub;\\n}\\n\\njp-button[data-command='filebrowser:refresh'] .jp-ToolbarButtonComponent-label {\\n  display: none;\\n}\\n\\n.jp-TreePanel .lm-TabBar-tabIcon svg {\\n  vertical-align: sub;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_SOURCEMAP_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/sourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../node_modules/@jupyterlab/builder/node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, ``, \"\",{\"version\":3,\"sources\":[],\"names\":[],\"mappings\":\"\",\"sourceRoot\":\"\"}]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n"], "names": [], "sourceRoot": ""}