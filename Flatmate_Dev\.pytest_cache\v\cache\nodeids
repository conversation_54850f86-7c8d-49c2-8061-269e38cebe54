["tests/AUGMENT TESTS/test_apply_button_simple.py::test_operator_detection", "tests/AUGMENT TESTS/test_column_order_service.py::test_column_order_service", "tests/AUGMENT TESTS/test_column_order_simple.py::test_column_ordering", "tests/AUGMENT TESTS/test_dataframe_reorder.py::test_dataframe_reorder", "tests/AUGMENT TESTS/test_export_simple.py::test_export_method_updated", "tests/AUGMENT TESTS/test_export_simple.py::test_method_exists", "tests/AUGMENT TESTS/test_export_simple.py::test_signal_connections", "tests/AUGMENT TESTS/test_filter_debug.py::test_column_selector", "tests/AUGMENT TESTS/test_filter_debug.py::test_filter_pattern_parsing", "tests/AUGMENT TESTS/test_filter_debug.py::test_table_config", "tests/AUGMENT TESTS/test_filter_persistence.py::test_pattern_parsing", "tests/AUGMENT TESTS/test_filter_unit.py::test_custom_table_view_methods", "tests/AUGMENT TESTS/test_filter_unit.py::test_filter_group_methods", "tests/AUGMENT TESTS/test_filter_unit.py::test_filter_logic", "tests/AUGMENT TESTS/test_filter_unit.py::test_placeholder_text", "tests/AUGMENT TESTS/test_filter_unit.py::test_table_config", "tests/AUGMENT TESTS/test_grouping_functionality.py::test_complex_expressions", "tests/AUGMENT TESTS/test_grouping_functionality.py::test_grouping_matching", "tests/AUGMENT TESTS/test_grouping_functionality.py::test_grouping_parsing", "tests/AUGMENT TESTS/test_integration.py::test_columns_integration", "tests/AUGMENT TESTS/test_or_functionality.py::test_backward_compatibility", "tests/AUGMENT TESTS/test_or_functionality.py::test_or_matching", "tests/AUGMENT TESTS/test_or_functionality.py::test_or_parsing", "tests/test_auto_import_manager.py::TestAutoImportManager::test_configuration_loading", "tests/test_auto_import_manager.py::TestAutoImportManager::test_disabled_configuration", "tests/test_auto_import_manager.py::TestAutoImportManager::test_move_to_archive", "tests/test_auto_import_manager.py::TestAutoImportManager::test_move_to_failed", "tests/test_auto_import_manager.py::TestAutoImportManager::test_process_single_file_failure", "tests/test_auto_import_manager.py::TestAutoImportManager::test_process_single_file_success", "tests/test_auto_import_manager.py::TestAutoImportManager::test_queue_file_for_processing", "tests/test_auto_import_manager.py::TestAutoImportManager::test_queue_full_handling", "tests/test_auto_import_manager.py::TestAutoImportManager::test_singleton_pattern", "tests/test_auto_import_manager.py::TestAutoImportManager::test_start_monitoring", "tests/test_column_order.py::test_column_ordering", "tests/test_pipeline.py::test_run_pipeline", "tests/test_real_config.py::test_real_categorize_config", "tests/test_real_csvs.py::test_handler"]