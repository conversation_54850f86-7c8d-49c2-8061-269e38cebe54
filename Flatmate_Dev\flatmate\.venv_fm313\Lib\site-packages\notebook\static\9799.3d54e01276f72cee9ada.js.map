{"version": 3, "file": "9799.3d54e01276f72cee9ada.js?v=3d54e01276f72cee9ada", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAgO;AACpF;AAC9E;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,gEAAU;AAC9B;AACA;AACA,yBAAyB;AACzB;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,mBAAmB,EAAE,iCAAiC;AACrE;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,WAAW;AACpE;AACA;AACA;AACA,6CAA6C,4DAA4D;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,KAAK;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,gEAAU,+CAA+C,KAAK;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,UAAU,SAAS;AACnB;AACA;AACA;AACA,yBAAyB,oBAAoB,KAAK,OAAO,GAAG,kBAAkB;AAC9E;AACA;AACA;AACA;AACA;AACA,sCAAsC,yDAAU;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,OAAO;AACjB,yCAAyC;AACzC;AACA;AACA,qBAAqB;AACrB;AACA;AACA,uBAAuB,gGAAgG;AACvH,mBAAmB,8DAAe;AAClC;AACA,KAAK,MAAM,mDAAmD;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,0DAAW;AACtD,2CAA2C,0DAAW;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C,uBAAuB,8DAAW,qBAAqB,gEAAa;AACpE;AACA;AACA,6BAA6B,8DAAW;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,sCAAsC;AACpD;AACA;AACA;AACA,wBAAwB,8DAAW,uBAAuB,gEAAa;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D,qBAAqB;AACjF,2BAA2B,8DAAW;AACtC;AACA;AACA,qBAAqB,gEAAa;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF,wBAAwB;AAC/G,uBAAuB,8DAAW;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,gEAAa;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,gEAAa;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,gEAAa,CAAC,8DAAW;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sCAAsC,oDAAK;AAC3C;AACA,eAAe,gEAAa;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,oCAAoC,uDAAS;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE;AACjE;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,KAAK,IAAI,gBAAgB,IAAI,aAAa,kBAAkB;AAC9E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,4BAA4B,iBAAiB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,oBAAoB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,UAAU;AAC5B,4CAA4C,wBAAwB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,gCAAgC,yCAAyC;AACzE,SAAS;AACT;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,8BAA8B;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,aAAa;AAC/B,kBAAkB,OAAO;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,aAAa,8DAAY;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,eAAe;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,KAAK;AACvE;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,iCAAiC,cAAc;AAC/C,kBAAkB,oBAAoB,gBAAgB,UAAU;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,UAAU;AACxB;AACA;AACA;AACA;AACA;AACA,6DAA6D,OAAO;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,YAAY;AAC1C;AACA;AACA;AACA;AACA;AACA,kBAAkB,UAAU;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,oBAAoB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,4FAA4F,mBAAmB,uCAAuC;AACtJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,0DAAW;AAChD,4BAA4B;AAC5B,CAAC;AACD,uCAAuC,0DAAW;AAClD,qCAAqC,yDAAU;AAC/C,eAAe,iCAAiC;AAChD,wBAAwB,0BAA0B;AAClD;AACA,QAAQ,yDAAW;AACnB,QAAQ,wDAAU;AAClB;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,sEAAsE,qDAAqD;AAC/K;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,4DAAU;AACjD;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA,wBAAwB,yCAAyC;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,yCAAyC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,yCAAyC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,wDAAU;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,wBAAwB,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,8DAAY;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,iCAAiC,yCAAyC;AAC1E,YAAY,8DAAY;AACxB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,sCAAsC;AACvE;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,2CAA2C,4DAAU;AACrD;AACA,0DAA0D,yCAAyC;AACnG;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,sDAAsD,0CAA0C;AAChG;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,sCAAsC,mDAAI,sBAAsB,wDAAU;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED,+BAA+B,wDAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,qBAAqB,qBAAqB;AAC1C;AACA;AACA,KAAK;AACL,mDAAmD,eAAe;AAClE,oDAAoD,cAAc;AAClE,0DAA0D,UAAU,qBAAqB,KAAK;AAC9F,2DAA2D,SAAS,qBAAqB,KAAK;AAC9F,iCAAiC,8BAA8B;AAC/D,gCAAgC,8BAA8B;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,qBAAqB;AACrB,KAAK;AACL;AACA,qBAAqB;AACrB,KAAK;AACL;AACA,qBAAqB;AACrB,KAAK;AACL;AACA,qBAAqB;AACrB,KAAK;AACL;AACA,qBAAqB;AACrB,KAAK;AACL;AACA,qBAAqB;AACrB,KAAK;AACL;AACA,qBAAqB;AACrB,KAAK;AACL;AACA,qBAAqB;AACrB,KAAK;AACL;AACA,qBAAqB,wBAAwB;AAC7C,KAAK;AACL;AACA,qBAAqB;AACrB,KAAK;AACL;AACA,qBAAqB;AACrB;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,sDAAO;AACxD,4CAA4C,sDAAO;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,UAAU;AAC1C,0CAA0C,4DAAU;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,8BAA8B,gBAAgB,eAAe,KAAK,OAAO;AACzE;AACA,iDAAiD;AACjD,gCAAgC,mBAAmB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,WAAW;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,+BAA+B,wDAAU,UAAU,uCAAuC,wDAAU;AACpG;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB,OAAO;AACP,8BAA8B,wDAAU,QAAQ,0BAA0B;AAC1E;AACA;AACA;AACA;AACA,oBAAoB,wDAAU;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,0DAAW;AAC1C,0BAA0B;AAC1B,CAAC;AACD,iCAAiC,0DAAW;AAC5C,kCAAkC,yDAAU;AAC5C,eAAe,cAAc;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,kBAAkB,wDAAU,6CAA6C,wDAAU;AACnF,CAAC;AACD;AACA,WAAW,8DAAe,sDAAsD,8DAAe;AAC/F;AACA;AACA;AACA;AACA;;AAEA,gBAAgB,OAAO,KAAK,EAAE,OAAO,IAAI,MAAM,EAAE,MAAM,KAAK,OAAO,GAAG;;AAEtE,SAAS,oCAAoC;AAC7C;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,4BAA4B,EAAE,QAAQ,cAAc;AACpD;;AAEA,uBAAuB,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,eAAe;AAC7B,cAAc,OAAO;AACrB;AACA,uBAAuB,kDAAkD,mDAAI,WAAW;AACxF;AACA,wEAAwE,0DAAW;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,0DAAW;AACxC;AACA;AACA;AACA;AACA;AACA,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA,4BAA4B,6BAA6B;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,4DAA4D;AAClE,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,oDAAK;AACxC,oBAAoB;AACpB,CAAC;AACD,sCAAsC,mDAAI,sBAAsB,oDAAM;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,iBAAiB,0BAA0B;AACpF;AACA,2CAA2C,wDAAU;AACrD;AACA;AACA,iDAAiD,oCAAoC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,gCAAgC,WAAW,IAAI,OAAO,GAAG,QAAQ;AACjE;AACA;AACA,gCAAgC,QAAQ;AACxC;AACA;AACA;AACA;AACA;AACA,gCAAgC,mEAAmB;AACnD;AACA;AACA;AACA;AACA,+CAA+C,mBAAmB;AAClE,cAAc,QAAQ;AACtB;AACA;AACA;AACA,8BAA8B,2BAA2B;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA,2BAA2B;AAC3B,gBAAgB,EAAE;AAClB;AACA;AACA,wCAAwC,0DAAW;AACnD;AACA,+CAA+C,sDAAO;AACtD;AACA;AACA,CAAC;AACD,qDAAqD,yDAAU;AAC/D;AACA;AACA;AACA,kCAAkC,yDAAU;AAC5C,eAAe,OAAO,uDAAQ,SAAS;AACvC;AACA;AACA;AACA;AACA,mCAAmC,sDAAsD;AACzF;AACA;AACA;AACA,uCAAuC,4DAA4D;AACnG;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA,oBAAoB,2BAA2B;AAC/C;AACA;AACA,WAAW,gEAAa;AACxB;AACA;AACA;AACA;AACA;AACA,kCAAkC,wDAAU;AAC5C;AACA;AACA;AACA,mDAAmD,gEAAa,CAAC,8DAAW;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,6BAA6B,iBAAiB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kFAAkF,8DAAW;AAC7F,6BAA6B,WAAW,gEAAgE;AACxG,+BAA+B,8DAAe;AAC9C;AACA;AACA,iBAAiB;AACjB,KAAK;AACL;AACA,yCAAyC,oDAAoD;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,8DAAW;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,yBAAyB,gEAAa,CAAC,8DAAW;AAClD;AACA;AACA;AACA,WAAW,gEAAa,CAAC,8DAAW;AACpC;AACA;AACA;AACA;AACA,qBAAqB,YAAY,gCAAgC,IAAI,+BAA+B;AACpG;AACA,uBAAuB,8DAAe;AACtC;AACA;AACA,qBAAqB,WAAW,wCAAwC;AACxE;AACA,uBAAuB,8DAAe;AACtC,iBAAiB;AACjB,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,qBAAqB,WAAW,gEAAgE;AAChG,uBAAuB,8DAAe;AACtC,wBAAwB;AACxB,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,YAAY,iCAAiC,IAAI,+BAA+B;AACrG;AACA,uBAAuB,8DAAe;AACtC;AACA;AACA;AACA,yBAAyB,WAAW,kCAAkC;AACtE;AACA,2BAA2B,8DAAe;AAC1C;AACA;AACA;AACA;AACA,yBAAyB,WAAW,sDAAsD;AAC1F,2BAA2B,8DAAe;AAC1C;AACA;AACA;AACA;AACA;AACA,qBAAqB,WAAW,kDAAkD;AAClF;AACA,uBAAuB,8DAAe;AACtC;AACA,qDAAqD,2DAAY;AACjE;AACA,yBAAyB,WAAW,kCAAkC;AACtE;AACA,2BAA2B,8DAAe;AAC1C;AACA,iBAAiB;AACjB,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,eAAe,gEAAU;AACzB;AACA;AACA;AACA,eAAe,gEAAU;AACzB;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,2DAAY;AAC7D;AACA;AACA;AACA,iGAAiG,2DAAY;AAC7G;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,yCAAyC;AAC/C,MAAM,oCAAoC;AAC1C,MAAM,qCAAqC;AAC3C,MAAM,mEAAmE;AACzE,MAAM,kEAAkE;AACxE,MAAM,0EAA0E;AAChF,MAAM,yEAAyE;AAC/E,MAAM;AACN;AACA,yCAAyC,mDAAI,sBAAsB,oDAAM;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE6hB;;;;;;;;;;;;;;;;;;;AC9hEpb;;AAEzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,2DAA2D,IAAI,SAAS,EAAE,mCAAmC;AAChI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE;AACjE,sCAAsC,SAAS;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,uBAAuB;AACtD;AACA;AACA;AACA;AACA,gCAAgC,qEAAqE;AACrG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kEAAkE;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,uBAAuB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,kBAAkB;AAClB,gBAAgB;AAChB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,mBAAmB;AAClD;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,UAAU;AACvD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,YAAY;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,WAAW;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,yCAAyC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,uBAAuB,OAAO,mBAAmB,OAAO,yBAAyB,OAAO;AACxF;AACA;AACA,6BAA6B,mDAAQ;AACrC;AACA,WAAW;AACX;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,+CAAI;AACpC;AACA;AACA;AACA;AACA;AACA,kDAAkD,mDAAQ;AAC1D;AACA;AACA;AACA;AACA;AACA,2EAA2E;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,SAAS,aAAa,aAAa;AACjD;AACA;AACA;AACA,wBAAwB,uBAAuB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,SAAS;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,SAAS;AAC3B,4BAA4B,+BAA+B;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,WAAW;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ,WAAW,SAAS,aAAa,OAAO;AAC9D,0BAA0B,SAAS;AACnC,+GAA+G;AAC/G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,OAAO;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,0BAA0B;AAC7D;AACA,oCAAoC,sBAAsB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,sBAAsB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,SAAS;AAC1C;AACA;AACA;AACA;AACA;AACA,4DAA4D,OAAO;AACnE;AACA,8EAA8E,mDAAQ;AACtF;AACA;AACA,mFAAmF,+BAA+B;AAClH;AACA;AACA,wCAAwC,+CAAI;AAC5C;AACA;AACA,qCAAqC,+CAAI;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,6DAA6D;AAC7I;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE;AACvE,mCAAmC,sDAAsD,GAAG,MAAM,sBAAsB,IAAI,MAAM,EAAE,qCAAqC;AACzK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,0DAA0D;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFAAqF,2BAA2B;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,+CAAI,SAAS;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,sBAAsB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,iDAAM;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,aAAa,mCAAmC,sBAAsB;AAC1H;AACA;AACA,wBAAwB,0BAA0B;AAClD;AACA;AACA;AACA,wBAAwB,sBAAsB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,mDAAQ;AACnC,gCAAgC,oBAAoB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,OAAO;AACnD;AACA;AACA;AACA;AACA;AACA,2BAA2B,kDAAO,4BAA4B,mDAAQ;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,4BAA4B,8DAAmB;AAC/C;AACA;AACA;AACA;AACA,wBAAwB,kCAAkC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA,kDAAkD,WAAW;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,SAAS;AACnC,mHAAmH;AACnH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,gBAAgB;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,WAAW;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,QAAQ,oBAAoB;AACrF;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA,2DAA2D,6CAA6C;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE4F;;;;;;;;ACv0D5F;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,MAAM;AACN;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,sBAAsB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sCAAsC;;AAEtC;AACA;AACA;;AAEA,4BAA4B;AAC5B;AACA;AACA;AACA,6BAA6B", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/autocomplete/dist/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@lezer/lr/dist/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/process/browser.js"], "sourcesContent": ["import { Annotation, StateEffect, EditorSelection, codePointAt, codePointSize, fromCodePoint, Facet, combineConfig, StateField, Prec, Text, Transaction, MapMode, RangeValue, RangeSet, CharCategory } from '@codemirror/state';\nimport { Direction, logException, showTooltip, EditorView, ViewPlugin, getTooltip, Decoration, WidgetType, keymap } from '@codemirror/view';\nimport { syntaxTree, indentUnit } from '@codemirror/language';\n\n/**\nAn instance of this is passed to completion source functions.\n*/\nclass CompletionContext {\n    /**\n    Create a new completion context. (Mostly useful for testing\n    completion sources—in the editor, the extension will create\n    these for you.)\n    */\n    constructor(\n    /**\n    The editor state that the completion happens in.\n    */\n    state, \n    /**\n    The position at which the completion is happening.\n    */\n    pos, \n    /**\n    Indicates whether completion was activated explicitly, or\n    implicitly by typing. The usual way to respond to this is to\n    only return completions when either there is part of a\n    completable entity before the cursor, or `explicit` is true.\n    */\n    explicit, \n    /**\n    The editor view. May be undefined if the context was created\n    in a situation where there is no such view available, such as\n    in synchronous updates via\n    [`CompletionResult.update`](https://codemirror.net/6/docs/ref/#autocomplete.CompletionResult.update)\n    or when called by test code.\n    */\n    view) {\n        this.state = state;\n        this.pos = pos;\n        this.explicit = explicit;\n        this.view = view;\n        /**\n        @internal\n        */\n        this.abortListeners = [];\n        /**\n        @internal\n        */\n        this.abortOnDocChange = false;\n    }\n    /**\n    Get the extent, content, and (if there is a token) type of the\n    token before `this.pos`.\n    */\n    tokenBefore(types) {\n        let token = syntaxTree(this.state).resolveInner(this.pos, -1);\n        while (token && types.indexOf(token.name) < 0)\n            token = token.parent;\n        return token ? { from: token.from, to: this.pos,\n            text: this.state.sliceDoc(token.from, this.pos),\n            type: token.type } : null;\n    }\n    /**\n    Get the match of the given expression directly before the\n    cursor.\n    */\n    matchBefore(expr) {\n        let line = this.state.doc.lineAt(this.pos);\n        let start = Math.max(line.from, this.pos - 250);\n        let str = line.text.slice(start - line.from, this.pos - line.from);\n        let found = str.search(ensureAnchor(expr, false));\n        return found < 0 ? null : { from: start + found, to: this.pos, text: str.slice(found) };\n    }\n    /**\n    Yields true when the query has been aborted. Can be useful in\n    asynchronous queries to avoid doing work that will be ignored.\n    */\n    get aborted() { return this.abortListeners == null; }\n    /**\n    Allows you to register abort handlers, which will be called when\n    the query is\n    [aborted](https://codemirror.net/6/docs/ref/#autocomplete.CompletionContext.aborted).\n    \n    By default, running queries will not be aborted for regular\n    typing or backspacing, on the assumption that they are likely to\n    return a result with a\n    [`validFor`](https://codemirror.net/6/docs/ref/#autocomplete.CompletionResult.validFor) field that\n    allows the result to be used after all. Passing `onDocChange:\n    true` will cause this query to be aborted for any document\n    change.\n    */\n    addEventListener(type, listener, options) {\n        if (type == \"abort\" && this.abortListeners) {\n            this.abortListeners.push(listener);\n            if (options && options.onDocChange)\n                this.abortOnDocChange = true;\n        }\n    }\n}\nfunction toSet(chars) {\n    let flat = Object.keys(chars).join(\"\");\n    let words = /\\w/.test(flat);\n    if (words)\n        flat = flat.replace(/\\w/g, \"\");\n    return `[${words ? \"\\\\w\" : \"\"}${flat.replace(/[^\\w\\s]/g, \"\\\\$&\")}]`;\n}\nfunction prefixMatch(options) {\n    let first = Object.create(null), rest = Object.create(null);\n    for (let { label } of options) {\n        first[label[0]] = true;\n        for (let i = 1; i < label.length; i++)\n            rest[label[i]] = true;\n    }\n    let source = toSet(first) + toSet(rest) + \"*$\";\n    return [new RegExp(\"^\" + source), new RegExp(source)];\n}\n/**\nGiven a a fixed array of options, return an autocompleter that\ncompletes them.\n*/\nfunction completeFromList(list) {\n    let options = list.map(o => typeof o == \"string\" ? { label: o } : o);\n    let [validFor, match] = options.every(o => /^\\w+$/.test(o.label)) ? [/\\w*$/, /\\w+$/] : prefixMatch(options);\n    return (context) => {\n        let token = context.matchBefore(match);\n        return token || context.explicit ? { from: token ? token.from : context.pos, options, validFor } : null;\n    };\n}\n/**\nWrap the given completion source so that it will only fire when the\ncursor is in a syntax node with one of the given names.\n*/\nfunction ifIn(nodes, source) {\n    return (context) => {\n        for (let pos = syntaxTree(context.state).resolveInner(context.pos, -1); pos; pos = pos.parent) {\n            if (nodes.indexOf(pos.name) > -1)\n                return source(context);\n            if (pos.type.isTop)\n                break;\n        }\n        return null;\n    };\n}\n/**\nWrap the given completion source so that it will not fire when the\ncursor is in a syntax node with one of the given names.\n*/\nfunction ifNotIn(nodes, source) {\n    return (context) => {\n        for (let pos = syntaxTree(context.state).resolveInner(context.pos, -1); pos; pos = pos.parent) {\n            if (nodes.indexOf(pos.name) > -1)\n                return null;\n            if (pos.type.isTop)\n                break;\n        }\n        return source(context);\n    };\n}\nclass Option {\n    constructor(completion, source, match, score) {\n        this.completion = completion;\n        this.source = source;\n        this.match = match;\n        this.score = score;\n    }\n}\nfunction cur(state) { return state.selection.main.from; }\n// Make sure the given regexp has a $ at its end and, if `start` is\n// true, a ^ at its start.\nfunction ensureAnchor(expr, start) {\n    var _a;\n    let { source } = expr;\n    let addStart = start && source[0] != \"^\", addEnd = source[source.length - 1] != \"$\";\n    if (!addStart && !addEnd)\n        return expr;\n    return new RegExp(`${addStart ? \"^\" : \"\"}(?:${source})${addEnd ? \"$\" : \"\"}`, (_a = expr.flags) !== null && _a !== void 0 ? _a : (expr.ignoreCase ? \"i\" : \"\"));\n}\n/**\nThis annotation is added to transactions that are produced by\npicking a completion.\n*/\nconst pickedCompletion = /*@__PURE__*/Annotation.define();\n/**\nHelper function that returns a transaction spec which inserts a\ncompletion's text in the main selection range, and any other\nselection range that has the same text in front of it.\n*/\nfunction insertCompletionText(state, text, from, to) {\n    let { main } = state.selection, fromOff = from - main.from, toOff = to - main.from;\n    return Object.assign(Object.assign({}, state.changeByRange(range => {\n        if (range != main && from != to &&\n            state.sliceDoc(range.from + fromOff, range.from + toOff) != state.sliceDoc(from, to))\n            return { range };\n        let lines = state.toText(text);\n        return {\n            changes: { from: range.from + fromOff, to: to == main.from ? range.to : range.from + toOff, insert: lines },\n            range: EditorSelection.cursor(range.from + fromOff + lines.length)\n        };\n    })), { scrollIntoView: true, userEvent: \"input.complete\" });\n}\nconst SourceCache = /*@__PURE__*/new WeakMap();\nfunction asSource(source) {\n    if (!Array.isArray(source))\n        return source;\n    let known = SourceCache.get(source);\n    if (!known)\n        SourceCache.set(source, known = completeFromList(source));\n    return known;\n}\nconst startCompletionEffect = /*@__PURE__*/StateEffect.define();\nconst closeCompletionEffect = /*@__PURE__*/StateEffect.define();\n\n// A pattern matcher for fuzzy completion matching. Create an instance\n// once for a pattern, and then use that to match any number of\n// completions.\nclass FuzzyMatcher {\n    constructor(pattern) {\n        this.pattern = pattern;\n        this.chars = [];\n        this.folded = [];\n        // Buffers reused by calls to `match` to track matched character\n        // positions.\n        this.any = [];\n        this.precise = [];\n        this.byWord = [];\n        this.score = 0;\n        this.matched = [];\n        for (let p = 0; p < pattern.length;) {\n            let char = codePointAt(pattern, p), size = codePointSize(char);\n            this.chars.push(char);\n            let part = pattern.slice(p, p + size), upper = part.toUpperCase();\n            this.folded.push(codePointAt(upper == part ? part.toLowerCase() : upper, 0));\n            p += size;\n        }\n        this.astral = pattern.length != this.chars.length;\n    }\n    ret(score, matched) {\n        this.score = score;\n        this.matched = matched;\n        return this;\n    }\n    // Matches a given word (completion) against the pattern (input).\n    // Will return a boolean indicating whether there was a match and,\n    // on success, set `this.score` to the score, `this.matched` to an\n    // array of `from, to` pairs indicating the matched parts of `word`.\n    //\n    // The score is a number that is more negative the worse the match\n    // is. See `Penalty` above.\n    match(word) {\n        if (this.pattern.length == 0)\n            return this.ret(-100 /* Penalty.NotFull */, []);\n        if (word.length < this.pattern.length)\n            return null;\n        let { chars, folded, any, precise, byWord } = this;\n        // For single-character queries, only match when they occur right\n        // at the start\n        if (chars.length == 1) {\n            let first = codePointAt(word, 0), firstSize = codePointSize(first);\n            let score = firstSize == word.length ? 0 : -100 /* Penalty.NotFull */;\n            if (first == chars[0]) ;\n            else if (first == folded[0])\n                score += -200 /* Penalty.CaseFold */;\n            else\n                return null;\n            return this.ret(score, [0, firstSize]);\n        }\n        let direct = word.indexOf(this.pattern);\n        if (direct == 0)\n            return this.ret(word.length == this.pattern.length ? 0 : -100 /* Penalty.NotFull */, [0, this.pattern.length]);\n        let len = chars.length, anyTo = 0;\n        if (direct < 0) {\n            for (let i = 0, e = Math.min(word.length, 200); i < e && anyTo < len;) {\n                let next = codePointAt(word, i);\n                if (next == chars[anyTo] || next == folded[anyTo])\n                    any[anyTo++] = i;\n                i += codePointSize(next);\n            }\n            // No match, exit immediately\n            if (anyTo < len)\n                return null;\n        }\n        // This tracks the extent of the precise (non-folded, not\n        // necessarily adjacent) match\n        let preciseTo = 0;\n        // Tracks whether there is a match that hits only characters that\n        // appear to be starting words. `byWordFolded` is set to true when\n        // a case folded character is encountered in such a match\n        let byWordTo = 0, byWordFolded = false;\n        // If we've found a partial adjacent match, these track its state\n        let adjacentTo = 0, adjacentStart = -1, adjacentEnd = -1;\n        let hasLower = /[a-z]/.test(word), wordAdjacent = true;\n        // Go over the option's text, scanning for the various kinds of matches\n        for (let i = 0, e = Math.min(word.length, 200), prevType = 0 /* Tp.NonWord */; i < e && byWordTo < len;) {\n            let next = codePointAt(word, i);\n            if (direct < 0) {\n                if (preciseTo < len && next == chars[preciseTo])\n                    precise[preciseTo++] = i;\n                if (adjacentTo < len) {\n                    if (next == chars[adjacentTo] || next == folded[adjacentTo]) {\n                        if (adjacentTo == 0)\n                            adjacentStart = i;\n                        adjacentEnd = i + 1;\n                        adjacentTo++;\n                    }\n                    else {\n                        adjacentTo = 0;\n                    }\n                }\n            }\n            let ch, type = next < 0xff\n                ? (next >= 48 && next <= 57 || next >= 97 && next <= 122 ? 2 /* Tp.Lower */ : next >= 65 && next <= 90 ? 1 /* Tp.Upper */ : 0 /* Tp.NonWord */)\n                : ((ch = fromCodePoint(next)) != ch.toLowerCase() ? 1 /* Tp.Upper */ : ch != ch.toUpperCase() ? 2 /* Tp.Lower */ : 0 /* Tp.NonWord */);\n            if (!i || type == 1 /* Tp.Upper */ && hasLower || prevType == 0 /* Tp.NonWord */ && type != 0 /* Tp.NonWord */) {\n                if (chars[byWordTo] == next || (folded[byWordTo] == next && (byWordFolded = true)))\n                    byWord[byWordTo++] = i;\n                else if (byWord.length)\n                    wordAdjacent = false;\n            }\n            prevType = type;\n            i += codePointSize(next);\n        }\n        if (byWordTo == len && byWord[0] == 0 && wordAdjacent)\n            return this.result(-100 /* Penalty.ByWord */ + (byWordFolded ? -200 /* Penalty.CaseFold */ : 0), byWord, word);\n        if (adjacentTo == len && adjacentStart == 0)\n            return this.ret(-200 /* Penalty.CaseFold */ - word.length + (adjacentEnd == word.length ? 0 : -100 /* Penalty.NotFull */), [0, adjacentEnd]);\n        if (direct > -1)\n            return this.ret(-700 /* Penalty.NotStart */ - word.length, [direct, direct + this.pattern.length]);\n        if (adjacentTo == len)\n            return this.ret(-200 /* Penalty.CaseFold */ + -700 /* Penalty.NotStart */ - word.length, [adjacentStart, adjacentEnd]);\n        if (byWordTo == len)\n            return this.result(-100 /* Penalty.ByWord */ + (byWordFolded ? -200 /* Penalty.CaseFold */ : 0) + -700 /* Penalty.NotStart */ +\n                (wordAdjacent ? 0 : -1100 /* Penalty.Gap */), byWord, word);\n        return chars.length == 2 ? null\n            : this.result((any[0] ? -700 /* Penalty.NotStart */ : 0) + -200 /* Penalty.CaseFold */ + -1100 /* Penalty.Gap */, any, word);\n    }\n    result(score, positions, word) {\n        let result = [], i = 0;\n        for (let pos of positions) {\n            let to = pos + (this.astral ? codePointSize(codePointAt(word, pos)) : 1);\n            if (i && result[i - 1] == pos)\n                result[i - 1] = to;\n            else {\n                result[i++] = pos;\n                result[i++] = to;\n            }\n        }\n        return this.ret(score - word.length, result);\n    }\n}\nclass StrictMatcher {\n    constructor(pattern) {\n        this.pattern = pattern;\n        this.matched = [];\n        this.score = 0;\n        this.folded = pattern.toLowerCase();\n    }\n    match(word) {\n        if (word.length < this.pattern.length)\n            return null;\n        let start = word.slice(0, this.pattern.length);\n        let match = start == this.pattern ? 0 : start.toLowerCase() == this.folded ? -200 /* Penalty.CaseFold */ : null;\n        if (match == null)\n            return null;\n        this.matched = [0, start.length];\n        this.score = match + (word.length == this.pattern.length ? 0 : -100 /* Penalty.NotFull */);\n        return this;\n    }\n}\n\nconst completionConfig = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            activateOnTyping: true,\n            activateOnCompletion: () => false,\n            activateOnTypingDelay: 100,\n            selectOnOpen: true,\n            override: null,\n            closeOnBlur: true,\n            maxRenderedOptions: 100,\n            defaultKeymap: true,\n            tooltipClass: () => \"\",\n            optionClass: () => \"\",\n            aboveCursor: false,\n            icons: true,\n            addToOptions: [],\n            positionInfo: defaultPositionInfo,\n            filterStrict: false,\n            compareCompletions: (a, b) => a.label.localeCompare(b.label),\n            interactionDelay: 75,\n            updateSyncTime: 100\n        }, {\n            defaultKeymap: (a, b) => a && b,\n            closeOnBlur: (a, b) => a && b,\n            icons: (a, b) => a && b,\n            tooltipClass: (a, b) => c => joinClass(a(c), b(c)),\n            optionClass: (a, b) => c => joinClass(a(c), b(c)),\n            addToOptions: (a, b) => a.concat(b),\n            filterStrict: (a, b) => a || b,\n        });\n    }\n});\nfunction joinClass(a, b) {\n    return a ? b ? a + \" \" + b : a : b;\n}\nfunction defaultPositionInfo(view, list, option, info, space, tooltip) {\n    let rtl = view.textDirection == Direction.RTL, left = rtl, narrow = false;\n    let side = \"top\", offset, maxWidth;\n    let spaceLeft = list.left - space.left, spaceRight = space.right - list.right;\n    let infoWidth = info.right - info.left, infoHeight = info.bottom - info.top;\n    if (left && spaceLeft < Math.min(infoWidth, spaceRight))\n        left = false;\n    else if (!left && spaceRight < Math.min(infoWidth, spaceLeft))\n        left = true;\n    if (infoWidth <= (left ? spaceLeft : spaceRight)) {\n        offset = Math.max(space.top, Math.min(option.top, space.bottom - infoHeight)) - list.top;\n        maxWidth = Math.min(400 /* Info.Width */, left ? spaceLeft : spaceRight);\n    }\n    else {\n        narrow = true;\n        maxWidth = Math.min(400 /* Info.Width */, (rtl ? list.right : space.right - list.left) - 30 /* Info.Margin */);\n        let spaceBelow = space.bottom - list.bottom;\n        if (spaceBelow >= infoHeight || spaceBelow > list.top) { // Below the completion\n            offset = option.bottom - list.top;\n        }\n        else { // Above it\n            side = \"bottom\";\n            offset = list.bottom - option.top;\n        }\n    }\n    let scaleY = (list.bottom - list.top) / tooltip.offsetHeight;\n    let scaleX = (list.right - list.left) / tooltip.offsetWidth;\n    return {\n        style: `${side}: ${offset / scaleY}px; max-width: ${maxWidth / scaleX}px`,\n        class: \"cm-completionInfo-\" + (narrow ? (rtl ? \"left-narrow\" : \"right-narrow\") : left ? \"left\" : \"right\")\n    };\n}\n\nfunction optionContent(config) {\n    let content = config.addToOptions.slice();\n    if (config.icons)\n        content.push({\n            render(completion) {\n                let icon = document.createElement(\"div\");\n                icon.classList.add(\"cm-completionIcon\");\n                if (completion.type)\n                    icon.classList.add(...completion.type.split(/\\s+/g).map(cls => \"cm-completionIcon-\" + cls));\n                icon.setAttribute(\"aria-hidden\", \"true\");\n                return icon;\n            },\n            position: 20\n        });\n    content.push({\n        render(completion, _s, _v, match) {\n            let labelElt = document.createElement(\"span\");\n            labelElt.className = \"cm-completionLabel\";\n            let label = completion.displayLabel || completion.label, off = 0;\n            for (let j = 0; j < match.length;) {\n                let from = match[j++], to = match[j++];\n                if (from > off)\n                    labelElt.appendChild(document.createTextNode(label.slice(off, from)));\n                let span = labelElt.appendChild(document.createElement(\"span\"));\n                span.appendChild(document.createTextNode(label.slice(from, to)));\n                span.className = \"cm-completionMatchedText\";\n                off = to;\n            }\n            if (off < label.length)\n                labelElt.appendChild(document.createTextNode(label.slice(off)));\n            return labelElt;\n        },\n        position: 50\n    }, {\n        render(completion) {\n            if (!completion.detail)\n                return null;\n            let detailElt = document.createElement(\"span\");\n            detailElt.className = \"cm-completionDetail\";\n            detailElt.textContent = completion.detail;\n            return detailElt;\n        },\n        position: 80\n    });\n    return content.sort((a, b) => a.position - b.position).map(a => a.render);\n}\nfunction rangeAroundSelected(total, selected, max) {\n    if (total <= max)\n        return { from: 0, to: total };\n    if (selected < 0)\n        selected = 0;\n    if (selected <= (total >> 1)) {\n        let off = Math.floor(selected / max);\n        return { from: off * max, to: (off + 1) * max };\n    }\n    let off = Math.floor((total - selected) / max);\n    return { from: total - (off + 1) * max, to: total - off * max };\n}\nclass CompletionTooltip {\n    constructor(view, stateField, applyCompletion) {\n        this.view = view;\n        this.stateField = stateField;\n        this.applyCompletion = applyCompletion;\n        this.info = null;\n        this.infoDestroy = null;\n        this.placeInfoReq = {\n            read: () => this.measureInfo(),\n            write: (pos) => this.placeInfo(pos),\n            key: this\n        };\n        this.space = null;\n        this.currentClass = \"\";\n        let cState = view.state.field(stateField);\n        let { options, selected } = cState.open;\n        let config = view.state.facet(completionConfig);\n        this.optionContent = optionContent(config);\n        this.optionClass = config.optionClass;\n        this.tooltipClass = config.tooltipClass;\n        this.range = rangeAroundSelected(options.length, selected, config.maxRenderedOptions);\n        this.dom = document.createElement(\"div\");\n        this.dom.className = \"cm-tooltip-autocomplete\";\n        this.updateTooltipClass(view.state);\n        this.dom.addEventListener(\"mousedown\", (e) => {\n            let { options } = view.state.field(stateField).open;\n            for (let dom = e.target, match; dom && dom != this.dom; dom = dom.parentNode) {\n                if (dom.nodeName == \"LI\" && (match = /-(\\d+)$/.exec(dom.id)) && +match[1] < options.length) {\n                    this.applyCompletion(view, options[+match[1]]);\n                    e.preventDefault();\n                    return;\n                }\n            }\n        });\n        this.dom.addEventListener(\"focusout\", (e) => {\n            let state = view.state.field(this.stateField, false);\n            if (state && state.tooltip && view.state.facet(completionConfig).closeOnBlur &&\n                e.relatedTarget != view.contentDOM)\n                view.dispatch({ effects: closeCompletionEffect.of(null) });\n        });\n        this.showOptions(options, cState.id);\n    }\n    mount() { this.updateSel(); }\n    showOptions(options, id) {\n        if (this.list)\n            this.list.remove();\n        this.list = this.dom.appendChild(this.createListBox(options, id, this.range));\n        this.list.addEventListener(\"scroll\", () => {\n            if (this.info)\n                this.view.requestMeasure(this.placeInfoReq);\n        });\n    }\n    update(update) {\n        var _a;\n        let cState = update.state.field(this.stateField);\n        let prevState = update.startState.field(this.stateField);\n        this.updateTooltipClass(update.state);\n        if (cState != prevState) {\n            let { options, selected, disabled } = cState.open;\n            if (!prevState.open || prevState.open.options != options) {\n                this.range = rangeAroundSelected(options.length, selected, update.state.facet(completionConfig).maxRenderedOptions);\n                this.showOptions(options, cState.id);\n            }\n            this.updateSel();\n            if (disabled != ((_a = prevState.open) === null || _a === void 0 ? void 0 : _a.disabled))\n                this.dom.classList.toggle(\"cm-tooltip-autocomplete-disabled\", !!disabled);\n        }\n    }\n    updateTooltipClass(state) {\n        let cls = this.tooltipClass(state);\n        if (cls != this.currentClass) {\n            for (let c of this.currentClass.split(\" \"))\n                if (c)\n                    this.dom.classList.remove(c);\n            for (let c of cls.split(\" \"))\n                if (c)\n                    this.dom.classList.add(c);\n            this.currentClass = cls;\n        }\n    }\n    positioned(space) {\n        this.space = space;\n        if (this.info)\n            this.view.requestMeasure(this.placeInfoReq);\n    }\n    updateSel() {\n        let cState = this.view.state.field(this.stateField), open = cState.open;\n        if (open.selected > -1 && open.selected < this.range.from || open.selected >= this.range.to) {\n            this.range = rangeAroundSelected(open.options.length, open.selected, this.view.state.facet(completionConfig).maxRenderedOptions);\n            this.showOptions(open.options, cState.id);\n        }\n        if (this.updateSelectedOption(open.selected)) {\n            this.destroyInfo();\n            let { completion } = open.options[open.selected];\n            let { info } = completion;\n            if (!info)\n                return;\n            let infoResult = typeof info === \"string\" ? document.createTextNode(info) : info(completion);\n            if (!infoResult)\n                return;\n            if (\"then\" in infoResult) {\n                infoResult.then(obj => {\n                    if (obj && this.view.state.field(this.stateField, false) == cState)\n                        this.addInfoPane(obj, completion);\n                }).catch(e => logException(this.view.state, e, \"completion info\"));\n            }\n            else {\n                this.addInfoPane(infoResult, completion);\n            }\n        }\n    }\n    addInfoPane(content, completion) {\n        this.destroyInfo();\n        let wrap = this.info = document.createElement(\"div\");\n        wrap.className = \"cm-tooltip cm-completionInfo\";\n        if (content.nodeType != null) {\n            wrap.appendChild(content);\n            this.infoDestroy = null;\n        }\n        else {\n            let { dom, destroy } = content;\n            wrap.appendChild(dom);\n            this.infoDestroy = destroy || null;\n        }\n        this.dom.appendChild(wrap);\n        this.view.requestMeasure(this.placeInfoReq);\n    }\n    updateSelectedOption(selected) {\n        let set = null;\n        for (let opt = this.list.firstChild, i = this.range.from; opt; opt = opt.nextSibling, i++) {\n            if (opt.nodeName != \"LI\" || !opt.id) {\n                i--; // A section header\n            }\n            else if (i == selected) {\n                if (!opt.hasAttribute(\"aria-selected\")) {\n                    opt.setAttribute(\"aria-selected\", \"true\");\n                    set = opt;\n                }\n            }\n            else {\n                if (opt.hasAttribute(\"aria-selected\"))\n                    opt.removeAttribute(\"aria-selected\");\n            }\n        }\n        if (set)\n            scrollIntoView(this.list, set);\n        return set;\n    }\n    measureInfo() {\n        let sel = this.dom.querySelector(\"[aria-selected]\");\n        if (!sel || !this.info)\n            return null;\n        let listRect = this.dom.getBoundingClientRect();\n        let infoRect = this.info.getBoundingClientRect();\n        let selRect = sel.getBoundingClientRect();\n        let space = this.space;\n        if (!space) {\n            let docElt = this.dom.ownerDocument.documentElement;\n            space = { left: 0, top: 0, right: docElt.clientWidth, bottom: docElt.clientHeight };\n        }\n        if (selRect.top > Math.min(space.bottom, listRect.bottom) - 10 ||\n            selRect.bottom < Math.max(space.top, listRect.top) + 10)\n            return null;\n        return this.view.state.facet(completionConfig).positionInfo(this.view, listRect, selRect, infoRect, space, this.dom);\n    }\n    placeInfo(pos) {\n        if (this.info) {\n            if (pos) {\n                if (pos.style)\n                    this.info.style.cssText = pos.style;\n                this.info.className = \"cm-tooltip cm-completionInfo \" + (pos.class || \"\");\n            }\n            else {\n                this.info.style.cssText = \"top: -1e6px\";\n            }\n        }\n    }\n    createListBox(options, id, range) {\n        const ul = document.createElement(\"ul\");\n        ul.id = id;\n        ul.setAttribute(\"role\", \"listbox\");\n        ul.setAttribute(\"aria-expanded\", \"true\");\n        ul.setAttribute(\"aria-label\", this.view.state.phrase(\"Completions\"));\n        ul.addEventListener(\"mousedown\", e => {\n            // Prevent focus change when clicking the scrollbar\n            if (e.target == ul)\n                e.preventDefault();\n        });\n        let curSection = null;\n        for (let i = range.from; i < range.to; i++) {\n            let { completion, match } = options[i], { section } = completion;\n            if (section) {\n                let name = typeof section == \"string\" ? section : section.name;\n                if (name != curSection && (i > range.from || range.from == 0)) {\n                    curSection = name;\n                    if (typeof section != \"string\" && section.header) {\n                        ul.appendChild(section.header(section));\n                    }\n                    else {\n                        let header = ul.appendChild(document.createElement(\"completion-section\"));\n                        header.textContent = name;\n                    }\n                }\n            }\n            const li = ul.appendChild(document.createElement(\"li\"));\n            li.id = id + \"-\" + i;\n            li.setAttribute(\"role\", \"option\");\n            let cls = this.optionClass(completion);\n            if (cls)\n                li.className = cls;\n            for (let source of this.optionContent) {\n                let node = source(completion, this.view.state, this.view, match);\n                if (node)\n                    li.appendChild(node);\n            }\n        }\n        if (range.from)\n            ul.classList.add(\"cm-completionListIncompleteTop\");\n        if (range.to < options.length)\n            ul.classList.add(\"cm-completionListIncompleteBottom\");\n        return ul;\n    }\n    destroyInfo() {\n        if (this.info) {\n            if (this.infoDestroy)\n                this.infoDestroy();\n            this.info.remove();\n            this.info = null;\n        }\n    }\n    destroy() {\n        this.destroyInfo();\n    }\n}\nfunction completionTooltip(stateField, applyCompletion) {\n    return (view) => new CompletionTooltip(view, stateField, applyCompletion);\n}\nfunction scrollIntoView(container, element) {\n    let parent = container.getBoundingClientRect();\n    let self = element.getBoundingClientRect();\n    let scaleY = parent.height / container.offsetHeight;\n    if (self.top < parent.top)\n        container.scrollTop -= (parent.top - self.top) / scaleY;\n    else if (self.bottom > parent.bottom)\n        container.scrollTop += (self.bottom - parent.bottom) / scaleY;\n}\n\n// Used to pick a preferred option when two options with the same\n// label occur in the result.\nfunction score(option) {\n    return (option.boost || 0) * 100 + (option.apply ? 10 : 0) + (option.info ? 5 : 0) +\n        (option.type ? 1 : 0);\n}\nfunction sortOptions(active, state) {\n    let options = [];\n    let sections = null;\n    let addOption = (option) => {\n        options.push(option);\n        let { section } = option.completion;\n        if (section) {\n            if (!sections)\n                sections = [];\n            let name = typeof section == \"string\" ? section : section.name;\n            if (!sections.some(s => s.name == name))\n                sections.push(typeof section == \"string\" ? { name } : section);\n        }\n    };\n    let conf = state.facet(completionConfig);\n    for (let a of active)\n        if (a.hasResult()) {\n            let getMatch = a.result.getMatch;\n            if (a.result.filter === false) {\n                for (let option of a.result.options) {\n                    addOption(new Option(option, a.source, getMatch ? getMatch(option) : [], 1e9 - options.length));\n                }\n            }\n            else {\n                let pattern = state.sliceDoc(a.from, a.to), match;\n                let matcher = conf.filterStrict ? new StrictMatcher(pattern) : new FuzzyMatcher(pattern);\n                for (let option of a.result.options)\n                    if (match = matcher.match(option.label)) {\n                        let matched = !option.displayLabel ? match.matched : getMatch ? getMatch(option, match.matched) : [];\n                        addOption(new Option(option, a.source, matched, match.score + (option.boost || 0)));\n                    }\n            }\n        }\n    if (sections) {\n        let sectionOrder = Object.create(null), pos = 0;\n        let cmp = (a, b) => { var _a, _b; return ((_a = a.rank) !== null && _a !== void 0 ? _a : 1e9) - ((_b = b.rank) !== null && _b !== void 0 ? _b : 1e9) || (a.name < b.name ? -1 : 1); };\n        for (let s of sections.sort(cmp)) {\n            pos -= 1e5;\n            sectionOrder[s.name] = pos;\n        }\n        for (let option of options) {\n            let { section } = option.completion;\n            if (section)\n                option.score += sectionOrder[typeof section == \"string\" ? section : section.name];\n        }\n    }\n    let result = [], prev = null;\n    let compare = conf.compareCompletions;\n    for (let opt of options.sort((a, b) => (b.score - a.score) || compare(a.completion, b.completion))) {\n        let cur = opt.completion;\n        if (!prev || prev.label != cur.label || prev.detail != cur.detail ||\n            (prev.type != null && cur.type != null && prev.type != cur.type) ||\n            prev.apply != cur.apply || prev.boost != cur.boost)\n            result.push(opt);\n        else if (score(opt.completion) > score(prev))\n            result[result.length - 1] = opt;\n        prev = opt.completion;\n    }\n    return result;\n}\nclass CompletionDialog {\n    constructor(options, attrs, tooltip, timestamp, selected, disabled) {\n        this.options = options;\n        this.attrs = attrs;\n        this.tooltip = tooltip;\n        this.timestamp = timestamp;\n        this.selected = selected;\n        this.disabled = disabled;\n    }\n    setSelected(selected, id) {\n        return selected == this.selected || selected >= this.options.length ? this\n            : new CompletionDialog(this.options, makeAttrs(id, selected), this.tooltip, this.timestamp, selected, this.disabled);\n    }\n    static build(active, state, id, prev, conf, didSetActive) {\n        if (prev && !didSetActive && active.some(s => s.isPending))\n            return prev.setDisabled();\n        let options = sortOptions(active, state);\n        if (!options.length)\n            return prev && active.some(a => a.isPending) ? prev.setDisabled() : null;\n        let selected = state.facet(completionConfig).selectOnOpen ? 0 : -1;\n        if (prev && prev.selected != selected && prev.selected != -1) {\n            let selectedValue = prev.options[prev.selected].completion;\n            for (let i = 0; i < options.length; i++)\n                if (options[i].completion == selectedValue) {\n                    selected = i;\n                    break;\n                }\n        }\n        return new CompletionDialog(options, makeAttrs(id, selected), {\n            pos: active.reduce((a, b) => b.hasResult() ? Math.min(a, b.from) : a, 1e8),\n            create: createTooltip,\n            above: conf.aboveCursor,\n        }, prev ? prev.timestamp : Date.now(), selected, false);\n    }\n    map(changes) {\n        return new CompletionDialog(this.options, this.attrs, Object.assign(Object.assign({}, this.tooltip), { pos: changes.mapPos(this.tooltip.pos) }), this.timestamp, this.selected, this.disabled);\n    }\n    setDisabled() {\n        return new CompletionDialog(this.options, this.attrs, this.tooltip, this.timestamp, this.selected, true);\n    }\n}\nclass CompletionState {\n    constructor(active, id, open) {\n        this.active = active;\n        this.id = id;\n        this.open = open;\n    }\n    static start() {\n        return new CompletionState(none, \"cm-ac-\" + Math.floor(Math.random() * 2e6).toString(36), null);\n    }\n    update(tr) {\n        let { state } = tr, conf = state.facet(completionConfig);\n        let sources = conf.override ||\n            state.languageDataAt(\"autocomplete\", cur(state)).map(asSource);\n        let active = sources.map(source => {\n            let value = this.active.find(s => s.source == source) ||\n                new ActiveSource(source, this.active.some(a => a.state != 0 /* State.Inactive */) ? 1 /* State.Pending */ : 0 /* State.Inactive */);\n            return value.update(tr, conf);\n        });\n        if (active.length == this.active.length && active.every((a, i) => a == this.active[i]))\n            active = this.active;\n        let open = this.open, didSet = tr.effects.some(e => e.is(setActiveEffect));\n        if (open && tr.docChanged)\n            open = open.map(tr.changes);\n        if (tr.selection || active.some(a => a.hasResult() && tr.changes.touchesRange(a.from, a.to)) ||\n            !sameResults(active, this.active) || didSet)\n            open = CompletionDialog.build(active, state, this.id, open, conf, didSet);\n        else if (open && open.disabled && !active.some(a => a.isPending))\n            open = null;\n        if (!open && active.every(a => !a.isPending) && active.some(a => a.hasResult()))\n            active = active.map(a => a.hasResult() ? new ActiveSource(a.source, 0 /* State.Inactive */) : a);\n        for (let effect of tr.effects)\n            if (effect.is(setSelectedEffect))\n                open = open && open.setSelected(effect.value, this.id);\n        return active == this.active && open == this.open ? this : new CompletionState(active, this.id, open);\n    }\n    get tooltip() { return this.open ? this.open.tooltip : null; }\n    get attrs() { return this.open ? this.open.attrs : this.active.length ? baseAttrs : noAttrs; }\n}\nfunction sameResults(a, b) {\n    if (a == b)\n        return true;\n    for (let iA = 0, iB = 0;;) {\n        while (iA < a.length && !a[iA].hasResult())\n            iA++;\n        while (iB < b.length && !b[iB].hasResult())\n            iB++;\n        let endA = iA == a.length, endB = iB == b.length;\n        if (endA || endB)\n            return endA == endB;\n        if (a[iA++].result != b[iB++].result)\n            return false;\n    }\n}\nconst baseAttrs = {\n    \"aria-autocomplete\": \"list\"\n};\nconst noAttrs = {};\nfunction makeAttrs(id, selected) {\n    let result = {\n        \"aria-autocomplete\": \"list\",\n        \"aria-haspopup\": \"listbox\",\n        \"aria-controls\": id\n    };\n    if (selected > -1)\n        result[\"aria-activedescendant\"] = id + \"-\" + selected;\n    return result;\n}\nconst none = [];\nfunction getUpdateType(tr, conf) {\n    if (tr.isUserEvent(\"input.complete\")) {\n        let completion = tr.annotation(pickedCompletion);\n        if (completion && conf.activateOnCompletion(completion))\n            return 4 /* UpdateType.Activate */ | 8 /* UpdateType.Reset */;\n    }\n    let typing = tr.isUserEvent(\"input.type\");\n    return typing && conf.activateOnTyping ? 4 /* UpdateType.Activate */ | 1 /* UpdateType.Typing */\n        : typing ? 1 /* UpdateType.Typing */\n            : tr.isUserEvent(\"delete.backward\") ? 2 /* UpdateType.Backspacing */\n                : tr.selection ? 8 /* UpdateType.Reset */\n                    : tr.docChanged ? 16 /* UpdateType.ResetIfTouching */ : 0 /* UpdateType.None */;\n}\nclass ActiveSource {\n    constructor(source, state, explicit = false) {\n        this.source = source;\n        this.state = state;\n        this.explicit = explicit;\n    }\n    hasResult() { return false; }\n    get isPending() { return this.state == 1 /* State.Pending */; }\n    update(tr, conf) {\n        let type = getUpdateType(tr, conf), value = this;\n        if ((type & 8 /* UpdateType.Reset */) || (type & 16 /* UpdateType.ResetIfTouching */) && this.touches(tr))\n            value = new ActiveSource(value.source, 0 /* State.Inactive */);\n        if ((type & 4 /* UpdateType.Activate */) && value.state == 0 /* State.Inactive */)\n            value = new ActiveSource(this.source, 1 /* State.Pending */);\n        value = value.updateFor(tr, type);\n        for (let effect of tr.effects) {\n            if (effect.is(startCompletionEffect))\n                value = new ActiveSource(value.source, 1 /* State.Pending */, effect.value);\n            else if (effect.is(closeCompletionEffect))\n                value = new ActiveSource(value.source, 0 /* State.Inactive */);\n            else if (effect.is(setActiveEffect))\n                for (let active of effect.value)\n                    if (active.source == value.source)\n                        value = active;\n        }\n        return value;\n    }\n    updateFor(tr, type) { return this.map(tr.changes); }\n    map(changes) { return this; }\n    touches(tr) {\n        return tr.changes.touchesRange(cur(tr.state));\n    }\n}\nclass ActiveResult extends ActiveSource {\n    constructor(source, explicit, limit, result, from, to) {\n        super(source, 3 /* State.Result */, explicit);\n        this.limit = limit;\n        this.result = result;\n        this.from = from;\n        this.to = to;\n    }\n    hasResult() { return true; }\n    updateFor(tr, type) {\n        var _a;\n        if (!(type & 3 /* UpdateType.SimpleInteraction */))\n            return this.map(tr.changes);\n        let result = this.result;\n        if (result.map && !tr.changes.empty)\n            result = result.map(result, tr.changes);\n        let from = tr.changes.mapPos(this.from), to = tr.changes.mapPos(this.to, 1);\n        let pos = cur(tr.state);\n        if (pos > to || !result ||\n            (type & 2 /* UpdateType.Backspacing */) && (cur(tr.startState) == this.from || pos < this.limit))\n            return new ActiveSource(this.source, type & 4 /* UpdateType.Activate */ ? 1 /* State.Pending */ : 0 /* State.Inactive */);\n        let limit = tr.changes.mapPos(this.limit);\n        if (checkValid(result.validFor, tr.state, from, to))\n            return new ActiveResult(this.source, this.explicit, limit, result, from, to);\n        if (result.update &&\n            (result = result.update(result, from, to, new CompletionContext(tr.state, pos, false))))\n            return new ActiveResult(this.source, this.explicit, limit, result, result.from, (_a = result.to) !== null && _a !== void 0 ? _a : cur(tr.state));\n        return new ActiveSource(this.source, 1 /* State.Pending */, this.explicit);\n    }\n    map(mapping) {\n        if (mapping.empty)\n            return this;\n        let result = this.result.map ? this.result.map(this.result, mapping) : this.result;\n        if (!result)\n            return new ActiveSource(this.source, 0 /* State.Inactive */);\n        return new ActiveResult(this.source, this.explicit, mapping.mapPos(this.limit), this.result, mapping.mapPos(this.from), mapping.mapPos(this.to, 1));\n    }\n    touches(tr) {\n        return tr.changes.touchesRange(this.from, this.to);\n    }\n}\nfunction checkValid(validFor, state, from, to) {\n    if (!validFor)\n        return false;\n    let text = state.sliceDoc(from, to);\n    return typeof validFor == \"function\" ? validFor(text, from, to, state) : ensureAnchor(validFor, true).test(text);\n}\nconst setActiveEffect = /*@__PURE__*/StateEffect.define({\n    map(sources, mapping) { return sources.map(s => s.map(mapping)); }\n});\nconst setSelectedEffect = /*@__PURE__*/StateEffect.define();\nconst completionState = /*@__PURE__*/StateField.define({\n    create() { return CompletionState.start(); },\n    update(value, tr) { return value.update(tr); },\n    provide: f => [\n        showTooltip.from(f, val => val.tooltip),\n        EditorView.contentAttributes.from(f, state => state.attrs)\n    ]\n});\nfunction applyCompletion(view, option) {\n    const apply = option.completion.apply || option.completion.label;\n    let result = view.state.field(completionState).active.find(a => a.source == option.source);\n    if (!(result instanceof ActiveResult))\n        return false;\n    if (typeof apply == \"string\")\n        view.dispatch(Object.assign(Object.assign({}, insertCompletionText(view.state, apply, result.from, result.to)), { annotations: pickedCompletion.of(option.completion) }));\n    else\n        apply(view, option.completion, result.from, result.to);\n    return true;\n}\nconst createTooltip = /*@__PURE__*/completionTooltip(completionState, applyCompletion);\n\n/**\nReturns a command that moves the completion selection forward or\nbackward by the given amount.\n*/\nfunction moveCompletionSelection(forward, by = \"option\") {\n    return (view) => {\n        let cState = view.state.field(completionState, false);\n        if (!cState || !cState.open || cState.open.disabled ||\n            Date.now() - cState.open.timestamp < view.state.facet(completionConfig).interactionDelay)\n            return false;\n        let step = 1, tooltip;\n        if (by == \"page\" && (tooltip = getTooltip(view, cState.open.tooltip)))\n            step = Math.max(2, Math.floor(tooltip.dom.offsetHeight /\n                tooltip.dom.querySelector(\"li\").offsetHeight) - 1);\n        let { length } = cState.open.options;\n        let selected = cState.open.selected > -1 ? cState.open.selected + step * (forward ? 1 : -1) : forward ? 0 : length - 1;\n        if (selected < 0)\n            selected = by == \"page\" ? 0 : length - 1;\n        else if (selected >= length)\n            selected = by == \"page\" ? length - 1 : 0;\n        view.dispatch({ effects: setSelectedEffect.of(selected) });\n        return true;\n    };\n}\n/**\nAccept the current completion.\n*/\nconst acceptCompletion = (view) => {\n    let cState = view.state.field(completionState, false);\n    if (view.state.readOnly || !cState || !cState.open || cState.open.selected < 0 || cState.open.disabled ||\n        Date.now() - cState.open.timestamp < view.state.facet(completionConfig).interactionDelay)\n        return false;\n    return applyCompletion(view, cState.open.options[cState.open.selected]);\n};\n/**\nExplicitly start autocompletion.\n*/\nconst startCompletion = (view) => {\n    let cState = view.state.field(completionState, false);\n    if (!cState)\n        return false;\n    view.dispatch({ effects: startCompletionEffect.of(true) });\n    return true;\n};\n/**\nClose the currently active completion.\n*/\nconst closeCompletion = (view) => {\n    let cState = view.state.field(completionState, false);\n    if (!cState || !cState.active.some(a => a.state != 0 /* State.Inactive */))\n        return false;\n    view.dispatch({ effects: closeCompletionEffect.of(null) });\n    return true;\n};\nclass RunningQuery {\n    constructor(active, context) {\n        this.active = active;\n        this.context = context;\n        this.time = Date.now();\n        this.updates = [];\n        // Note that 'undefined' means 'not done yet', whereas 'null' means\n        // 'query returned null'.\n        this.done = undefined;\n    }\n}\nconst MaxUpdateCount = 50, MinAbortTime = 1000;\nconst completionPlugin = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.view = view;\n        this.debounceUpdate = -1;\n        this.running = [];\n        this.debounceAccept = -1;\n        this.pendingStart = false;\n        this.composing = 0 /* CompositionState.None */;\n        for (let active of view.state.field(completionState).active)\n            if (active.isPending)\n                this.startQuery(active);\n    }\n    update(update) {\n        let cState = update.state.field(completionState);\n        let conf = update.state.facet(completionConfig);\n        if (!update.selectionSet && !update.docChanged && update.startState.field(completionState) == cState)\n            return;\n        let doesReset = update.transactions.some(tr => {\n            let type = getUpdateType(tr, conf);\n            return (type & 8 /* UpdateType.Reset */) || (tr.selection || tr.docChanged) && !(type & 3 /* UpdateType.SimpleInteraction */);\n        });\n        for (let i = 0; i < this.running.length; i++) {\n            let query = this.running[i];\n            if (doesReset ||\n                query.context.abortOnDocChange && update.docChanged ||\n                query.updates.length + update.transactions.length > MaxUpdateCount && Date.now() - query.time > MinAbortTime) {\n                for (let handler of query.context.abortListeners) {\n                    try {\n                        handler();\n                    }\n                    catch (e) {\n                        logException(this.view.state, e);\n                    }\n                }\n                query.context.abortListeners = null;\n                this.running.splice(i--, 1);\n            }\n            else {\n                query.updates.push(...update.transactions);\n            }\n        }\n        if (this.debounceUpdate > -1)\n            clearTimeout(this.debounceUpdate);\n        if (update.transactions.some(tr => tr.effects.some(e => e.is(startCompletionEffect))))\n            this.pendingStart = true;\n        let delay = this.pendingStart ? 50 : conf.activateOnTypingDelay;\n        this.debounceUpdate = cState.active.some(a => a.isPending && !this.running.some(q => q.active.source == a.source))\n            ? setTimeout(() => this.startUpdate(), delay) : -1;\n        if (this.composing != 0 /* CompositionState.None */)\n            for (let tr of update.transactions) {\n                if (tr.isUserEvent(\"input.type\"))\n                    this.composing = 2 /* CompositionState.Changed */;\n                else if (this.composing == 2 /* CompositionState.Changed */ && tr.selection)\n                    this.composing = 3 /* CompositionState.ChangedAndMoved */;\n            }\n    }\n    startUpdate() {\n        this.debounceUpdate = -1;\n        this.pendingStart = false;\n        let { state } = this.view, cState = state.field(completionState);\n        for (let active of cState.active) {\n            if (active.isPending && !this.running.some(r => r.active.source == active.source))\n                this.startQuery(active);\n        }\n        if (this.running.length && cState.open && cState.open.disabled)\n            this.debounceAccept = setTimeout(() => this.accept(), this.view.state.facet(completionConfig).updateSyncTime);\n    }\n    startQuery(active) {\n        let { state } = this.view, pos = cur(state);\n        let context = new CompletionContext(state, pos, active.explicit, this.view);\n        let pending = new RunningQuery(active, context);\n        this.running.push(pending);\n        Promise.resolve(active.source(context)).then(result => {\n            if (!pending.context.aborted) {\n                pending.done = result || null;\n                this.scheduleAccept();\n            }\n        }, err => {\n            this.view.dispatch({ effects: closeCompletionEffect.of(null) });\n            logException(this.view.state, err);\n        });\n    }\n    scheduleAccept() {\n        if (this.running.every(q => q.done !== undefined))\n            this.accept();\n        else if (this.debounceAccept < 0)\n            this.debounceAccept = setTimeout(() => this.accept(), this.view.state.facet(completionConfig).updateSyncTime);\n    }\n    // For each finished query in this.running, try to create a result\n    // or, if appropriate, restart the query.\n    accept() {\n        var _a;\n        if (this.debounceAccept > -1)\n            clearTimeout(this.debounceAccept);\n        this.debounceAccept = -1;\n        let updated = [];\n        let conf = this.view.state.facet(completionConfig), cState = this.view.state.field(completionState);\n        for (let i = 0; i < this.running.length; i++) {\n            let query = this.running[i];\n            if (query.done === undefined)\n                continue;\n            this.running.splice(i--, 1);\n            if (query.done) {\n                let pos = cur(query.updates.length ? query.updates[0].startState : this.view.state);\n                let limit = Math.min(pos, query.done.from + (query.active.explicit ? 0 : 1));\n                let active = new ActiveResult(query.active.source, query.active.explicit, limit, query.done, query.done.from, (_a = query.done.to) !== null && _a !== void 0 ? _a : pos);\n                // Replay the transactions that happened since the start of\n                // the request and see if that preserves the result\n                for (let tr of query.updates)\n                    active = active.update(tr, conf);\n                if (active.hasResult()) {\n                    updated.push(active);\n                    continue;\n                }\n            }\n            let current = cState.active.find(a => a.source == query.active.source);\n            if (current && current.isPending) {\n                if (query.done == null) {\n                    // Explicitly failed. Should clear the pending status if it\n                    // hasn't been re-set in the meantime.\n                    let active = new ActiveSource(query.active.source, 0 /* State.Inactive */);\n                    for (let tr of query.updates)\n                        active = active.update(tr, conf);\n                    if (!active.isPending)\n                        updated.push(active);\n                }\n                else {\n                    // Cleared by subsequent transactions. Restart.\n                    this.startQuery(current);\n                }\n            }\n        }\n        if (updated.length || cState.open && cState.open.disabled)\n            this.view.dispatch({ effects: setActiveEffect.of(updated) });\n    }\n}, {\n    eventHandlers: {\n        blur(event) {\n            let state = this.view.state.field(completionState, false);\n            if (state && state.tooltip && this.view.state.facet(completionConfig).closeOnBlur) {\n                let dialog = state.open && getTooltip(this.view, state.open.tooltip);\n                if (!dialog || !dialog.dom.contains(event.relatedTarget))\n                    setTimeout(() => this.view.dispatch({ effects: closeCompletionEffect.of(null) }), 10);\n            }\n        },\n        compositionstart() {\n            this.composing = 1 /* CompositionState.Started */;\n        },\n        compositionend() {\n            if (this.composing == 3 /* CompositionState.ChangedAndMoved */) {\n                // Safari fires compositionend events synchronously, possibly\n                // from inside an update, so dispatch asynchronously to avoid reentrancy\n                setTimeout(() => this.view.dispatch({ effects: startCompletionEffect.of(false) }), 20);\n            }\n            this.composing = 0 /* CompositionState.None */;\n        }\n    }\n});\nconst windows = typeof navigator == \"object\" && /*@__PURE__*//Win/.test(navigator.platform);\nconst commitCharacters = /*@__PURE__*/Prec.highest(/*@__PURE__*/EditorView.domEventHandlers({\n    keydown(event, view) {\n        let field = view.state.field(completionState, false);\n        if (!field || !field.open || field.open.disabled || field.open.selected < 0 ||\n            event.key.length > 1 || event.ctrlKey && !(windows && event.altKey) || event.metaKey)\n            return false;\n        let option = field.open.options[field.open.selected];\n        let result = field.active.find(a => a.source == option.source);\n        let commitChars = option.completion.commitCharacters || result.result.commitCharacters;\n        if (commitChars && commitChars.indexOf(event.key) > -1)\n            applyCompletion(view, option);\n        return false;\n    }\n}));\n\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-tooltip.cm-tooltip-autocomplete\": {\n        \"& > ul\": {\n            fontFamily: \"monospace\",\n            whiteSpace: \"nowrap\",\n            overflow: \"hidden auto\",\n            maxWidth_fallback: \"700px\",\n            maxWidth: \"min(700px, 95vw)\",\n            minWidth: \"250px\",\n            maxHeight: \"10em\",\n            height: \"100%\",\n            listStyle: \"none\",\n            margin: 0,\n            padding: 0,\n            \"& > li, & > completion-section\": {\n                padding: \"1px 3px\",\n                lineHeight: 1.2\n            },\n            \"& > li\": {\n                overflowX: \"hidden\",\n                textOverflow: \"ellipsis\",\n                cursor: \"pointer\"\n            },\n            \"& > completion-section\": {\n                display: \"list-item\",\n                borderBottom: \"1px solid silver\",\n                paddingLeft: \"0.5em\",\n                opacity: 0.7\n            }\n        }\n    },\n    \"&light .cm-tooltip-autocomplete ul li[aria-selected]\": {\n        background: \"#17c\",\n        color: \"white\",\n    },\n    \"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]\": {\n        background: \"#777\",\n    },\n    \"&dark .cm-tooltip-autocomplete ul li[aria-selected]\": {\n        background: \"#347\",\n        color: \"white\",\n    },\n    \"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]\": {\n        background: \"#444\",\n    },\n    \".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after\": {\n        content: '\"···\"',\n        opacity: 0.5,\n        display: \"block\",\n        textAlign: \"center\"\n    },\n    \".cm-tooltip.cm-completionInfo\": {\n        position: \"absolute\",\n        padding: \"3px 9px\",\n        width: \"max-content\",\n        maxWidth: `${400 /* Info.Width */}px`,\n        boxSizing: \"border-box\",\n        whiteSpace: \"pre-line\"\n    },\n    \".cm-completionInfo.cm-completionInfo-left\": { right: \"100%\" },\n    \".cm-completionInfo.cm-completionInfo-right\": { left: \"100%\" },\n    \".cm-completionInfo.cm-completionInfo-left-narrow\": { right: `${30 /* Info.Margin */}px` },\n    \".cm-completionInfo.cm-completionInfo-right-narrow\": { left: `${30 /* Info.Margin */}px` },\n    \"&light .cm-snippetField\": { backgroundColor: \"#00000022\" },\n    \"&dark .cm-snippetField\": { backgroundColor: \"#ffffff22\" },\n    \".cm-snippetFieldPosition\": {\n        verticalAlign: \"text-top\",\n        width: 0,\n        height: \"1.15em\",\n        display: \"inline-block\",\n        margin: \"0 -0.7px -.7em\",\n        borderLeft: \"1.4px dotted #888\"\n    },\n    \".cm-completionMatchedText\": {\n        textDecoration: \"underline\"\n    },\n    \".cm-completionDetail\": {\n        marginLeft: \"0.5em\",\n        fontStyle: \"italic\"\n    },\n    \".cm-completionIcon\": {\n        fontSize: \"90%\",\n        width: \".8em\",\n        display: \"inline-block\",\n        textAlign: \"center\",\n        paddingRight: \".6em\",\n        opacity: \"0.6\",\n        boxSizing: \"content-box\"\n    },\n    \".cm-completionIcon-function, .cm-completionIcon-method\": {\n        \"&:after\": { content: \"'ƒ'\" }\n    },\n    \".cm-completionIcon-class\": {\n        \"&:after\": { content: \"'○'\" }\n    },\n    \".cm-completionIcon-interface\": {\n        \"&:after\": { content: \"'◌'\" }\n    },\n    \".cm-completionIcon-variable\": {\n        \"&:after\": { content: \"'𝑥'\" }\n    },\n    \".cm-completionIcon-constant\": {\n        \"&:after\": { content: \"'𝐶'\" }\n    },\n    \".cm-completionIcon-type\": {\n        \"&:after\": { content: \"'𝑡'\" }\n    },\n    \".cm-completionIcon-enum\": {\n        \"&:after\": { content: \"'∪'\" }\n    },\n    \".cm-completionIcon-property\": {\n        \"&:after\": { content: \"'□'\" }\n    },\n    \".cm-completionIcon-keyword\": {\n        \"&:after\": { content: \"'🔑\\uFE0E'\" } // Disable emoji rendering\n    },\n    \".cm-completionIcon-namespace\": {\n        \"&:after\": { content: \"'▢'\" }\n    },\n    \".cm-completionIcon-text\": {\n        \"&:after\": { content: \"'abc'\", fontSize: \"50%\", verticalAlign: \"middle\" }\n    }\n});\n\nclass FieldPos {\n    constructor(field, line, from, to) {\n        this.field = field;\n        this.line = line;\n        this.from = from;\n        this.to = to;\n    }\n}\nclass FieldRange {\n    constructor(field, from, to) {\n        this.field = field;\n        this.from = from;\n        this.to = to;\n    }\n    map(changes) {\n        let from = changes.mapPos(this.from, -1, MapMode.TrackDel);\n        let to = changes.mapPos(this.to, 1, MapMode.TrackDel);\n        return from == null || to == null ? null : new FieldRange(this.field, from, to);\n    }\n}\nclass Snippet {\n    constructor(lines, fieldPositions) {\n        this.lines = lines;\n        this.fieldPositions = fieldPositions;\n    }\n    instantiate(state, pos) {\n        let text = [], lineStart = [pos];\n        let lineObj = state.doc.lineAt(pos), baseIndent = /^\\s*/.exec(lineObj.text)[0];\n        for (let line of this.lines) {\n            if (text.length) {\n                let indent = baseIndent, tabs = /^\\t*/.exec(line)[0].length;\n                for (let i = 0; i < tabs; i++)\n                    indent += state.facet(indentUnit);\n                lineStart.push(pos + indent.length - tabs);\n                line = indent + line.slice(tabs);\n            }\n            text.push(line);\n            pos += line.length + 1;\n        }\n        let ranges = this.fieldPositions.map(pos => new FieldRange(pos.field, lineStart[pos.line] + pos.from, lineStart[pos.line] + pos.to));\n        return { text, ranges };\n    }\n    static parse(template) {\n        let fields = [];\n        let lines = [], positions = [], m;\n        for (let line of template.split(/\\r\\n?|\\n/)) {\n            while (m = /[#$]\\{(?:(\\d+)(?::([^}]*))?|((?:\\\\[{}]|[^}])*))\\}/.exec(line)) {\n                let seq = m[1] ? +m[1] : null, rawName = m[2] || m[3] || \"\", found = -1;\n                let name = rawName.replace(/\\\\[{}]/g, m => m[1]);\n                for (let i = 0; i < fields.length; i++) {\n                    if (seq != null ? fields[i].seq == seq : name ? fields[i].name == name : false)\n                        found = i;\n                }\n                if (found < 0) {\n                    let i = 0;\n                    while (i < fields.length && (seq == null || (fields[i].seq != null && fields[i].seq < seq)))\n                        i++;\n                    fields.splice(i, 0, { seq, name });\n                    found = i;\n                    for (let pos of positions)\n                        if (pos.field >= found)\n                            pos.field++;\n                }\n                positions.push(new FieldPos(found, lines.length, m.index, m.index + name.length));\n                line = line.slice(0, m.index) + rawName + line.slice(m.index + m[0].length);\n            }\n            line = line.replace(/\\\\([{}])/g, (_, brace, index) => {\n                for (let pos of positions)\n                    if (pos.line == lines.length && pos.from > index) {\n                        pos.from--;\n                        pos.to--;\n                    }\n                return brace;\n            });\n            lines.push(line);\n        }\n        return new Snippet(lines, positions);\n    }\n}\nlet fieldMarker = /*@__PURE__*/Decoration.widget({ widget: /*@__PURE__*/new class extends WidgetType {\n        toDOM() {\n            let span = document.createElement(\"span\");\n            span.className = \"cm-snippetFieldPosition\";\n            return span;\n        }\n        ignoreEvent() { return false; }\n    } });\nlet fieldRange = /*@__PURE__*/Decoration.mark({ class: \"cm-snippetField\" });\nclass ActiveSnippet {\n    constructor(ranges, active) {\n        this.ranges = ranges;\n        this.active = active;\n        this.deco = Decoration.set(ranges.map(r => (r.from == r.to ? fieldMarker : fieldRange).range(r.from, r.to)));\n    }\n    map(changes) {\n        let ranges = [];\n        for (let r of this.ranges) {\n            let mapped = r.map(changes);\n            if (!mapped)\n                return null;\n            ranges.push(mapped);\n        }\n        return new ActiveSnippet(ranges, this.active);\n    }\n    selectionInsideField(sel) {\n        return sel.ranges.every(range => this.ranges.some(r => r.field == this.active && r.from <= range.from && r.to >= range.to));\n    }\n}\nconst setActive = /*@__PURE__*/StateEffect.define({\n    map(value, changes) { return value && value.map(changes); }\n});\nconst moveToField = /*@__PURE__*/StateEffect.define();\nconst snippetState = /*@__PURE__*/StateField.define({\n    create() { return null; },\n    update(value, tr) {\n        for (let effect of tr.effects) {\n            if (effect.is(setActive))\n                return effect.value;\n            if (effect.is(moveToField) && value)\n                return new ActiveSnippet(value.ranges, effect.value);\n        }\n        if (value && tr.docChanged)\n            value = value.map(tr.changes);\n        if (value && tr.selection && !value.selectionInsideField(tr.selection))\n            value = null;\n        return value;\n    },\n    provide: f => EditorView.decorations.from(f, val => val ? val.deco : Decoration.none)\n});\nfunction fieldSelection(ranges, field) {\n    return EditorSelection.create(ranges.filter(r => r.field == field).map(r => EditorSelection.range(r.from, r.to)));\n}\n/**\nConvert a snippet template to a function that can\n[apply](https://codemirror.net/6/docs/ref/#autocomplete.Completion.apply) it. Snippets are written\nusing syntax like this:\n\n    \"for (let ${index} = 0; ${index} < ${end}; ${index}++) {\\n\\t${}\\n}\"\n\nEach `${}` placeholder (you may also use `#{}`) indicates a field\nthat the user can fill in. Its name, if any, will be the default\ncontent for the field.\n\nWhen the snippet is activated by calling the returned function,\nthe code is inserted at the given position. Newlines in the\ntemplate are indented by the indentation of the start line, plus\none [indent unit](https://codemirror.net/6/docs/ref/#language.indentUnit) per tab character after\nthe newline.\n\nOn activation, (all instances of) the first field are selected.\nThe user can move between fields with Tab and Shift-Tab as long as\nthe fields are active. Moving to the last field or moving the\ncursor out of the current field deactivates the fields.\n\nThe order of fields defaults to textual order, but you can add\nnumbers to placeholders (`${1}` or `${1:defaultText}`) to provide\na custom order.\n\nTo include a literal `{` or `}` in your template, put a backslash\nin front of it. This will be removed and the brace will not be\ninterpreted as indicating a placeholder.\n*/\nfunction snippet(template) {\n    let snippet = Snippet.parse(template);\n    return (editor, completion, from, to) => {\n        let { text, ranges } = snippet.instantiate(editor.state, from);\n        let { main } = editor.state.selection;\n        let spec = {\n            changes: { from, to: to == main.from ? main.to : to, insert: Text.of(text) },\n            scrollIntoView: true,\n            annotations: completion ? [pickedCompletion.of(completion), Transaction.userEvent.of(\"input.complete\")] : undefined\n        };\n        if (ranges.length)\n            spec.selection = fieldSelection(ranges, 0);\n        if (ranges.some(r => r.field > 0)) {\n            let active = new ActiveSnippet(ranges, 0);\n            let effects = spec.effects = [setActive.of(active)];\n            if (editor.state.field(snippetState, false) === undefined)\n                effects.push(StateEffect.appendConfig.of([snippetState, addSnippetKeymap, snippetPointerHandler, baseTheme]));\n        }\n        editor.dispatch(editor.state.update(spec));\n    };\n}\nfunction moveField(dir) {\n    return ({ state, dispatch }) => {\n        let active = state.field(snippetState, false);\n        if (!active || dir < 0 && active.active == 0)\n            return false;\n        let next = active.active + dir, last = dir > 0 && !active.ranges.some(r => r.field == next + dir);\n        dispatch(state.update({\n            selection: fieldSelection(active.ranges, next),\n            effects: setActive.of(last ? null : new ActiveSnippet(active.ranges, next)),\n            scrollIntoView: true\n        }));\n        return true;\n    };\n}\n/**\nA command that clears the active snippet, if any.\n*/\nconst clearSnippet = ({ state, dispatch }) => {\n    let active = state.field(snippetState, false);\n    if (!active)\n        return false;\n    dispatch(state.update({ effects: setActive.of(null) }));\n    return true;\n};\n/**\nMove to the next snippet field, if available.\n*/\nconst nextSnippetField = /*@__PURE__*/moveField(1);\n/**\nMove to the previous snippet field, if available.\n*/\nconst prevSnippetField = /*@__PURE__*/moveField(-1);\n/**\nCheck if there is an active snippet with a next field for\n`nextSnippetField` to move to.\n*/\nfunction hasNextSnippetField(state) {\n    let active = state.field(snippetState, false);\n    return !!(active && active.ranges.some(r => r.field == active.active + 1));\n}\n/**\nReturns true if there is an active snippet and a previous field\nfor `prevSnippetField` to move to.\n*/\nfunction hasPrevSnippetField(state) {\n    let active = state.field(snippetState, false);\n    return !!(active && active.active > 0);\n}\nconst defaultSnippetKeymap = [\n    { key: \"Tab\", run: nextSnippetField, shift: prevSnippetField },\n    { key: \"Escape\", run: clearSnippet }\n];\n/**\nA facet that can be used to configure the key bindings used by\nsnippets. The default binds Tab to\n[`nextSnippetField`](https://codemirror.net/6/docs/ref/#autocomplete.nextSnippetField), Shift-Tab to\n[`prevSnippetField`](https://codemirror.net/6/docs/ref/#autocomplete.prevSnippetField), and Escape\nto [`clearSnippet`](https://codemirror.net/6/docs/ref/#autocomplete.clearSnippet).\n*/\nconst snippetKeymap = /*@__PURE__*/Facet.define({\n    combine(maps) { return maps.length ? maps[0] : defaultSnippetKeymap; }\n});\nconst addSnippetKeymap = /*@__PURE__*/Prec.highest(/*@__PURE__*/keymap.compute([snippetKeymap], state => state.facet(snippetKeymap)));\n/**\nCreate a completion from a snippet. Returns an object with the\nproperties from `completion`, plus an `apply` function that\napplies the snippet.\n*/\nfunction snippetCompletion(template, completion) {\n    return Object.assign(Object.assign({}, completion), { apply: snippet(template) });\n}\nconst snippetPointerHandler = /*@__PURE__*/EditorView.domEventHandlers({\n    mousedown(event, view) {\n        let active = view.state.field(snippetState, false), pos;\n        if (!active || (pos = view.posAtCoords({ x: event.clientX, y: event.clientY })) == null)\n            return false;\n        let match = active.ranges.find(r => r.from <= pos && r.to >= pos);\n        if (!match || match.field == active.active)\n            return false;\n        view.dispatch({\n            selection: fieldSelection(active.ranges, match.field),\n            effects: setActive.of(active.ranges.some(r => r.field > match.field)\n                ? new ActiveSnippet(active.ranges, match.field) : null),\n            scrollIntoView: true\n        });\n        return true;\n    }\n});\n\nfunction wordRE(wordChars) {\n    let escaped = wordChars.replace(/[\\]\\-\\\\]/g, \"\\\\$&\");\n    try {\n        return new RegExp(`[\\\\p{Alphabetic}\\\\p{Number}_${escaped}]+`, \"ug\");\n    }\n    catch (_a) {\n        return new RegExp(`[\\w${escaped}]`, \"g\");\n    }\n}\nfunction mapRE(re, f) {\n    return new RegExp(f(re.source), re.unicode ? \"u\" : \"\");\n}\nconst wordCaches = /*@__PURE__*/Object.create(null);\nfunction wordCache(wordChars) {\n    return wordCaches[wordChars] || (wordCaches[wordChars] = new WeakMap);\n}\nfunction storeWords(doc, wordRE, result, seen, ignoreAt) {\n    for (let lines = doc.iterLines(), pos = 0; !lines.next().done;) {\n        let { value } = lines, m;\n        wordRE.lastIndex = 0;\n        while (m = wordRE.exec(value)) {\n            if (!seen[m[0]] && pos + m.index != ignoreAt) {\n                result.push({ type: \"text\", label: m[0] });\n                seen[m[0]] = true;\n                if (result.length >= 2000 /* C.MaxList */)\n                    return;\n            }\n        }\n        pos += value.length + 1;\n    }\n}\nfunction collectWords(doc, cache, wordRE, to, ignoreAt) {\n    let big = doc.length >= 1000 /* C.MinCacheLen */;\n    let cached = big && cache.get(doc);\n    if (cached)\n        return cached;\n    let result = [], seen = Object.create(null);\n    if (doc.children) {\n        let pos = 0;\n        for (let ch of doc.children) {\n            if (ch.length >= 1000 /* C.MinCacheLen */) {\n                for (let c of collectWords(ch, cache, wordRE, to - pos, ignoreAt - pos)) {\n                    if (!seen[c.label]) {\n                        seen[c.label] = true;\n                        result.push(c);\n                    }\n                }\n            }\n            else {\n                storeWords(ch, wordRE, result, seen, ignoreAt - pos);\n            }\n            pos += ch.length + 1;\n        }\n    }\n    else {\n        storeWords(doc, wordRE, result, seen, ignoreAt);\n    }\n    if (big && result.length < 2000 /* C.MaxList */)\n        cache.set(doc, result);\n    return result;\n}\n/**\nA completion source that will scan the document for words (using a\n[character categorizer](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer)), and\nreturn those as completions.\n*/\nconst completeAnyWord = context => {\n    let wordChars = context.state.languageDataAt(\"wordChars\", context.pos).join(\"\");\n    let re = wordRE(wordChars);\n    let token = context.matchBefore(mapRE(re, s => s + \"$\"));\n    if (!token && !context.explicit)\n        return null;\n    let from = token ? token.from : context.pos;\n    let options = collectWords(context.state.doc, wordCache(wordChars), re, 50000 /* C.Range */, from);\n    return { from, options, validFor: mapRE(re, s => \"^\" + s) };\n};\n\nconst defaults = {\n    brackets: [\"(\", \"[\", \"{\", \"'\", '\"'],\n    before: \")]}:;>\",\n    stringPrefixes: []\n};\nconst closeBracketEffect = /*@__PURE__*/StateEffect.define({\n    map(value, mapping) {\n        let mapped = mapping.mapPos(value, -1, MapMode.TrackAfter);\n        return mapped == null ? undefined : mapped;\n    }\n});\nconst closedBracket = /*@__PURE__*/new class extends RangeValue {\n};\nclosedBracket.startSide = 1;\nclosedBracket.endSide = -1;\nconst bracketState = /*@__PURE__*/StateField.define({\n    create() { return RangeSet.empty; },\n    update(value, tr) {\n        value = value.map(tr.changes);\n        if (tr.selection) {\n            let line = tr.state.doc.lineAt(tr.selection.main.head);\n            value = value.update({ filter: from => from >= line.from && from <= line.to });\n        }\n        for (let effect of tr.effects)\n            if (effect.is(closeBracketEffect))\n                value = value.update({ add: [closedBracket.range(effect.value, effect.value + 1)] });\n        return value;\n    }\n});\n/**\nExtension to enable bracket-closing behavior. When a closeable\nbracket is typed, its closing bracket is immediately inserted\nafter the cursor. When closing a bracket directly in front of a\nclosing bracket inserted by the extension, the cursor moves over\nthat bracket.\n*/\nfunction closeBrackets() {\n    return [inputHandler, bracketState];\n}\nconst definedClosing = \"()[]{}<>«»»«［］｛｝\";\nfunction closing(ch) {\n    for (let i = 0; i < definedClosing.length; i += 2)\n        if (definedClosing.charCodeAt(i) == ch)\n            return definedClosing.charAt(i + 1);\n    return fromCodePoint(ch < 128 ? ch : ch + 1);\n}\nfunction config(state, pos) {\n    return state.languageDataAt(\"closeBrackets\", pos)[0] || defaults;\n}\nconst android = typeof navigator == \"object\" && /*@__PURE__*//Android\\b/.test(navigator.userAgent);\nconst inputHandler = /*@__PURE__*/EditorView.inputHandler.of((view, from, to, insert) => {\n    if ((android ? view.composing : view.compositionStarted) || view.state.readOnly)\n        return false;\n    let sel = view.state.selection.main;\n    if (insert.length > 2 || insert.length == 2 && codePointSize(codePointAt(insert, 0)) == 1 ||\n        from != sel.from || to != sel.to)\n        return false;\n    let tr = insertBracket(view.state, insert);\n    if (!tr)\n        return false;\n    view.dispatch(tr);\n    return true;\n});\n/**\nCommand that implements deleting a pair of matching brackets when\nthe cursor is between them.\n*/\nconst deleteBracketPair = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let conf = config(state, state.selection.main.head);\n    let tokens = conf.brackets || defaults.brackets;\n    let dont = null, changes = state.changeByRange(range => {\n        if (range.empty) {\n            let before = prevChar(state.doc, range.head);\n            for (let token of tokens) {\n                if (token == before && nextChar(state.doc, range.head) == closing(codePointAt(token, 0)))\n                    return { changes: { from: range.head - token.length, to: range.head + token.length },\n                        range: EditorSelection.cursor(range.head - token.length) };\n            }\n        }\n        return { range: dont = range };\n    });\n    if (!dont)\n        dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"delete.backward\" }));\n    return !dont;\n};\n/**\nClose-brackets related key bindings. Binds Backspace to\n[`deleteBracketPair`](https://codemirror.net/6/docs/ref/#autocomplete.deleteBracketPair).\n*/\nconst closeBracketsKeymap = [\n    { key: \"Backspace\", run: deleteBracketPair }\n];\n/**\nImplements the extension's behavior on text insertion. If the\ngiven string counts as a bracket in the language around the\nselection, and replacing the selection with it requires custom\nbehavior (inserting a closing version or skipping past a\npreviously-closed bracket), this function returns a transaction\nrepresenting that custom behavior. (You only need this if you want\nto programmatically insert brackets—the\n[`closeBrackets`](https://codemirror.net/6/docs/ref/#autocomplete.closeBrackets) extension will\ntake care of running this for user input.)\n*/\nfunction insertBracket(state, bracket) {\n    let conf = config(state, state.selection.main.head);\n    let tokens = conf.brackets || defaults.brackets;\n    for (let tok of tokens) {\n        let closed = closing(codePointAt(tok, 0));\n        if (bracket == tok)\n            return closed == tok ? handleSame(state, tok, tokens.indexOf(tok + tok + tok) > -1, conf)\n                : handleOpen(state, tok, closed, conf.before || defaults.before);\n        if (bracket == closed && closedBracketAt(state, state.selection.main.from))\n            return handleClose(state, tok, closed);\n    }\n    return null;\n}\nfunction closedBracketAt(state, pos) {\n    let found = false;\n    state.field(bracketState).between(0, state.doc.length, from => {\n        if (from == pos)\n            found = true;\n    });\n    return found;\n}\nfunction nextChar(doc, pos) {\n    let next = doc.sliceString(pos, pos + 2);\n    return next.slice(0, codePointSize(codePointAt(next, 0)));\n}\nfunction prevChar(doc, pos) {\n    let prev = doc.sliceString(pos - 2, pos);\n    return codePointSize(codePointAt(prev, 0)) == prev.length ? prev : prev.slice(1);\n}\nfunction handleOpen(state, open, close, closeBefore) {\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty)\n            return { changes: [{ insert: open, from: range.from }, { insert: close, from: range.to }],\n                effects: closeBracketEffect.of(range.to + open.length),\n                range: EditorSelection.range(range.anchor + open.length, range.head + open.length) };\n        let next = nextChar(state.doc, range.head);\n        if (!next || /\\s/.test(next) || closeBefore.indexOf(next) > -1)\n            return { changes: { insert: open + close, from: range.head },\n                effects: closeBracketEffect.of(range.head + open.length),\n                range: EditorSelection.cursor(range.head + open.length) };\n        return { range: dont = range };\n    });\n    return dont ? null : state.update(changes, {\n        scrollIntoView: true,\n        userEvent: \"input.type\"\n    });\n}\nfunction handleClose(state, _open, close) {\n    let dont = null, changes = state.changeByRange(range => {\n        if (range.empty && nextChar(state.doc, range.head) == close)\n            return { changes: { from: range.head, to: range.head + close.length, insert: close },\n                range: EditorSelection.cursor(range.head + close.length) };\n        return dont = { range };\n    });\n    return dont ? null : state.update(changes, {\n        scrollIntoView: true,\n        userEvent: \"input.type\"\n    });\n}\n// Handles cases where the open and close token are the same, and\n// possibly triple quotes (as in `\"\"\"abc\"\"\"`-style quoting).\nfunction handleSame(state, token, allowTriple, config) {\n    let stringPrefixes = config.stringPrefixes || defaults.stringPrefixes;\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty)\n            return { changes: [{ insert: token, from: range.from }, { insert: token, from: range.to }],\n                effects: closeBracketEffect.of(range.to + token.length),\n                range: EditorSelection.range(range.anchor + token.length, range.head + token.length) };\n        let pos = range.head, next = nextChar(state.doc, pos), start;\n        if (next == token) {\n            if (nodeStart(state, pos)) {\n                return { changes: { insert: token + token, from: pos },\n                    effects: closeBracketEffect.of(pos + token.length),\n                    range: EditorSelection.cursor(pos + token.length) };\n            }\n            else if (closedBracketAt(state, pos)) {\n                let isTriple = allowTriple && state.sliceDoc(pos, pos + token.length * 3) == token + token + token;\n                let content = isTriple ? token + token + token : token;\n                return { changes: { from: pos, to: pos + content.length, insert: content },\n                    range: EditorSelection.cursor(pos + content.length) };\n            }\n        }\n        else if (allowTriple && state.sliceDoc(pos - 2 * token.length, pos) == token + token &&\n            (start = canStartStringAt(state, pos - 2 * token.length, stringPrefixes)) > -1 &&\n            nodeStart(state, start)) {\n            return { changes: { insert: token + token + token + token, from: pos },\n                effects: closeBracketEffect.of(pos + token.length),\n                range: EditorSelection.cursor(pos + token.length) };\n        }\n        else if (state.charCategorizer(pos)(next) != CharCategory.Word) {\n            if (canStartStringAt(state, pos, stringPrefixes) > -1 && !probablyInString(state, pos, token, stringPrefixes))\n                return { changes: { insert: token + token, from: pos },\n                    effects: closeBracketEffect.of(pos + token.length),\n                    range: EditorSelection.cursor(pos + token.length) };\n        }\n        return { range: dont = range };\n    });\n    return dont ? null : state.update(changes, {\n        scrollIntoView: true,\n        userEvent: \"input.type\"\n    });\n}\nfunction nodeStart(state, pos) {\n    let tree = syntaxTree(state).resolveInner(pos + 1);\n    return tree.parent && tree.from == pos;\n}\nfunction probablyInString(state, pos, quoteToken, prefixes) {\n    let node = syntaxTree(state).resolveInner(pos, -1);\n    let maxPrefix = prefixes.reduce((m, p) => Math.max(m, p.length), 0);\n    for (let i = 0; i < 5; i++) {\n        let start = state.sliceDoc(node.from, Math.min(node.to, node.from + quoteToken.length + maxPrefix));\n        let quotePos = start.indexOf(quoteToken);\n        if (!quotePos || quotePos > -1 && prefixes.indexOf(start.slice(0, quotePos)) > -1) {\n            let first = node.firstChild;\n            while (first && first.from == node.from && first.to - first.from > quoteToken.length + quotePos) {\n                if (state.sliceDoc(first.to - quoteToken.length, first.to) == quoteToken)\n                    return false;\n                first = first.firstChild;\n            }\n            return true;\n        }\n        let parent = node.to == pos && node.parent;\n        if (!parent)\n            break;\n        node = parent;\n    }\n    return false;\n}\nfunction canStartStringAt(state, pos, prefixes) {\n    let charCat = state.charCategorizer(pos);\n    if (charCat(state.sliceDoc(pos - 1, pos)) != CharCategory.Word)\n        return pos;\n    for (let prefix of prefixes) {\n        let start = pos - prefix.length;\n        if (state.sliceDoc(start, pos) == prefix && charCat(state.sliceDoc(start - 1, start)) != CharCategory.Word)\n            return start;\n    }\n    return -1;\n}\n\n/**\nReturns an extension that enables autocompletion.\n*/\nfunction autocompletion(config = {}) {\n    return [\n        commitCharacters,\n        completionState,\n        completionConfig.of(config),\n        completionPlugin,\n        completionKeymapExt,\n        baseTheme\n    ];\n}\n/**\nBasic keybindings for autocompletion.\n\n - Ctrl-Space (and Alt-\\` on macOS): [`startCompletion`](https://codemirror.net/6/docs/ref/#autocomplete.startCompletion)\n - Escape: [`closeCompletion`](https://codemirror.net/6/docs/ref/#autocomplete.closeCompletion)\n - ArrowDown: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(true)`\n - ArrowUp: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(false)`\n - PageDown: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(true, \"page\")`\n - PageDown: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(true, \"page\")`\n - Enter: [`acceptCompletion`](https://codemirror.net/6/docs/ref/#autocomplete.acceptCompletion)\n*/\nconst completionKeymap = [\n    { key: \"Ctrl-Space\", run: startCompletion },\n    { mac: \"Alt-`\", run: startCompletion },\n    { key: \"Escape\", run: closeCompletion },\n    { key: \"ArrowDown\", run: /*@__PURE__*/moveCompletionSelection(true) },\n    { key: \"ArrowUp\", run: /*@__PURE__*/moveCompletionSelection(false) },\n    { key: \"PageDown\", run: /*@__PURE__*/moveCompletionSelection(true, \"page\") },\n    { key: \"PageUp\", run: /*@__PURE__*/moveCompletionSelection(false, \"page\") },\n    { key: \"Enter\", run: acceptCompletion }\n];\nconst completionKeymapExt = /*@__PURE__*/Prec.highest(/*@__PURE__*/keymap.computeN([completionConfig], state => state.facet(completionConfig).defaultKeymap ? [completionKeymap] : []));\n/**\nGet the current completion status. When completions are available,\nthis will return `\"active\"`. When completions are pending (in the\nprocess of being queried), this returns `\"pending\"`. Otherwise, it\nreturns `null`.\n*/\nfunction completionStatus(state) {\n    let cState = state.field(completionState, false);\n    return cState && cState.active.some(a => a.isPending) ? \"pending\"\n        : cState && cState.active.some(a => a.state != 0 /* State.Inactive */) ? \"active\" : null;\n}\nconst completionArrayCache = /*@__PURE__*/new WeakMap;\n/**\nReturns the available completions as an array.\n*/\nfunction currentCompletions(state) {\n    var _a;\n    let open = (_a = state.field(completionState, false)) === null || _a === void 0 ? void 0 : _a.open;\n    if (!open || open.disabled)\n        return [];\n    let completions = completionArrayCache.get(open.options);\n    if (!completions)\n        completionArrayCache.set(open.options, completions = open.options.map(o => o.completion));\n    return completions;\n}\n/**\nReturn the currently selected completion, if any.\n*/\nfunction selectedCompletion(state) {\n    var _a;\n    let open = (_a = state.field(completionState, false)) === null || _a === void 0 ? void 0 : _a.open;\n    return open && !open.disabled && open.selected >= 0 ? open.options[open.selected].completion : null;\n}\n/**\nReturns the currently selected position in the active completion\nlist, or null if no completions are active.\n*/\nfunction selectedCompletionIndex(state) {\n    var _a;\n    let open = (_a = state.field(completionState, false)) === null || _a === void 0 ? void 0 : _a.open;\n    return open && !open.disabled && open.selected >= 0 ? open.selected : null;\n}\n/**\nCreate an effect that can be attached to a transaction to change\nthe currently selected completion.\n*/\nfunction setSelectedCompletion(index) {\n    return setSelectedEffect.of(index);\n}\n\nexport { CompletionContext, acceptCompletion, autocompletion, clearSnippet, closeBrackets, closeBracketsKeymap, closeCompletion, completeAnyWord, completeFromList, completionKeymap, completionStatus, currentCompletions, deleteBracketPair, hasNextSnippetField, hasPrevSnippetField, ifIn, ifNotIn, insertBracket, insertCompletionText, moveCompletionSelection, nextSnippetField, pickedCompletion, prevSnippetField, selectedCompletion, selectedCompletionIndex, setSelectedCompletion, snippet, snippetCompletion, snippetKeymap, startCompletion };\n", "import { <PERSON><PERSON><PERSON>, NodeProp, NodeSet, NodeType, <PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tree, IterMode } from '@lezer/common';\n\n/**\nA parse stack. These are used internally by the parser to track\nparsing progress. They also provide some properties and methods\nthat external code such as a tokenizer can use to get information\nabout the parse state.\n*/\nclass Stack {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parse that this stack is part of @internal\n    */\n    p, \n    /**\n    Holds state, input pos, buffer index triplets for all but the\n    top state @internal\n    */\n    stack, \n    /**\n    The current parse state @internal\n    */\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /**\n    @internal\n    */\n    reducePos, \n    /**\n    The input position up to which this stack has parsed.\n    */\n    pos, \n    /**\n    The dynamic score of the stack, including dynamic precedence\n    and error-recovery penalties\n    @internal\n    */\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /**\n    @internal\n    */\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /**\n    @internal\n    */\n    bufferBase, \n    /**\n    @internal\n    */\n    curContext, \n    /**\n    @internal\n    */\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /**\n    @internal\n    */\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /**\n    @internal\n    */\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /**\n    The stack's current [context](#lr.ContextTracker) value, if\n    any. Its type will depend on the context tracker's type\n    parameter, or it will be `null` if there is no context\n    tracker.\n    */\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /**\n    @internal\n    */\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /**\n    @internal\n    */\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /**\n    @internal\n    */\n    storeNode(term, start, end, size = 4, isReduce = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!isReduce || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */)\n                while (index > 0 && this.buffer[index - 2] > end) {\n                    // Move this record forward\n                    this.buffer[index] = this.buffer[index - 4];\n                    this.buffer[index + 1] = this.buffer[index - 3];\n                    this.buffer[index + 2] = this.buffer[index - 2];\n                    this.buffer[index + 3] = this.buffer[index - 1];\n                    index -= 4;\n                    if (size > 4)\n                        size -= 4;\n                }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /**\n    @internal\n    */\n    shift(action, type, start, end) {\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (end > this.pos || type <= parser.maxNode) {\n                this.pos = end;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = end;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(type, start);\n            if (type <= parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = end;\n            this.shiftContext(type, start);\n            if (type <= this.p.parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n    }\n    // Apply an action\n    /**\n    @internal\n    */\n    apply(action, next, nextStart, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextStart, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /**\n    @internal\n    */\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /**\n    @internal\n    */\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /**\n    @internal\n    */\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /**\n    Check if the given term would be able to be shifted (optionally\n    after some reductions) on this stack. This can be useful for\n    external tokenizers that want to make sure they only provide a\n    given token when it applies.\n    */\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /**\n    @internal\n    */\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.reducePos = this.pos;\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /**\n    @internal\n    */\n    forceReduce() {\n        let { parser } = this.p;\n        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n                let backup = this.findForcedReduction();\n                if (backup == null)\n                    return false;\n                reduce = backup;\n            }\n            this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /**\n    Try to scan through the automaton to find some kind of reduction\n    that can be applied. Used when the regular ForcedReduce field\n    isn't a valid action. @internal\n    */\n    findForcedReduction() {\n        let { parser } = this.p, seen = [];\n        let explore = (state, depth) => {\n            if (seen.includes(state))\n                return;\n            seen.push(state);\n            return parser.allActions(state, (action) => {\n                if (action & (262144 /* Action.StayFlag */ | 131072 /* Action.GotoFlag */)) ;\n                else if (action & 65536 /* Action.ReduceFlag */) {\n                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */) - depth;\n                    if (rDepth > 1) {\n                        let term = action & 65535 /* Action.ValueMask */, target = this.stack.length - rDepth * 3;\n                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n                            return (rDepth << 19 /* Action.ReduceDepthShift */) | 65536 /* Action.ReduceFlag */ | term;\n                    }\n                }\n                else {\n                    let found = explore(action, depth + 1);\n                    if (found != null)\n                        return found;\n                }\n            });\n        };\n        return explore(this.state, 0);\n    }\n    /**\n    @internal\n    */\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /**\n    Check whether this state has no further actions (assumed to be a direct descendant of the\n    top state, since any other states must be able to continue\n    somehow). @internal\n    */\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /**\n    Restart the stack (put it back in its start state). Only safe\n    when this.stack.length == 3 (state is directly below the top\n    state). @internal\n    */\n    restart() {\n        this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /**\n    @internal\n    */\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /**\n    Get the parser used by this stack.\n    */\n    get parser() { return this.p.parser; }\n    /**\n    Test whether a given dialect (by numeric ID, as exported from\n    the terms file) is enabled.\n    */\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /**\n    @internal\n    */\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n    }\n    /**\n    @internal\n    */\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /**\n    @internal\n    */\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /**\n    @internal\n    */\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/**\n[Tokenizers](#lr.ExternalTokenizer) interact with the input\nthrough this interface. It presents the input as a stream of\ncharacters, tracking lookahead and hiding the complexity of\n[ranges](#common.Parser.parse^ranges) from tokenizer code.\n*/\nclass InputStream {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    input, \n    /**\n    @internal\n    */\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /**\n        @internal\n        */\n        this.chunk = \"\";\n        /**\n        @internal\n        */\n        this.chunkOff = 0;\n        /**\n        Backup chunk\n        */\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /**\n        The character code of the next code unit in the input, or -1\n        when the stream is at the end of the input.\n        */\n        this.next = -1;\n        /**\n        @internal\n        */\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /**\n    @internal\n    */\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /**\n    @internal\n    */\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /**\n    Look at a code unit near the stream position. `.peek(0)` equals\n    `.next`, `.peek(-1)` gives you the previous character, and so\n    on.\n    \n    Note that looking around during tokenizing creates dependencies\n    on potentially far-away content, which may reduce the\n    effectiveness incremental parsing—when looking forward—or even\n    cause invalid reparses when looking backward more than 25 code\n    units, since the library does not track lookbehind.\n    */\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /**\n    Accept a token. By default, the end of the token is set to the\n    current stream position, but you can pass an offset (relative to\n    the stream position) to change that.\n    */\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /**\n    Move the stream forward N (defaults to 1) code units. Returns\n    the new value of [`next`](#lr.InputStream.next).\n    */\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /**\n    @internal\n    */\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /**\n    @internal\n    */\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/**\n@internal\n*/\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n@hide\n*/\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, skipped = 0;\n        for (;;) {\n            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (!atEof)\n                skipped++;\n            if (nextPos == null)\n                break;\n            input.reset(nextPos, input.token);\n        }\n        if (skipped) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, skipped);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n`@external tokens` declarations in the grammar should resolve to\nan instance of this class.\n*/\nclass ExternalTokenizer {\n    /**\n    Create a tokenizer. The first argument is the function that,\n    given an input stream, scans for the types of tokens it\n    recognizes at the stream's position, and calls\n    [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    one.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */ && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Safety.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Safety.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Safety.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Finish with \" + this.stackID(finished));\n                return this.stackToTree(finished);\n            }\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Force-finish \" + this.stackID(finished));\n                return this.stackToTree(finished.forceAll());\n            }\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 9000 /* Rec.CutDepth */) {\n            while (stack.stack.length > 6000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            let main = this.tokens.mainToken;\n            localStack.apply(action, term, main ? main.start : localStack.pos, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/**\nContext trackers are used to track stateful context (such as\nindentation in the Python grammar, or parent elements in the XML\ngrammar) needed by external tokenizers. You declare them in a\ngrammar file as `@context exportName from \"module\"`.\n\nContext values should be immutable, and can be updated (replaced)\non shift or reduce actions.\n\nThe export used in a `@context` declaration should be of this\ntype.\n*/\nclass ContextTracker {\n    /**\n    Define a context tracker.\n    */\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/**\nHolds the parse tables for a given grammar, as generated by\n`lezer-generator`, and provides [methods](#common.Parser) to parse\ncontent with.\n*/\nclass LRParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(spec) {\n        super();\n        /**\n        @internal\n        */\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Get a goto table entry @internal\n    */\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /**\n    Check if this state has an action for a given terminal @internal\n    */\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /**\n    @internal\n    */\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /**\n    @internal\n    */\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /**\n    @internal\n    */\n    validAction(state, action) {\n        return !!this.allActions(state, a => a == action ? true : null);\n    }\n    /**\n    @internal\n    */\n    allActions(state, action) {\n        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */);\n        let result = deflt ? action(deflt) : undefined;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */); result == null; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            result = action(pair(this.data, i + 1));\n        }\n        return result;\n    }\n    /**\n    Get the states that can follow this one through shift actions or\n    goto jumps. @internal\n    */\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /**\n    Configure the parser. Returns a new parser instance that has the\n    given settings modified. Settings not provided in `config` are\n    kept from the original parser.\n    */\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /**\n    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    are registered for this parser.\n    */\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /**\n    Returns the name associated with a given term. This will only\n    work for all terms when the parser was generated with the\n    `--names` option. By default, only the names of tagged terms are\n    stored.\n    */\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /**\n    The eof term id is always allocated directly after the node\n    types. @internal\n    */\n    get eofTerm() { return this.maxNode + 1; }\n    /**\n    The type of top node produced by the parser.\n    */\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /**\n    @internal\n    */\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /**\n    @internal\n    */\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /**\n    Used by the output of the parser generator. Not available to\n    user code. @hide\n    */\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\nexport { ContextTracker, ExternalTokenizer, InputStream, LRParser, LocalTokenGroup, Stack };\n", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n"], "names": [], "sourceRoot": ""}