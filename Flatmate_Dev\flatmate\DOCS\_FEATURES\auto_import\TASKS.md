# Implementation Tasks: Auto-Import Folder Feature

## Task 1: Install watchdog Dependency
**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\requirements.txt` (or package manager)
**Time**: 5 minutes
**Dependencies**: None

**Action**: Add watchdog library to project dependencies
```bash
pip install watchdog
```

**Verification**: Import succeeds without errors
```python
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
```

## Task 2: Create AutoImportManager Service Structure
**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py`
**Time**: 20 minutes
**Dependencies**: Task 1

**Current Code**: File does not exist

**New Code**: Complete service class structure
```python
#!/usr/bin/env python3
"""
Auto Import Manager Service

Monitors designated folder for new CSV files and automatically processes them
through the existing dw_director pipeline.
"""

import os
import queue
import shutil
import threading
import time
from pathlib import Path
from typing import Optional

from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from fm.core.config import config
from fm.core.config.keys import ConfigKeys
from fm.core.services.logger import log
from fm.core.services.event_bus import global_event_bus


class AutoImportEventHandler(FileSystemEventHandler):
    """Handle file system events for import folder"""
    
    def __init__(self, manager):
        super().__init__()
        self.manager = manager
    
    def on_created(self, event):
        """Handle new file creation events"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        if file_path.suffix.lower() == '.csv':
            log.info(f"New CSV file detected: {file_path.name}")
            self.manager.queue_file_for_processing(str(file_path))


class AutoImportManager:
    """Singleton service for automated file import monitoring"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, 'initialized'):
            return
        
        self.initialized = True
        self.observer = None
        self.processing_queue = queue.Queue(maxsize=100)
        self.worker_thread = None
        self.running = False
        
        # Configuration
        self.import_path = None
        self.archive_path = None
        self.failed_path = None
        
        log.info("AutoImportManager initialized")
    
    def start(self) -> None:
        """Start file system monitoring"""
        # Implementation placeholder
        pass
    
    def stop(self) -> None:
        """Stop monitoring and cleanup resources"""
        # Implementation placeholder
        pass
    
    def queue_file_for_processing(self, file_path: str) -> None:
        """Add file to processing queue"""
        # Implementation placeholder
        pass
```

## Task 3: Implement Configuration Loading
**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py`
**Method**: `AutoImportManager.__init__()` (line ~45)
**Time**: 15 minutes
**Dependencies**: Task 2

**Current Code** (lines 45-55):
```python
        # Configuration
        self.import_path = None
        self.archive_path = None
        self.failed_path = None
        
        log.info("AutoImportManager initialized")
```

**New Code**:
```python
        # Load configuration
        self._load_configuration()
        
        log.info(f"AutoImportManager initialized - monitoring: {self.import_path}")
    
    def _load_configuration(self) -> None:
        """Load auto-import configuration from settings"""
        self.enabled = config.get_value(ConfigKeys.AutoImport.ENABLED, False)
        
        if not self.enabled:
            log.info("Auto-import is disabled in configuration")
            return
        
        # Get configured paths or set defaults
        downloads_path = Path.home() / "Downloads"
        default_import_path = downloads_path / "flatmate_imports"
        
        self.import_path = Path(config.get_value(
            ConfigKeys.AutoImport.IMPORT_PATH, 
            str(default_import_path)
        ))
        
        self.archive_path = Path(config.get_value(
            ConfigKeys.AutoImport.ARCHIVE_PATH,
            str(self.import_path / "archive")
        ))
        
        self.failed_path = Path(config.get_value(
            ConfigKeys.AutoImport.FAILED_PATH,
            str(self.import_path / "failed_imports")
        ))
        
        # Ensure directories exist
        self.import_path.mkdir(parents=True, exist_ok=True)
        self.archive_path.mkdir(parents=True, exist_ok=True)
        self.failed_path.mkdir(parents=True, exist_ok=True)
        
        log.info(f"Auto-import paths configured:")
        log.info(f"  Import: {self.import_path}")
        log.info(f"  Archive: {self.archive_path}")
        log.info(f"  Failed: {self.failed_path}")
```

## Task 4: Implement File System Monitoring
**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py`
**Method**: `AutoImportManager.start()` (line ~58)
**Time**: 20 minutes
**Dependencies**: Task 3

**Current Code** (lines 58-61):
```python
    def start(self) -> None:
        """Start file system monitoring"""
        # Implementation placeholder
        pass
```

**New Code**:
```python
    def start(self) -> None:
        """Start file system monitoring"""
        if not self.enabled:
            log.info("Auto-import is disabled - not starting monitoring")
            return
        
        if self.running:
            log.warning("Auto-import monitoring is already running")
            return
        
        try:
            # Start worker thread
            self.running = True
            self.worker_thread = threading.Thread(
                target=self._process_files_worker,
                daemon=True,
                name="AutoImportWorker"
            )
            self.worker_thread.start()
            
            # Start file system observer
            self.observer = Observer()
            event_handler = AutoImportEventHandler(self)
            self.observer.schedule(
                event_handler,
                str(self.import_path),
                recursive=False
            )
            self.observer.start()
            
            log.info(f"Auto-import monitoring started for: {self.import_path}")
            
        except Exception as e:
            log.error(f"Failed to start auto-import monitoring: {e}")
            self.stop()
            raise
```

## Task 5: Implement Worker Thread Processing
**File**: `C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py`
**Method**: Add `_process_files_worker()` method
**Time**: 20 minutes
**Dependencies**: Task 4

**New Code**: Add after `start()` method
```python
    def _process_files_worker(self) -> None:
        """Worker thread to process files from queue"""
        log.info("Auto-import worker thread started")
        
        while self.running:
            try:
                # Get file from queue with timeout
                file_path = self.processing_queue.get(timeout=1.0)
                
                if file_path is None:  # Shutdown signal
                    break
                
                self._process_single_file(file_path)
                self.processing_queue.task_done()
                
            except queue.Empty:
                continue  # Timeout - check if still running
            except Exception as e:
                log.error(f"Error in auto-import worker thread: {e}")
        
        log.info("Auto-import worker thread stopped")
    
    def _process_single_file(self, file_path: str) -> None:
        """Process a single file through dw_director pipeline"""
        file_path_obj = Path(file_path)
        
        try:
            # Wait for file to be fully written (debounce)
            time.sleep(2.0)
            
            if not file_path_obj.exists():
                log.warning(f"File no longer exists: {file_path_obj.name}")
                return
            
            # Import dw_director here to avoid circular imports
            from fm.modules.update_data.utils.dw_director import dw_director
            
            # Create job sheet for single file
            job_sheet = {
                "filepaths": [str(file_path_obj)],
                "save_folder": str(self.archive_path.parent),  # Temp location
                "update_database": True,
                "cleanup_source_folder": False  # We handle file movement
            }
            
            log.info(f"Processing file: {file_path_obj.name}")
            result = dw_director(job_sheet)
            
            if result.get("status") == "success":
                self._move_to_archive(file_path_obj)
                log.info(f"Successfully processed: {file_path_obj.name}")
            else:
                self._move_to_failed(file_path_obj, result.get("message", "Unknown error"))
                log.error(f"Failed to process: {file_path_obj.name}")
                
        except Exception as e:
            log.error(f"Error processing file {file_path_obj.name}: {e}")
            self._move_to_failed(file_path_obj, str(e))
```

---
*Created: 2025-07-21*
*Based on: implementation_outline.md and DESIGN.md*
