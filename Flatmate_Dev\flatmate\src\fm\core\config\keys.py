"""Configuration keys for the FlatMate application."""

from enum import Enum
from typing import Dict, Any

class EnvMode(str, Enum):
    """Valid environment modes"""
    DEV = 'development'
    PROD = 'production'

class ConfigKeys:
    """All configuration keys in one place"""
    
    class App(str, Enum):
        """Application settings"""
        DEBUG_MODE = 'app.debug_mode'
        IS_FIRST_RUN = 'app.is_first_run'
        ENVIRONMENT = 'app.environment'
        BASE_FONT_SIZE = 'app.base_font_size'
    
    class Env(str, Enum):
        """Environment settings"""
        MODE = 'env.mode'
        DEBUG = 'env.debug'
        OS_TYPE = 'env.os_type'

    class Paths(str, Enum):
        """All path configurations"""
        # System paths
        PROJECT_ROOT = 'paths.project_root'
        LOGS = 'paths.logs'
        CONFIG = 'paths.config'
        RESOURCES = 'paths.resources'
        
        # User paths
        USER_HOME = 'paths.user_home'
        DATA = 'paths.data'
        PROFILES = 'paths.profiles'
        REPORTS = 'paths.reports'
        MASTER = 'paths.master'
        BACKUP = 'paths.backup'

    class Window(str, Enum):
        """Window preferences"""
        LEFT_PANEL_WIDTH = 'window.left_panel_width'
        RIGHT_PANEL_WIDTH = 'window.right_panel_width'
        COLUMN_WIDTHS = 'window.column_widths'
        RECENT_FILES = 'window.recent_files'

    class Logging(str, Enum):
        """Logging preferences"""
        LEVEL = 'logging.level'  # General/file log level
        CONSOLE_LOG_LEVEL = 'logging.console_level'  # New key for console output threshold
        SHOW_INFO = 'log.show_info'
        SHOW_WARNINGS = 'log.show_warnings'

    class Reports(str, Enum):
        """Report settings"""
        LAST_OUTPUT_PATH = 'reports.last_output_path'
        RECENT_OUTPUTS = 'reports.recent_outputs'
        
    class UpdateData(str, Enum):
        """Update data preferences"""
        LAST_UPDATE = 'update_data.last_update'
        UPDATE_FREQUENCY = 'update_data.frequency'
        AUTO_UPDATE = 'update_data.auto_update'

    class AutoImport(str, Enum):
        """Auto-import folder settings"""
        ENABLED = 'auto_import.enabled'
        IMPORT_PATH = 'auto_import.import_path'
        ARCHIVE_PATH = 'auto_import.archive_path'
        FAILED_PATH = 'auto_import.failed_path'
    
    @classmethod
    def get_defaults(cls) -> Dict[str, Any]:
        """Get default values for all settings"""
        from pathlib import Path
        
        return {
            # Environment
            cls.Env.MODE: EnvMode.DEV,
            cls.Env.DEBUG: False,
            cls.Env.OS_TYPE: 'auto-detect',
            
            # Paths are set dynamically in SystemPaths class
            
            # App
            cls.App.DEBUG_MODE: True,
            cls.App.ENVIRONMENT: EnvMode.DEV.value,
            cls.App.IS_FIRST_RUN: True,
            cls.App.BASE_FONT_SIZE: 12,
            
            # Window preferences
            cls.Window.LEFT_PANEL_WIDTH: 250,
            cls.Window.RIGHT_PANEL_WIDTH: 250,
            cls.Window.COLUMN_WIDTHS: {},
            cls.Window.RECENT_FILES: [],
            
            # Logging
            cls.Logging.LEVEL: "DEBUG",
            cls.Logging.CONSOLE_LOG_LEVEL: "INFO", # Default console log level
            cls.Logging.SHOW_INFO: True,
            cls.Logging.SHOW_WARNINGS: True,
            
            # Reports
            cls.Reports.LAST_OUTPUT_PATH: str(Path('~/Documents').expanduser()),
            cls.Reports.RECENT_OUTPUTS: [],
            
            # Update data
            cls.UpdateData.LAST_UPDATE: None, #? unsure, dont think used, yet.
            cls.UpdateData.UPDATE_FREQUENCY: "daily", # not used
            cls.UpdateData.AUTO_UPDATE: False, # not used

            # Auto-import
            cls.AutoImport.ENABLED: False,
            cls.AutoImport.IMPORT_PATH: str(Path.home() / "Downloads" / "flatmate_imports"),
            cls.AutoImport.ARCHIVE_PATH: str(Path.home() / "Downloads" / "flatmate_imports" / "archive"),
            cls.AutoImport.FAILED_PATH: str(Path.home() / "Downloads" / "flatmate_imports" / "failed_imports"),
            
           
        }
