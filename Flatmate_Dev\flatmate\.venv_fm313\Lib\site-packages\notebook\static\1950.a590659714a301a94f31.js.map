{"version": 3, "file": "1950.a590659714a301a94f31.js?v=a590659714a301a94f31", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA4C;AACN;AACA;AACiB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA,YAAY,4BAAQ;AACpB,SAAS,4BAAQ,CAAC,+BAAW,YAAY,gCAAiB;AAC1D,CAAC;;AAED,sDAAe,KAAK,EAAC;;;;;;;ACzBU;;AAE/B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oEAAoE,WAAW;AAC/E;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oEAAoE,WAAW;AAC/E;AACA;AACA;AACA;AACO;AACP,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,+BAA+B,uBAAU;;AAEzC;AACA,+BAA+B,uBAAU;;AAEzC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,yBAAY;AACrB,mBAAmB,uBAAU;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,mBAAM;AACjB;AACA;AACA;AACA,WAAW,qBAAQ;AACnB,aAAa,sBAAS;AACtB,KAAK;AACL;AACA;AACA;AACA,WAAW,qBAAQ;AACnB,aAAa,sBAAS;AACtB,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,sBAAM;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,sBAAM;AACd;AACA,SAAS;AACT;AACA;AACA,MAAM,sBAAM,CAAC,mBAAM;AACnB;AACA;AACA,MAAM,sBAAM,CAAC,mBAAM;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,QAAQ,0BAAa;AACrB;AACA,MAAM;AACN;AACA;AACA,kCAAkC,CAAC,0BAAa,YAAY;AAC5D;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,0BAAa;AACrB;AACA;;AAEA;AACA;AACA;AACA,eAAe,mBAAM;AACrB;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,mBAAM;AACnB;AACA;AACA;AACA;AACA;AACA,aAAa,mBAAM;AACnB;AACA;AACA;AACA;AACA;AACA,aAAa,eAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA,IAAI,sBAAM;AACV;AACA;AACA;AACA,KAAK;;AAEL,IAAI,sBAAM;AACV;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA,MAAM,sBAAM;AACZ;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA,SAAS,yBAAY;AACrB,mBAAmB,uBAAU;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,qBAAQ;AACnB;AACA;AACA;AACA;AACA,IAAI,qBAAQ;AACZ;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,eAAe,cAAc;AAC7B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS,0BAAa;AACtB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,0BAAa;AACtB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,qBAAQ;AAC1B;AACA;AACA;AACA,aAAa,qBAAQ;AACrB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,kBAAkB,qBAAQ;AAC1B;AACA;AACA;AACA,aAAa,qBAAQ;AACrB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,0BAAa;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;;AChgBA;;AAEmC;;AAEnC;;AAE0B;;;;;;;;;;;;;ACN1B;AACkC;AACI;AACtC;AACA;AACA,WAAW,gEAAC,YAAY,gEAAK;AAC7B;AACA;AACA,iEAAe,OAAO,EAAC;;;;;;;;;;;;ACRiB;;AAExC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,GAAG;AAChB;AACA;AACA;AACA,oBAAoB,QAAQ,IAAI,QAAQ;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kEAAS;AAClB;;AAEA,iEAAe,KAAK,EAAC;;;;;;;;;;;;;;;;;;;;;ACjCS;AAGA;AAIA;AAKA;AAWA;;AAE9B;AACA;AACA,0BAA0B,qEAAM;AAChC,sBAAsB,gBAAgB,KAAK;AAC3C;AACA,GAAG;AACH;AACA,2BAA2B,qEAAM;AACjC,KAAK;AACL,UAAU;AACV,gBAAgB,m1BAAm1B;AACn2B,kBAAkB,uhBAAuhB;AACziB;AACA,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,cAAc,sGAAsG;AACpH,cAAc,uMAAuM;AACrN,cAAc;AACd;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,KAAK;AACL,cAAc,kBAAkB,IAAI,QAAQ,IAAI,0IAA0I,IAAI,YAAY,oBAAoB,2IAA2I,qBAAqB,0BAA0B,2HAA2H,aAAa,sBAAsB,yBAAyB,IAAI,2IAA2I,IAAI,0BAA0B,IAAI,aAAa,IAAI,aAAa,oBAAoB,kCAAkC,KAAK,WAAW,qBAAqB,iBAAiB,IAAI,aAAa,IAAI,aAAa,qBAAqB,qKAAqK,IAAI,aAAa,IAAI,aAAa,IAAI,aAAa,IAAI,aAAa,IAAI,aAAa,qBAAqB,aAAa,IAAI,aAAa,qBAAqB,aAAa,IAAI,aAAa,yFAAyF,aAAa,IAAI,iBAAiB,IAAI,aAAa,sCAAsC,aAAa,IAAI,8BAA8B,IAAI,aAAa,qBAAqB,aAAa;AACrjD,sBAAsB,mDAAmD;AACzE,gCAAgC,qEAAM;AACtC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,KAAK;AACL,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ,iEAAiE;AACjE;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA,OAAO;AACP;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA,OAAO;AACP;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP,4DAA4D;AAC5D,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA,OAAO;AACP;AACA,sCAAsC,qEAAM;AAC5C;AACA,OAAO;AACP,iBAAiB;AACjB,qCAAqC,q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mqBAAmqB,eAAe,cAAc,2EAA2E,EAAE,WAAW,8OAA8O,EAAE,WAAW,6KAA6K,EAAE;AAClsC,oBAAoB,sBAAsB,mCAAmC,mBAAmB,mCAAmC,kBAAkB,mCAAmC,gBAAgB,uCAAuC,mBAAmB,mCAAmC,aAAa,mCAAmC,cAAc,wDAAwD,iBAAiB,2DAA2D,mBAAmB,2CAA2C,YAAY,2GAA2G,iBAAiB,+CAA+C,aAAa,iCAAiC,cAAc,+CAA+C,2BAA2B,uCAAuC,iBAAiB,mCAAmC,iBAAiB,mCAAmC,eAAe;AAChgC;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,yEAAU;AACvB;AACA,oCAAoC,qEAAM,UAAU,yEAAc;AAClE,oCAAoC,qEAAM;AAC1C;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,8CAA8C,IAAI;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,4CAA4C,qEAAM;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,OAAO;AAC/B,2BAA2B,uEAAK;AAChC;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,kBAAkB;AAClB,6BAA6B,qEAAM;AACnC,EAAE,8DAAG;AACL,EAAE,oEAAK;AACP,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA,MAAM,8DAAG;AACT;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA,iCAAiC,qEAAM;AACvC;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC,iCAAiC,qEAAM;AACvC;AACA,CAAC;AACD;AACA,6BAA6B,qEAAM,OAAO,wEAAS;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACiC;AACjC,2BAA2B,qEAAM;AACjC,mBAAmB,uDAAc;AACjC;AACA;AACA;AACA,SAAS,uDAAW;AACpB,CAAC;AACD,gCAAgC,qEAAM;AACtC,mBAAmB;AACnB,aAAa;AACb;AACA;AACA,YAAY;AACZ;AACA;AACA,aAAa;AACb;;;;AAIA;AACA,YAAY;AACZ,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA,cAAc;AACd;AACA;;AAEA;AACA,cAAc;AACd;AACA;;AAEA;AACA,wBAAwB;AACxB;AACA;AACA,0BAA0B;AAC1B,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA,wBAAwB;AACxB;AACA;;AAEA;AACA,eAAe;AACf,YAAY;AACZ,cAAc;AACd;AACA;AACA;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA,kBAAkB;AAClB,wBAAwB;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACwC;;AAExC;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA,GAAG;AACH,CAAC;AACD,gCAAgC,qEAAM;AACtC,EAAE,8DAAG;AACL;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA,CAAC;AACD,4BAA4B,qEAAM;AAClC;AACA;AACA,CAAC;AACD,6BAA6B,qEAAM;AACnC;AACA;AACA,CAAC;AACD,4BAA4B,qEAAM;AAClC;AACA;AACA,CAAC;AACD,2BAA2B,qEAAM;AACjC;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc,yEAAU;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,WAAW;AACX;AACA,qEAAM;AACN,sCAAsC,qEAAM;AAC5C;AACA;AACA;AACA,YAAY,sBAAsB,kBAAkB;AACpD,IAAI,8DAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,CAAC;AACD;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA,QAAQ,8DAAG;AACX,+CAA+C,UAAU,KAAK,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,2BAA2B;AAC9H;AACA;AACA;AACA;AACA;AACA,QAAQ,8DAAG;AACX,oCAAoC,UAAU,iBAAiB,UAAU,WAAW,UAAU,YAAY,UAAU;AACpH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8DAAG;AACT,6CAA6C,UAAU,gBAAgB,cAAc,iBAAiB,eAAe,QAAQ,MAAM;AACnI;AACA;AACA;AACA;AACA;AACA,MAAM,8DAAG;AACT,MAAM,8DAAG;AACT,MAAM,8DAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP,6BAA6B,UAAU,QAAQ,OAAO,QAAQ,OAAO,UAAU,QAAQ,EAAE,uBAAuB,QAAQ,wCAAwC;AAChK;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,8DAAG;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA,EAAE,8DAAG;AACL,4CAA4C,UAAU,KAAK,gBAAgB,KAAK,gBAAgB,SAAS,mBAAmB;AAC5H;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,wBAAwB;AACtC,cAAc,SAAS;AACvB;AACA;AACA;AACA,QAAQ,8DAAG;AACX;AACA,MAAM,8DAAG;AACT,2CAA2C,UAAU,OAAO,WAAW,WAAW,GAAG,GAAG,IAAI,GAAG,gBAAgB,GAAG,gBAAgB,YAAY,WAAW,SAAS,OAAO,EAAE,QAAQ;AACnL;AACA;AACA;AACA;AACA,QAAQ,8DAAG;AACX,iDAAiD,UAAU,eAAe,cAAc,kBAAkB,cAAc,EAAE,WAAW,UAAU,SAAS,QAAQ,QAAQ,YAAY,WAAW,OAAO,cAAc,IAAI,cAAc,EAAE,sBAAsB,gCAAgC,0CAA0C;AACxU;AACA;AACA;AACA,QAAQ,8DAAG;AACX,iDAAiD,SAAS,cAAc,aAAa,EAAE,QAAQ,EAAE,UAAU,MAAM,aAAa,IAAI,aAAa,EAAE,qBAAqB,+BAA+B,0CAA0C;AAC/O;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8DAAG;AACT;AACA;AACA,EAAE,8DAAG;AACL,uCAAuC,UAAU,KAAK,gBAAgB,KAAK,gBAAgB,SAAS,mBAAmB;AACvH;AACA;AACA,qEAAM;AACN,6BAA6B,yBAAyB,IAAI,oCAAoC;AAC9F;AACA,YAAY,sBAAsB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,yBAAyB,sBAAsB,wBAAwB;AAChF;AACA;AACA,WAAW;AACX;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL,UAAU,yBAAyB;AACnC;AACA;AACA,WAAW;AACX;AACA,qEAAM;;AAEN;AAC8D;;AAE9D;AAC4B;AAC5B;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA,aAAa,oDAAM;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA,MAAM,uEAAQ,CAAC,yEAAU;AACzB;AACA,IAAI,8DAAG;AACP;AACA;AACA,aAAa,mFAAoB,CAAC,6EAAc;AAChD;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;AAEA;AACyD;;AAEzD;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA,IAAI,8DAAG,6BAA6B,UAAU;AAC9C;AACA;AACA;AACA,yBAAyB,SAAS,UAAU,IAAI,GAAG,GAAG,GAAG,YAAY,GAAG,cAAc,EAAE,OAAO;AAC/F,CAAC;;AAED;AACA;AACA;AACA,sCAAsC,qEAAM;AAC5C,kBAAkB,yEAAU;AAC5B,wBAAwB,uEAAQ;AAChC,uDAAuD,yEAAU;AACjE;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,oDAAO;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,MAAM,yEAAU;AAChB;AACA;AACA;AACA;AACA,qEAAM;AACN,wCAAwC,qEAAM;AAC9C,EAAE,8DAAG;AACL;AACA,qBAAqB,yEAAU;AAC/B,UAAU,2BAA2B,EAAE,qFAAuB;AAC9D;AACA;AACA;AACA;AACA;AACA,kBAAkB,wEAAa;AAC/B,MAAM,8DAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,EAAE,IAAI,iCAAiC;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,wEAAa;AAC/B;AACA;AACA;AACA,sCAAsC,EAAE,IAAI,EAAE;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,wEAAa;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,EAAE,IAAI,EAAE;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,wEAAa;AAC/B;AACA;AACA;AACA,sCAAsC,EAAE,IAAI,EAAE;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,wEAAa;AAC/B;AACA;AACA;AACA,sCAAsC,EAAE,IAAI,EAAE;AAC9C;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC,EAAE,8DAAG;AACL,kBAAkB;AAClB,kBAAkB;AAClB,oBAAoB,QAAQ,IAAI,QAAQ,IAAI,YAAY,IAAI,YAAY;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG,kCAAkC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AACnE;AACA,IAAI;AACJ;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG,8BAA8B,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,QAAQ;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,CAAC;AACD,yCAAyC,qEAAM;AAC/C,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA;AACA,cAAc,qDAAU;AACxB;AACA;AACA;AACA,UAAU,OAAO,EAAE,wFAA0B;AAC7C,uBAAuB,mDAAI;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,yEAAU,oCAAoC,yEAAU;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACuC;;AAEvC;AACA,qDAAqD,qEAAM;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,YAAY;AACpB,QAAQ,mBAAmB;AAC3B,QAAQ,+BAA+B;AACvC,QAAQ,2BAA2B;AACnC,QAAQ,gBAAgB;AACxB;AACA,QAAQ,0BAA0B;AAClC,QAAQ,yCAAyC;AACjD,QAAQ,8BAA8B;AACtC,QAAQ,sBAAsB;AAC9B;AACA,QAAQ,iCAAiC;AACzC,QAAQ,yCAAyC;AACjD,QAAQ,yBAAyB;AACjC;AACA,QAAQ,kBAAkB;AAC1B,QAAQ,0BAA0B;AAClC,QAAQ,kCAAkC;AAC1C,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,mBAAmB;AAC3B,QAAQ,2BAA2B;AACnC,QAAQ,0BAA0B;AAClC,QAAQ,iCAAiC;AACzC,QAAQ,yBAAyB;AACjC,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,YAAY;AACpB,QAAQ,yBAAyB;AACjC,QAAQ,iCAAiC;AACzC,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,YAAY;AACpB,QAAQ,wBAAwB;AAChC,QAAQ,iCAAiC;AACzC,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,gBAAgB;AACxB,QAAQ,oBAAoB;AAC5B,QAAQ,6BAA6B;AACrC,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,mBAAmB;AAC3B,QAAQ,2BAA2B;AACnC,QAAQ,mCAAmC;AAC3C,QAAQ,2BAA2B;AACnC,QAAQ,0BAA0B;AAClC,QAAQ,iCAAiC;AACzC,QAAQ,4CAA4C;AACpD,QAAQ,oCAAoC;AAC5C,QAAQ,yBAAyB;AACjC,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,QAAQ,oBAAoB;AAC5B;AACA,QAAQ,oBAAoB;AAC5B,QAAQ,2BAA2B;AACnC;AACA,QAAQ,oCAAoC;AAC5C,QAAQ,6BAA6B;AACrC;AACA,QAAQ,0BAA0B;AAClC,QAAQ,iCAAiC;AACzC;AACA,QAAQ,4CAA4C;AACpD,QAAQ,mCAAmC;AAC3C,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,YAAY;AACpB,QAAQ,wBAAwB;AAChC,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,YAAY;AACpB,QAAQ,gBAAgB;AACxB,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,gBAAgB;AACxB,QAAQ,oBAAoB;AAC5B,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,gBAAgB;AACxB,QAAQ,YAAY;AACpB,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,2BAA2B;AACnC,QAAQ,2BAA2B;AACnC,QAAQ,mCAAmC;AAC3C,QAAQ,2BAA2B;AACnC,QAAQ,0BAA0B;AAClC,QAAQ,iCAAiC;AACzC,QAAQ,4CAA4C;AACpD;AACA,QAAQ,oCAAoC;AAC5C,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ,mBAAmB;AAC3B,QAAQ,2BAA2B;AACnC;AACA,QAAQ,mCAAmC;AAC3C,QAAQ,4CAA4C;AACpD,QAAQ,oCAAoC;AAC5C,QAAQ,yBAAyB;AACjC,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,QAAQ,2BAA2B;AACnC;AACA,QAAQ,oCAAoC;AAC5C,QAAQ,6BAA6B;AACrC;AACA,QAAQ,0BAA0B;AAClC,QAAQ,iCAAiC;AACzC;AACA,QAAQ,4CAA4C;AACpD,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,QAAQ,oBAAoB;AAC5B;AACA,QAAQ,oBAAoB;AAC5B,QAAQ,2BAA2B;AACnC;AACA,QAAQ,oCAAoC;AAC5C,QAAQ,4CAA4C;AACpD,QAAQ,mCAAmC;AAC3C,QAAQ;AACR;AACA;AACA,YAAY,YAAY;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA,qEAAM;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,qEAAM;AACN;;AAEA;AACA;AACA;AACA;AACA,qEAAM;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,kBAAkB,uBAAuB;AACzC;AACA;AACA;AACA;AACA;AACA,QAAQ,+BAA+B;AACvC,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,qEAAM;;AAEN;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,CAAC;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACuC;AACvC,kCAAkC,qEAAM;AACxC,kBAAkB,yEAAU;AAC5B;AACA,8CAA8C,uEAAQ;AACtD;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,WAAW,yEAAU;AACrB;AACA,MAAM,2EAAY,CAAC,6EAAc;AACjC;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,IAAI;AACJ;AACA,0BAA0B,2EAAY,CAAC,6EAAc;AACrD;AACA;AACA;AACA;AACA,MAAM,uEAAQ;AACd;AACA,eAAe,oDAAO;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,YAAY,qEAAM;AAClB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,CAAC;AACD,uCAAuC,qEAAM;AAC7C;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,qEAAM;;AAEN;AACA,2BAA2B,qEAAM;AACjC,8CAA8C,yEAAU;AACxD;AACA;AACA;AACA,UAAU,8BAA8B;AACxC;AACA;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;AAEA;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA,CAAC;AACD,yCAAyC,qEAAM;AAC/C,YAAY,6CAA6C,EAAE,2BAA2B,EAAE;AACxF;AACA,IAAI;AACJ,CAAC;AACD,+BAA+B,qEAAM;AACrC,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM;AACN;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA,CAAC;AACD,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,8BAA8B,qEAAM;AACpC,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,aAAa;AACnB,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,0CAA0C,qEAAM;AAChD,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,iBAAiB;AACvB,MAAM,YAAY;AAClB,MAAM,aAAa;AACnB,MAAM,kBAAkB;AACxB,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA,MAAM,qBAAqB;AAC3B,MAAM,oBAAoB;AAC1B,MAAM,yBAAyB;AAC/B,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,oBAAoB;AAC1B,MAAM,oBAAoB;AAC1B,MAAM,yBAAyB;AAC/B,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qBAAqB;AAC3B,MAAM,wBAAwB;AAC9B,MAAM,qBAAqB;AAC3B,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,gBAAgB;AACtB,MAAM,oBAAoB;AAC1B,MAAM,yBAAyB;AAC/B,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,2CAA2C,qEAAM;AACjD,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAY;AAClB,MAAM,oBAAoB;AAC1B,MAAM,iBAAiB;AACvB,MAAM,qBAAqB;AAC3B,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,2BAA2B,qEAAM;AACjC,UAAU,8BAA8B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8DAAG,+BAA+B,QAAQ;AAChD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC,UAAU,8BAA8B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8DAAG,+BAA+B,QAAQ;AAChD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC,UAAU,WAAW;AACrB,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8DAAG,+BAA+B,QAAQ;AAChD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,oCAAoC,qEAAM;AAC1C;AACA,GAAG;AACH,qCAAqC,qEAAM;AAC3C;AACA,GAAG;AACH;AACA,IAAI,8DAAG;AACP;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,qEAAM;AACN,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,EAAE,8DAAG;AACL;AACA,eAAe;AACf,MAAM,uEAAQ,CAAC,yEAAU;AACzB;AACA,eAAe,oDAAO;AACtB;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA,MAAM,uEAAQ,CAAC,yEAAU;AACzB;AACA,eAAe,oDAAO;AACtB;AACA;AACA;AACA;AACA;AACA,EAAE,oDAAO;AACT;AACA;AACA;AACA;AACA,EAAE,oDAAO;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,8BAA8B,qEAAM;AACpC,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,8BAA8B,qEAAM;AACpC,UAAU,8BAA8B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC,UAAU,8BAA8B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,8DAAG;AACL;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC,UAAU,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAY;AAClB,MAAM,YAAY;AAClB,MAAM,aAAa;AACnB,MAAM,aAAa;AACnB,MAAM,YAAY;AAClB,MAAM,aAAa;AACnB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,cAAc;AACpB,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,0BAA0B,qEAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,uEAAQ,CAAC,yEAAU;AACzB;AACA,eAAe,oDAAO;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,yEAAU;AAClB,+BAA+B,+BAA+B;AAC9D,MAAM;AACN;AACA;AACA;AACA;AACA,EAAE,oDAAO;AACT;AACA,MAAM,uEAAQ,CAAC,yEAAU;AACzB;AACA,eAAe,oDAAO;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,yEAAU;AAClB,iDAAiD,sBAAsB;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,uEAAQ,CAAC,yEAAU;AAC3B;AACA,iBAAiB,oDAAO;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,QAAQ,yEAAU;AAClB,mDAAmD,sBAAsB;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,uEAAQ,CAAC,yEAAU;AAC3B;AACA,iBAAiB,oDAAO;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,IAAI,oDAAO;AACX;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,oDAAO;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oDAAO;AACX;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,IAAI,oDAAO;AACX;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA,QAAQ,yEAAU;AAClB;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC;AACA,EAAE,8DAAG;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,iFAAkB;AACnC;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,wEAAS;AAClC;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA,kBAAkB,wEAAS;AAC3B,gDAAgD,iBAAiB;AACjE;AACA;AACA,eAAe;AACf;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA,oBAAoB,wEAAS;AAC7B,mCAAmC,iBAAiB;AACpD;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;AACN;AACA,gBAAgB,6EAAc;AAC9B;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,0BAA0B;AACtC,YAAY,8EAA8E;AAC1F,YAAY;AACZ;AACA;AACA;AACA,YAAY,2CAA2C;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,oBAAoB,UAAU;AACrE;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,cAAc,yCAAyC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;;AAEN;AACA,kCAAkC,qEAAM;AACxC;AACA,CAAC;AACD,2BAA2B,qEAAM;AACjC,UAAU,6BAA6B,EAAE,wEAAS;AAClD;AACA;AACA;AACA,qBAAqB,oDAAQ;AAC7B;AACA,6CAA6C,oDAAQ,mDAAmD,oDAAQ;AAChH,gEAAgE,GAAG,OAAO,oDAAQ,SAAS,GAAG;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,cAAc;AAC1B,IAAI,+EAAgB;AACpB,IAAI,8DAAG;AACP;AACA;AACA,SAAS,eAAe,EAAE,eAAe,EAAE,oBAAoB,EAAE,oBAAoB;AACrF;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/union.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/graph.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/dagre-d3-es/src/graphlib/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/khroma/dist/methods/channel.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash-es/clone.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/blockDiagram-JOT3LUYC.mjs"], "sourcesContent": ["import baseFlatten from './_baseFlatten.js';\nimport baseRest from './_baseRest.js';\nimport baseUniq from './_baseUniq.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\n\n/**\n * Creates an array of unique values, in order, from all given arrays using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @returns {Array} Returns the new array of combined values.\n * @example\n *\n * _.union([2], [1, 2]);\n * // => [2, 1]\n */\nvar union = baseRest(function(arrays) {\n  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true));\n});\n\nexport default union;\n", "import * as _ from 'lodash-es';\n\nvar DEFAULT_EDGE_NAME = '\\x00';\nvar GRAPH_NODE = '\\x00';\nvar EDGE_KEY_DELIM = '\\x01';\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\nexport class Graph {\n  constructor(opts = {}) {\n    this._isDirected = Object.prototype.hasOwnProperty.call(opts, 'directed')\n      ? opts.directed\n      : true;\n    this._isMultigraph = Object.prototype.hasOwnProperty.call(opts, 'multigraph')\n      ? opts.multigraph\n      : false;\n    this._isCompound = Object.prototype.hasOwnProperty.call(opts, 'compound')\n      ? opts.compound\n      : false;\n\n    // Label for the graph itself\n    this._label = undefined;\n\n    // Defaults to be set when creating a new node\n    this._defaultNodeLabelFn = _.constant(undefined);\n\n    // Defaults to be set when creating a new edge\n    this._defaultEdgeLabelFn = _.constant(undefined);\n\n    // v -> label\n    this._nodes = {};\n\n    if (this._isCompound) {\n      // v -> parent\n      this._parent = {};\n\n      // v -> children\n      this._children = {};\n      this._children[GRAPH_NODE] = {};\n    }\n\n    // v -> edgeObj\n    this._in = {};\n\n    // u -> v -> Number\n    this._preds = {};\n\n    // v -> edgeObj\n    this._out = {};\n\n    // v -> w -> Number\n    this._sucs = {};\n\n    // e -> edgeObj\n    this._edgeObjs = {};\n\n    // e -> label\n    this._edgeLabels = {};\n  }\n  /* === Graph functions ========= */\n  isDirected() {\n    return this._isDirected;\n  }\n  isMultigraph() {\n    return this._isMultigraph;\n  }\n  isCompound() {\n    return this._isCompound;\n  }\n  setGraph(label) {\n    this._label = label;\n    return this;\n  }\n  graph() {\n    return this._label;\n  }\n  /* === Node functions ========== */\n  setDefaultNodeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultNodeLabelFn = newDefault;\n    return this;\n  }\n  nodeCount() {\n    return this._nodeCount;\n  }\n  nodes() {\n    return _.keys(this._nodes);\n  }\n  sources() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._in[v]);\n    });\n  }\n  sinks() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._out[v]);\n    });\n  }\n  setNodes(vs, value) {\n    var args = arguments;\n    var self = this;\n    _.each(vs, function (v) {\n      if (args.length > 1) {\n        self.setNode(v, value);\n      } else {\n        self.setNode(v);\n      }\n    });\n    return this;\n  }\n  setNode(v, value) {\n    if (Object.prototype.hasOwnProperty.call(this._nodes, v)) {\n      if (arguments.length > 1) {\n        this._nodes[v] = value;\n      }\n      return this;\n    }\n\n    // @ts-expect-error\n    this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);\n    if (this._isCompound) {\n      this._parent[v] = GRAPH_NODE;\n      this._children[v] = {};\n      this._children[GRAPH_NODE][v] = true;\n    }\n    this._in[v] = {};\n    this._preds[v] = {};\n    this._out[v] = {};\n    this._sucs[v] = {};\n    ++this._nodeCount;\n    return this;\n  }\n  node(v) {\n    return this._nodes[v];\n  }\n  hasNode(v) {\n    return Object.prototype.hasOwnProperty.call(this._nodes, v);\n  }\n  removeNode(v) {\n    if (Object.prototype.hasOwnProperty.call(this._nodes, v)) {\n      var removeEdge = (e) => this.removeEdge(this._edgeObjs[e]);\n      delete this._nodes[v];\n      if (this._isCompound) {\n        this._removeFromParentsChildList(v);\n        delete this._parent[v];\n        _.each(this.children(v), (child) => {\n          this.setParent(child);\n        });\n        delete this._children[v];\n      }\n      _.each(_.keys(this._in[v]), removeEdge);\n      delete this._in[v];\n      delete this._preds[v];\n      _.each(_.keys(this._out[v]), removeEdge);\n      delete this._out[v];\n      delete this._sucs[v];\n      --this._nodeCount;\n    }\n    return this;\n  }\n  setParent(v, parent) {\n    if (!this._isCompound) {\n      throw new Error('Cannot set parent in a non-compound graph');\n    }\n\n    if (_.isUndefined(parent)) {\n      parent = GRAPH_NODE;\n    } else {\n      // Coerce parent to string\n      parent += '';\n      for (var ancestor = parent; !_.isUndefined(ancestor); ancestor = this.parent(ancestor)) {\n        if (ancestor === v) {\n          throw new Error('Setting ' + parent + ' as parent of ' + v + ' would create a cycle');\n        }\n      }\n\n      this.setNode(parent);\n    }\n\n    this.setNode(v);\n    this._removeFromParentsChildList(v);\n    this._parent[v] = parent;\n    this._children[parent][v] = true;\n    return this;\n  }\n  _removeFromParentsChildList(v) {\n    delete this._children[this._parent[v]][v];\n  }\n  parent(v) {\n    if (this._isCompound) {\n      var parent = this._parent[v];\n      if (parent !== GRAPH_NODE) {\n        return parent;\n      }\n    }\n  }\n  children(v) {\n    if (_.isUndefined(v)) {\n      v = GRAPH_NODE;\n    }\n\n    if (this._isCompound) {\n      var children = this._children[v];\n      if (children) {\n        return _.keys(children);\n      }\n    } else if (v === GRAPH_NODE) {\n      return this.nodes();\n    } else if (this.hasNode(v)) {\n      return [];\n    }\n  }\n  predecessors(v) {\n    var predsV = this._preds[v];\n    if (predsV) {\n      return _.keys(predsV);\n    }\n  }\n  successors(v) {\n    var sucsV = this._sucs[v];\n    if (sucsV) {\n      return _.keys(sucsV);\n    }\n  }\n  neighbors(v) {\n    var preds = this.predecessors(v);\n    if (preds) {\n      return _.union(preds, this.successors(v));\n    }\n  }\n  isLeaf(v) {\n    var neighbors;\n    if (this.isDirected()) {\n      neighbors = this.successors(v);\n    } else {\n      neighbors = this.neighbors(v);\n    }\n    return neighbors.length === 0;\n  }\n  filterNodes(filter) {\n    // @ts-expect-error\n    var copy = new this.constructor({\n      directed: this._isDirected,\n      multigraph: this._isMultigraph,\n      compound: this._isCompound,\n    });\n\n    copy.setGraph(this.graph());\n\n    var self = this;\n    _.each(this._nodes, function (value, v) {\n      if (filter(v)) {\n        copy.setNode(v, value);\n      }\n    });\n\n    _.each(this._edgeObjs, function (e) {\n      // @ts-expect-error\n      if (copy.hasNode(e.v) && copy.hasNode(e.w)) {\n        copy.setEdge(e, self.edge(e));\n      }\n    });\n\n    var parents = {};\n    function findParent(v) {\n      var parent = self.parent(v);\n      if (parent === undefined || copy.hasNode(parent)) {\n        parents[v] = parent;\n        return parent;\n      } else if (parent in parents) {\n        return parents[parent];\n      } else {\n        return findParent(parent);\n      }\n    }\n\n    if (this._isCompound) {\n      _.each(copy.nodes(), function (v) {\n        copy.setParent(v, findParent(v));\n      });\n    }\n\n    return copy;\n  }\n  /* === Edge functions ========== */\n  setDefaultEdgeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultEdgeLabelFn = newDefault;\n    return this;\n  }\n  edgeCount() {\n    return this._edgeCount;\n  }\n  edges() {\n    return _.values(this._edgeObjs);\n  }\n  setPath(vs, value) {\n    var self = this;\n    var args = arguments;\n    _.reduce(vs, function (v, w) {\n      if (args.length > 1) {\n        self.setEdge(v, w, value);\n      } else {\n        self.setEdge(v, w);\n      }\n      return w;\n    });\n    return this;\n  }\n  /*\n   * setEdge(v, w, [value, [name]])\n   * setEdge({ v, w, [name] }, [value])\n   */\n  setEdge() {\n    var v, w, name, value;\n    var valueSpecified = false;\n    var arg0 = arguments[0];\n\n    if (typeof arg0 === 'object' && arg0 !== null && 'v' in arg0) {\n      v = arg0.v;\n      w = arg0.w;\n      name = arg0.name;\n      if (arguments.length === 2) {\n        value = arguments[1];\n        valueSpecified = true;\n      }\n    } else {\n      v = arg0;\n      w = arguments[1];\n      name = arguments[3];\n      if (arguments.length > 2) {\n        value = arguments[2];\n        valueSpecified = true;\n      }\n    }\n\n    v = '' + v;\n    w = '' + w;\n    if (!_.isUndefined(name)) {\n      name = '' + name;\n    }\n\n    var e = edgeArgsToId(this._isDirected, v, w, name);\n    if (Object.prototype.hasOwnProperty.call(this._edgeLabels, e)) {\n      if (valueSpecified) {\n        this._edgeLabels[e] = value;\n      }\n      return this;\n    }\n\n    if (!_.isUndefined(name) && !this._isMultigraph) {\n      throw new Error('Cannot set a named edge when isMultigraph = false');\n    }\n\n    // It didn't exist, so we need to create it.\n    // First ensure the nodes exist.\n    this.setNode(v);\n    this.setNode(w);\n\n    // @ts-expect-error\n    this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);\n\n    var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);\n    // Ensure we add undirected edges in a consistent way.\n    v = edgeObj.v;\n    w = edgeObj.w;\n\n    Object.freeze(edgeObj);\n    this._edgeObjs[e] = edgeObj;\n    incrementOrInitEntry(this._preds[w], v);\n    incrementOrInitEntry(this._sucs[v], w);\n    this._in[w][e] = edgeObj;\n    this._out[v][e] = edgeObj;\n    this._edgeCount++;\n    return this;\n  }\n  edge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return this._edgeLabels[e];\n  }\n  hasEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return Object.prototype.hasOwnProperty.call(this._edgeLabels, e);\n  }\n  removeEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    var edge = this._edgeObjs[e];\n    if (edge) {\n      v = edge.v;\n      w = edge.w;\n      delete this._edgeLabels[e];\n      delete this._edgeObjs[e];\n      decrementOrRemoveEntry(this._preds[w], v);\n      decrementOrRemoveEntry(this._sucs[v], w);\n      delete this._in[w][e];\n      delete this._out[v][e];\n      this._edgeCount--;\n    }\n    return this;\n  }\n  inEdges(v, u) {\n    var inV = this._in[v];\n    if (inV) {\n      var edges = _.values(inV);\n      if (!u) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.v === u;\n      });\n    }\n  }\n  outEdges(v, w) {\n    var outV = this._out[v];\n    if (outV) {\n      var edges = _.values(outV);\n      if (!w) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.w === w;\n      });\n    }\n  }\n  nodeEdges(v, w) {\n    var inEdges = this.inEdges(v, w);\n    if (inEdges) {\n      return inEdges.concat(this.outEdges(v, w));\n    }\n  }\n}\n\n/* Number of nodes in the graph. Should only be changed by the implementation. */\nGraph.prototype._nodeCount = 0;\n\n/* Number of edges in the graph. Should only be changed by the implementation. */\nGraph.prototype._edgeCount = 0;\n\nfunction incrementOrInitEntry(map, k) {\n  if (map[k]) {\n    map[k]++;\n  } else {\n    map[k] = 1;\n  }\n}\n\nfunction decrementOrRemoveEntry(map, k) {\n  if (!--map[k]) {\n    delete map[k];\n  }\n}\n\nfunction edgeArgsToId(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM + (_.isUndefined(name) ? DEFAULT_EDGE_NAME : name);\n}\n\nfunction edgeArgsToObj(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var edgeObj = { v: v, w: w };\n  if (name) {\n    edgeObj.name = name;\n  }\n  return edgeObj;\n}\n\nfunction edgeObjToId(isDirected, edgeObj) {\n  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);\n}\n", "// Includes only the \"core\" of graphlib\n\nimport { Graph } from './graph.js';\n\nconst version = '2.1.9-pre';\n\nexport { Graph, version };\n", "/* IMPORT */\nimport _ from '../utils/index.js';\nimport Color from '../color/index.js';\n/* MAIN */\nconst channel = (color, channel) => {\n    return _.lang.round(Color.parse(color)[channel]);\n};\n/* EXPORT */\nexport default channel;\n", "import baseClone from './_baseClone.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * Creates a shallow clone of `value`.\n *\n * **Note:** This method is loosely based on the\n * [structured clone algorithm](https://mdn.io/Structured_clone_algorithm)\n * and supports cloning arrays, array buffers, booleans, date objects, maps,\n * numbers, `Object` objects, regexes, sets, strings, symbols, and typed\n * arrays. The own enumerable properties of `arguments` objects are cloned\n * as plain objects. An empty object is returned for uncloneable values such\n * as error objects, functions, DOM nodes, and WeakMaps.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to clone.\n * @returns {*} Returns the cloned value.\n * @see _.cloneDeep\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var shallow = _.clone(objects);\n * console.log(shallow[0] === objects[0]);\n * // => true\n */\nfunction clone(value) {\n  return baseClone(value, CLONE_SYMBOLS_FLAG);\n}\n\nexport default clone;\n", "import {\n  getLineFunctionsWithOffset\n} from \"./chunk-VV3M67IP.mjs\";\nimport {\n  getSubGraphTitleMargins\n} from \"./chunk-K557N5IZ.mjs\";\nimport {\n  createText,\n  replaceIconSubstring\n} from \"./chunk-C3MQ5ANM.mjs\";\nimport {\n  decodeEntities,\n  getStylesFromArray,\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  configureSvgSize,\n  evaluate,\n  getConfig,\n  getConfig2,\n  log,\n  sanitizeText\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/block/parser/block.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 7], $V1 = [1, 13], $V2 = [1, 14], $V3 = [1, 15], $V4 = [1, 19], $V5 = [1, 16], $V6 = [1, 17], $V7 = [1, 18], $V8 = [8, 30], $V9 = [8, 21, 28, 29, 30, 31, 32, 40, 44, 47], $Va = [1, 23], $Vb = [1, 24], $Vc = [8, 15, 16, 21, 28, 29, 30, 31, 32, 40, 44, 47], $Vd = [8, 15, 16, 21, 27, 28, 29, 30, 31, 32, 40, 44, 47], $Ve = [1, 49];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"spaceLines\": 3, \"SPACELINE\": 4, \"NL\": 5, \"separator\": 6, \"SPACE\": 7, \"EOF\": 8, \"start\": 9, \"BLOCK_DIAGRAM_KEY\": 10, \"document\": 11, \"stop\": 12, \"statement\": 13, \"link\": 14, \"LINK\": 15, \"START_LINK\": 16, \"LINK_LABEL\": 17, \"STR\": 18, \"nodeStatement\": 19, \"columnsStatement\": 20, \"SPACE_BLOCK\": 21, \"blockStatement\": 22, \"classDefStatement\": 23, \"cssClassStatement\": 24, \"styleStatement\": 25, \"node\": 26, \"SIZE\": 27, \"COLUMNS\": 28, \"id-block\": 29, \"end\": 30, \"block\": 31, \"NODE_ID\": 32, \"nodeShapeNLabel\": 33, \"dirList\": 34, \"DIR\": 35, \"NODE_DSTART\": 36, \"NODE_DEND\": 37, \"BLOCK_ARROW_START\": 38, \"BLOCK_ARROW_END\": 39, \"classDef\": 40, \"CLASSDEF_ID\": 41, \"CLASSDEF_STYLEOPTS\": 42, \"DEFAULT\": 43, \"class\": 44, \"CLASSENTITY_IDS\": 45, \"STYLECLASS\": 46, \"style\": 47, \"STYLE_ENTITY_IDS\": 48, \"STYLE_DEFINITION_DATA\": 49, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACELINE\", 5: \"NL\", 7: \"SPACE\", 8: \"EOF\", 10: \"BLOCK_DIAGRAM_KEY\", 15: \"LINK\", 16: \"START_LINK\", 17: \"LINK_LABEL\", 18: \"STR\", 21: \"SPACE_BLOCK\", 27: \"SIZE\", 28: \"COLUMNS\", 29: \"id-block\", 30: \"end\", 31: \"block\", 32: \"NODE_ID\", 35: \"DIR\", 36: \"NODE_DSTART\", 37: \"NODE_DEND\", 38: \"BLOCK_ARROW_START\", 39: \"BLOCK_ARROW_END\", 40: \"classDef\", 41: \"CLASSDEF_ID\", 42: \"CLASSDEF_STYLEOPTS\", 43: \"DEFAULT\", 44: \"class\", 45: \"CLASSENTITY_IDS\", 46: \"STYLECLASS\", 47: \"style\", 48: \"STYLE_ENTITY_IDS\", 49: \"STYLE_DEFINITION_DATA\" },\n    productions_: [0, [3, 1], [3, 2], [3, 2], [6, 1], [6, 1], [6, 1], [9, 3], [12, 1], [12, 1], [12, 2], [12, 2], [11, 1], [11, 2], [14, 1], [14, 4], [13, 1], [13, 1], [13, 1], [13, 1], [13, 1], [13, 1], [13, 1], [19, 3], [19, 2], [19, 1], [20, 1], [22, 4], [22, 3], [26, 1], [26, 2], [34, 1], [34, 2], [33, 3], [33, 4], [23, 3], [23, 3], [24, 3], [25, 3]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 4:\n          yy.getLogger().debug(\"Rule: separator (NL) \");\n          break;\n        case 5:\n          yy.getLogger().debug(\"Rule: separator (Space) \");\n          break;\n        case 6:\n          yy.getLogger().debug(\"Rule: separator (EOF) \");\n          break;\n        case 7:\n          yy.getLogger().debug(\"Rule: hierarchy: \", $$[$0 - 1]);\n          yy.setHierarchy($$[$0 - 1]);\n          break;\n        case 8:\n          yy.getLogger().debug(\"Stop NL \");\n          break;\n        case 9:\n          yy.getLogger().debug(\"Stop EOF \");\n          break;\n        case 10:\n          yy.getLogger().debug(\"Stop NL2 \");\n          break;\n        case 11:\n          yy.getLogger().debug(\"Stop EOF2 \");\n          break;\n        case 12:\n          yy.getLogger().debug(\"Rule: statement: \", $$[$0]);\n          typeof $$[$0].length === \"number\" ? this.$ = $$[$0] : this.$ = [$$[$0]];\n          break;\n        case 13:\n          yy.getLogger().debug(\"Rule: statement #2: \", $$[$0 - 1]);\n          this.$ = [$$[$0 - 1]].concat($$[$0]);\n          break;\n        case 14:\n          yy.getLogger().debug(\"Rule: link: \", $$[$0], yytext);\n          this.$ = { edgeTypeStr: $$[$0], label: \"\" };\n          break;\n        case 15:\n          yy.getLogger().debug(\"Rule: LABEL link: \", $$[$0 - 3], $$[$0 - 1], $$[$0]);\n          this.$ = { edgeTypeStr: $$[$0], label: $$[$0 - 1] };\n          break;\n        case 18:\n          const num = parseInt($$[$0]);\n          const spaceId = yy.generateId();\n          this.$ = { id: spaceId, type: \"space\", label: \"\", width: num, children: [] };\n          break;\n        case 23:\n          yy.getLogger().debug(\"Rule: (nodeStatement link node) \", $$[$0 - 2], $$[$0 - 1], $$[$0], \" typestr: \", $$[$0 - 1].edgeTypeStr);\n          const edgeData = yy.edgeStrToEdgeData($$[$0 - 1].edgeTypeStr);\n          this.$ = [\n            { id: $$[$0 - 2].id, label: $$[$0 - 2].label, type: $$[$0 - 2].type, directions: $$[$0 - 2].directions },\n            { id: $$[$0 - 2].id + \"-\" + $$[$0].id, start: $$[$0 - 2].id, end: $$[$0].id, label: $$[$0 - 1].label, type: \"edge\", directions: $$[$0].directions, arrowTypeEnd: edgeData, arrowTypeStart: \"arrow_open\" },\n            { id: $$[$0].id, label: $$[$0].label, type: yy.typeStr2Type($$[$0].typeStr), directions: $$[$0].directions }\n          ];\n          break;\n        case 24:\n          yy.getLogger().debug(\"Rule: nodeStatement (abc88 node size) \", $$[$0 - 1], $$[$0]);\n          this.$ = { id: $$[$0 - 1].id, label: $$[$0 - 1].label, type: yy.typeStr2Type($$[$0 - 1].typeStr), directions: $$[$0 - 1].directions, widthInColumns: parseInt($$[$0], 10) };\n          break;\n        case 25:\n          yy.getLogger().debug(\"Rule: nodeStatement (node) \", $$[$0]);\n          this.$ = { id: $$[$0].id, label: $$[$0].label, type: yy.typeStr2Type($$[$0].typeStr), directions: $$[$0].directions, widthInColumns: 1 };\n          break;\n        case 26:\n          yy.getLogger().debug(\"APA123\", this ? this : \"na\");\n          yy.getLogger().debug(\"COLUMNS: \", $$[$0]);\n          this.$ = { type: \"column-setting\", columns: $$[$0] === \"auto\" ? -1 : parseInt($$[$0]) };\n          break;\n        case 27:\n          yy.getLogger().debug(\"Rule: id-block statement : \", $$[$0 - 2], $$[$0 - 1]);\n          const id2 = yy.generateId();\n          this.$ = { ...$$[$0 - 2], type: \"composite\", children: $$[$0 - 1] };\n          break;\n        case 28:\n          yy.getLogger().debug(\"Rule: blockStatement : \", $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          const id = yy.generateId();\n          this.$ = { id, type: \"composite\", label: \"\", children: $$[$0 - 1] };\n          break;\n        case 29:\n          yy.getLogger().debug(\"Rule: node (NODE_ID separator): \", $$[$0]);\n          this.$ = { id: $$[$0] };\n          break;\n        case 30:\n          yy.getLogger().debug(\"Rule: node (NODE_ID nodeShapeNLabel separator): \", $$[$0 - 1], $$[$0]);\n          this.$ = { id: $$[$0 - 1], label: $$[$0].label, typeStr: $$[$0].typeStr, directions: $$[$0].directions };\n          break;\n        case 31:\n          yy.getLogger().debug(\"Rule: dirList: \", $$[$0]);\n          this.$ = [$$[$0]];\n          break;\n        case 32:\n          yy.getLogger().debug(\"Rule: dirList: \", $$[$0 - 1], $$[$0]);\n          this.$ = [$$[$0 - 1]].concat($$[$0]);\n          break;\n        case 33:\n          yy.getLogger().debug(\"Rule: nodeShapeNLabel: \", $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          this.$ = { typeStr: $$[$0 - 2] + $$[$0], label: $$[$0 - 1] };\n          break;\n        case 34:\n          yy.getLogger().debug(\"Rule: BLOCK_ARROW nodeShapeNLabel: \", $$[$0 - 3], $$[$0 - 2], \" #3:\", $$[$0 - 1], $$[$0]);\n          this.$ = { typeStr: $$[$0 - 3] + $$[$0], label: $$[$0 - 2], directions: $$[$0 - 1] };\n          break;\n        case 35:\n        case 36:\n          this.$ = { type: \"classDef\", id: $$[$0 - 1].trim(), css: $$[$0].trim() };\n          break;\n        case 37:\n          this.$ = { type: \"applyClass\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 38:\n          this.$ = { type: \"applyStyles\", id: $$[$0 - 1].trim(), stylesStr: $$[$0].trim() };\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 9: 1, 10: [1, 2] }, { 1: [3] }, { 11: 3, 13: 4, 19: 5, 20: 6, 21: $V0, 22: 8, 23: 9, 24: 10, 25: 11, 26: 12, 28: $V1, 29: $V2, 31: $V3, 32: $V4, 40: $V5, 44: $V6, 47: $V7 }, { 8: [1, 20] }, o($V8, [2, 12], { 13: 4, 19: 5, 20: 6, 22: 8, 23: 9, 24: 10, 25: 11, 26: 12, 11: 21, 21: $V0, 28: $V1, 29: $V2, 31: $V3, 32: $V4, 40: $V5, 44: $V6, 47: $V7 }), o($V9, [2, 16], { 14: 22, 15: $Va, 16: $Vb }), o($V9, [2, 17]), o($V9, [2, 18]), o($V9, [2, 19]), o($V9, [2, 20]), o($V9, [2, 21]), o($V9, [2, 22]), o($Vc, [2, 25], { 27: [1, 25] }), o($V9, [2, 26]), { 19: 26, 26: 12, 32: $V4 }, { 11: 27, 13: 4, 19: 5, 20: 6, 21: $V0, 22: 8, 23: 9, 24: 10, 25: 11, 26: 12, 28: $V1, 29: $V2, 31: $V3, 32: $V4, 40: $V5, 44: $V6, 47: $V7 }, { 41: [1, 28], 43: [1, 29] }, { 45: [1, 30] }, { 48: [1, 31] }, o($Vd, [2, 29], { 33: 32, 36: [1, 33], 38: [1, 34] }), { 1: [2, 7] }, o($V8, [2, 13]), { 26: 35, 32: $V4 }, { 32: [2, 14] }, { 17: [1, 36] }, o($Vc, [2, 24]), { 11: 37, 13: 4, 14: 22, 15: $Va, 16: $Vb, 19: 5, 20: 6, 21: $V0, 22: 8, 23: 9, 24: 10, 25: 11, 26: 12, 28: $V1, 29: $V2, 31: $V3, 32: $V4, 40: $V5, 44: $V6, 47: $V7 }, { 30: [1, 38] }, { 42: [1, 39] }, { 42: [1, 40] }, { 46: [1, 41] }, { 49: [1, 42] }, o($Vd, [2, 30]), { 18: [1, 43] }, { 18: [1, 44] }, o($Vc, [2, 23]), { 18: [1, 45] }, { 30: [1, 46] }, o($V9, [2, 28]), o($V9, [2, 35]), o($V9, [2, 36]), o($V9, [2, 37]), o($V9, [2, 38]), { 37: [1, 47] }, { 34: 48, 35: $Ve }, { 15: [1, 50] }, o($V9, [2, 27]), o($Vd, [2, 33]), { 39: [1, 51] }, { 34: 52, 35: $Ve, 39: [2, 31] }, { 32: [2, 15] }, o($Vd, [2, 34]), { 39: [2, 32] }],\n    defaultActions: { 20: [2, 7], 23: [2, 14], 50: [2, 15], 52: [2, 32] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {},\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 10;\n            break;\n          case 1:\n            yy.getLogger().debug(\"Found space-block\");\n            return 31;\n            break;\n          case 2:\n            yy.getLogger().debug(\"Found nl-block\");\n            return 31;\n            break;\n          case 3:\n            yy.getLogger().debug(\"Found space-block\");\n            return 29;\n            break;\n          case 4:\n            yy.getLogger().debug(\".\", yy_.yytext);\n            break;\n          case 5:\n            yy.getLogger().debug(\"_\", yy_.yytext);\n            break;\n          case 6:\n            return 5;\n            break;\n          case 7:\n            yy_.yytext = -1;\n            return 28;\n            break;\n          case 8:\n            yy_.yytext = yy_.yytext.replace(/columns\\s+/, \"\");\n            yy.getLogger().debug(\"COLUMNS (LEX)\", yy_.yytext);\n            return 28;\n            break;\n          case 9:\n            this.pushState(\"md_string\");\n            break;\n          case 10:\n            return \"MD_STR\";\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            this.pushState(\"string\");\n            break;\n          case 13:\n            yy.getLogger().debug(\"LEX: POPPING STR:\", yy_.yytext);\n            this.popState();\n            break;\n          case 14:\n            yy.getLogger().debug(\"LEX: STR end:\", yy_.yytext);\n            return \"STR\";\n            break;\n          case 15:\n            yy_.yytext = yy_.yytext.replace(/space\\:/, \"\");\n            yy.getLogger().debug(\"SPACE NUM (LEX)\", yy_.yytext);\n            return 21;\n            break;\n          case 16:\n            yy_.yytext = \"1\";\n            yy.getLogger().debug(\"COLUMNS (LEX)\", yy_.yytext);\n            return 21;\n            break;\n          case 17:\n            return 43;\n            break;\n          case 18:\n            return \"LINKSTYLE\";\n            break;\n          case 19:\n            return \"INTERPOLATE\";\n            break;\n          case 20:\n            this.pushState(\"CLASSDEF\");\n            return 40;\n            break;\n          case 21:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return \"DEFAULT_CLASSDEF_ID\";\n            break;\n          case 22:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return 41;\n            break;\n          case 23:\n            this.popState();\n            return 42;\n            break;\n          case 24:\n            this.pushState(\"CLASS\");\n            return 44;\n            break;\n          case 25:\n            this.popState();\n            this.pushState(\"CLASS_STYLE\");\n            return 45;\n            break;\n          case 26:\n            this.popState();\n            return 46;\n            break;\n          case 27:\n            this.pushState(\"STYLE_STMNT\");\n            return 47;\n            break;\n          case 28:\n            this.popState();\n            this.pushState(\"STYLE_DEFINITION\");\n            return 48;\n            break;\n          case 29:\n            this.popState();\n            return 49;\n            break;\n          case 30:\n            this.pushState(\"acc_title\");\n            return \"acc_title\";\n            break;\n          case 31:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 32:\n            this.pushState(\"acc_descr\");\n            return \"acc_descr\";\n            break;\n          case 33:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 34:\n            this.pushState(\"acc_descr_multiline\");\n            break;\n          case 35:\n            this.popState();\n            break;\n          case 36:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 37:\n            return 30;\n            break;\n          case 38:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ((\");\n            return \"NODE_DEND\";\n            break;\n          case 39:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ((\");\n            return \"NODE_DEND\";\n            break;\n          case 40:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ))\");\n            return \"NODE_DEND\";\n            break;\n          case 41:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ((\");\n            return \"NODE_DEND\";\n            break;\n          case 42:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ((\");\n            return \"NODE_DEND\";\n            break;\n          case 43:\n            this.popState();\n            yy.getLogger().debug(\"Lex: (-\");\n            return \"NODE_DEND\";\n            break;\n          case 44:\n            this.popState();\n            yy.getLogger().debug(\"Lex: -)\");\n            return \"NODE_DEND\";\n            break;\n          case 45:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ((\");\n            return \"NODE_DEND\";\n            break;\n          case 46:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ]]\");\n            return \"NODE_DEND\";\n            break;\n          case 47:\n            this.popState();\n            yy.getLogger().debug(\"Lex: (\");\n            return \"NODE_DEND\";\n            break;\n          case 48:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ])\");\n            return \"NODE_DEND\";\n            break;\n          case 49:\n            this.popState();\n            yy.getLogger().debug(\"Lex: /]\");\n            return \"NODE_DEND\";\n            break;\n          case 50:\n            this.popState();\n            yy.getLogger().debug(\"Lex: /]\");\n            return \"NODE_DEND\";\n            break;\n          case 51:\n            this.popState();\n            yy.getLogger().debug(\"Lex: )]\");\n            return \"NODE_DEND\";\n            break;\n          case 52:\n            this.popState();\n            yy.getLogger().debug(\"Lex: )\");\n            return \"NODE_DEND\";\n            break;\n          case 53:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ]>\");\n            return \"NODE_DEND\";\n            break;\n          case 54:\n            this.popState();\n            yy.getLogger().debug(\"Lex: ]\");\n            return \"NODE_DEND\";\n            break;\n          case 55:\n            yy.getLogger().debug(\"Lexa: -)\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 56:\n            yy.getLogger().debug(\"Lexa: (-\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 57:\n            yy.getLogger().debug(\"Lexa: ))\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 58:\n            yy.getLogger().debug(\"Lexa: )\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 59:\n            yy.getLogger().debug(\"Lex: (((\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 60:\n            yy.getLogger().debug(\"Lexa: )\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 61:\n            yy.getLogger().debug(\"Lexa: )\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 62:\n            yy.getLogger().debug(\"Lexa: )\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 63:\n            yy.getLogger().debug(\"Lexc: >\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 64:\n            yy.getLogger().debug(\"Lexa: ([\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 65:\n            yy.getLogger().debug(\"Lexa: )\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 66:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 67:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 68:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 69:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 70:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 71:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 72:\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 73:\n            yy.getLogger().debug(\"Lexa: [\");\n            this.pushState(\"NODE\");\n            return 36;\n            break;\n          case 74:\n            this.pushState(\"BLOCK_ARROW\");\n            yy.getLogger().debug(\"LEX ARR START\");\n            return 38;\n            break;\n          case 75:\n            yy.getLogger().debug(\"Lex: NODE_ID\", yy_.yytext);\n            return 32;\n            break;\n          case 76:\n            yy.getLogger().debug(\"Lex: EOF\", yy_.yytext);\n            return 8;\n            break;\n          case 77:\n            this.pushState(\"md_string\");\n            break;\n          case 78:\n            this.pushState(\"md_string\");\n            break;\n          case 79:\n            return \"NODE_DESCR\";\n            break;\n          case 80:\n            this.popState();\n            break;\n          case 81:\n            yy.getLogger().debug(\"Lex: Starting string\");\n            this.pushState(\"string\");\n            break;\n          case 82:\n            yy.getLogger().debug(\"LEX ARR: Starting string\");\n            this.pushState(\"string\");\n            break;\n          case 83:\n            yy.getLogger().debug(\"LEX: NODE_DESCR:\", yy_.yytext);\n            return \"NODE_DESCR\";\n            break;\n          case 84:\n            yy.getLogger().debug(\"LEX POPPING\");\n            this.popState();\n            break;\n          case 85:\n            yy.getLogger().debug(\"Lex: =>BAE\");\n            this.pushState(\"ARROW_DIR\");\n            break;\n          case 86:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (right): dir:\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 87:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (left):\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 88:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (x):\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 89:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (y):\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 90:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (up):\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 91:\n            yy_.yytext = yy_.yytext.replace(/^,\\s*/, \"\");\n            yy.getLogger().debug(\"Lex (down):\", yy_.yytext);\n            return \"DIR\";\n            break;\n          case 92:\n            yy_.yytext = \"]>\";\n            yy.getLogger().debug(\"Lex (ARROW_DIR end):\", yy_.yytext);\n            this.popState();\n            this.popState();\n            return \"BLOCK_ARROW_END\";\n            break;\n          case 93:\n            yy.getLogger().debug(\"Lex: LINK\", \"#\" + yy_.yytext + \"#\");\n            return 15;\n            break;\n          case 94:\n            yy.getLogger().debug(\"Lex: LINK\", yy_.yytext);\n            return 15;\n            break;\n          case 95:\n            yy.getLogger().debug(\"Lex: LINK\", yy_.yytext);\n            return 15;\n            break;\n          case 96:\n            yy.getLogger().debug(\"Lex: LINK\", yy_.yytext);\n            return 15;\n            break;\n          case 97:\n            yy.getLogger().debug(\"Lex: START_LINK\", yy_.yytext);\n            this.pushState(\"LLABEL\");\n            return 16;\n            break;\n          case 98:\n            yy.getLogger().debug(\"Lex: START_LINK\", yy_.yytext);\n            this.pushState(\"LLABEL\");\n            return 16;\n            break;\n          case 99:\n            yy.getLogger().debug(\"Lex: START_LINK\", yy_.yytext);\n            this.pushState(\"LLABEL\");\n            return 16;\n            break;\n          case 100:\n            this.pushState(\"md_string\");\n            break;\n          case 101:\n            yy.getLogger().debug(\"Lex: Starting string\");\n            this.pushState(\"string\");\n            return \"LINK_LABEL\";\n            break;\n          case 102:\n            this.popState();\n            yy.getLogger().debug(\"Lex: LINK\", \"#\" + yy_.yytext + \"#\");\n            return 15;\n            break;\n          case 103:\n            this.popState();\n            yy.getLogger().debug(\"Lex: LINK\", yy_.yytext);\n            return 15;\n            break;\n          case 104:\n            this.popState();\n            yy.getLogger().debug(\"Lex: LINK\", yy_.yytext);\n            return 15;\n            break;\n          case 105:\n            yy.getLogger().debug(\"Lex: COLON\", yy_.yytext);\n            yy_.yytext = yy_.yytext.slice(1);\n            return 27;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:block-beta\\b)/, /^(?:block\\s+)/, /^(?:block\\n+)/, /^(?:block:)/, /^(?:[\\s]+)/, /^(?:[\\n]+)/, /^(?:((\\u000D\\u000A)|(\\u000A)))/, /^(?:columns\\s+auto\\b)/, /^(?:columns\\s+[\\d]+)/, /^(?:[\"][`])/, /^(?:[^`\"]+)/, /^(?:[`][\"])/, /^(?:[\"])/, /^(?:[\"])/, /^(?:[^\"]*)/, /^(?:space[:]\\d+)/, /^(?:space\\b)/, /^(?:default\\b)/, /^(?:linkStyle\\b)/, /^(?:interpolate\\b)/, /^(?:classDef\\s+)/, /^(?:DEFAULT\\s+)/, /^(?:\\w+\\s+)/, /^(?:[^\\n]*)/, /^(?:class\\s+)/, /^(?:(\\w+)+((,\\s*\\w+)*))/, /^(?:[^\\n]*)/, /^(?:style\\s+)/, /^(?:(\\w+)+((,\\s*\\w+)*))/, /^(?:[^\\n]*)/, /^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:end\\b\\s*)/, /^(?:\\(\\(\\()/, /^(?:\\)\\)\\))/, /^(?:[\\)]\\))/, /^(?:\\}\\})/, /^(?:\\})/, /^(?:\\(-)/, /^(?:-\\))/, /^(?:\\(\\()/, /^(?:\\]\\])/, /^(?:\\()/, /^(?:\\]\\))/, /^(?:\\\\\\])/, /^(?:\\/\\])/, /^(?:\\)\\])/, /^(?:[\\)])/, /^(?:\\]>)/, /^(?:[\\]])/, /^(?:-\\))/, /^(?:\\(-)/, /^(?:\\)\\))/, /^(?:\\))/, /^(?:\\(\\(\\()/, /^(?:\\(\\()/, /^(?:\\{\\{)/, /^(?:\\{)/, /^(?:>)/, /^(?:\\(\\[)/, /^(?:\\()/, /^(?:\\[\\[)/, /^(?:\\[\\|)/, /^(?:\\[\\()/, /^(?:\\)\\)\\))/, /^(?:\\[\\\\)/, /^(?:\\[\\/)/, /^(?:\\[\\\\)/, /^(?:\\[)/, /^(?:<\\[)/, /^(?:[^\\(\\[\\n\\-\\)\\{\\}\\s\\<\\>:]+)/, /^(?:$)/, /^(?:[\"][`])/, /^(?:[\"][`])/, /^(?:[^`\"]+)/, /^(?:[`][\"])/, /^(?:[\"])/, /^(?:[\"])/, /^(?:[^\"]+)/, /^(?:[\"])/, /^(?:\\]>\\s*\\()/, /^(?:,?\\s*right\\s*)/, /^(?:,?\\s*left\\s*)/, /^(?:,?\\s*x\\s*)/, /^(?:,?\\s*y\\s*)/, /^(?:,?\\s*up\\s*)/, /^(?:,?\\s*down\\s*)/, /^(?:\\)\\s*)/, /^(?:\\s*[xo<]?--+[-xo>]\\s*)/, /^(?:\\s*[xo<]?==+[=xo>]\\s*)/, /^(?:\\s*[xo<]?-?\\.+-[xo>]?\\s*)/, /^(?:\\s*~~[\\~]+\\s*)/, /^(?:\\s*[xo<]?--\\s*)/, /^(?:\\s*[xo<]?==\\s*)/, /^(?:\\s*[xo<]?-\\.\\s*)/, /^(?:[\"][`])/, /^(?:[\"])/, /^(?:\\s*[xo<]?--+[-xo>]\\s*)/, /^(?:\\s*[xo<]?==+[=xo>]\\s*)/, /^(?:\\s*[xo<]?-?\\.+-[xo>]?\\s*)/, /^(?::\\d+)/],\n      conditions: { \"STYLE_DEFINITION\": { \"rules\": [29], \"inclusive\": false }, \"STYLE_STMNT\": { \"rules\": [28], \"inclusive\": false }, \"CLASSDEFID\": { \"rules\": [23], \"inclusive\": false }, \"CLASSDEF\": { \"rules\": [21, 22], \"inclusive\": false }, \"CLASS_STYLE\": { \"rules\": [26], \"inclusive\": false }, \"CLASS\": { \"rules\": [25], \"inclusive\": false }, \"LLABEL\": { \"rules\": [100, 101, 102, 103, 104], \"inclusive\": false }, \"ARROW_DIR\": { \"rules\": [86, 87, 88, 89, 90, 91, 92], \"inclusive\": false }, \"BLOCK_ARROW\": { \"rules\": [77, 82, 85], \"inclusive\": false }, \"NODE\": { \"rules\": [38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 78, 81], \"inclusive\": false }, \"md_string\": { \"rules\": [10, 11, 79, 80], \"inclusive\": false }, \"space\": { \"rules\": [], \"inclusive\": false }, \"string\": { \"rules\": [13, 14, 83, 84], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [35, 36], \"inclusive\": false }, \"acc_descr\": { \"rules\": [33], \"inclusive\": false }, \"acc_title\": { \"rules\": [31], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 15, 16, 17, 18, 19, 20, 24, 27, 30, 32, 34, 37, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 93, 94, 95, 96, 97, 98, 99, 105], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar block_default = parser;\n\n// src/diagrams/block/blockDB.ts\nimport clone from \"lodash-es/clone.js\";\nvar blockDatabase = /* @__PURE__ */ new Map();\nvar edgeList = [];\nvar edgeCount = /* @__PURE__ */ new Map();\nvar COLOR_KEYWORD = \"color\";\nvar FILL_KEYWORD = \"fill\";\nvar BG_FILL = \"bgFill\";\nvar STYLECLASS_SEP = \",\";\nvar config = getConfig2();\nvar classes = /* @__PURE__ */ new Map();\nvar sanitizeText2 = /* @__PURE__ */ __name((txt) => common_default.sanitizeText(txt, config), \"sanitizeText\");\nvar addStyleClass = /* @__PURE__ */ __name(function(id, styleAttributes = \"\") {\n  let foundClass = classes.get(id);\n  if (!foundClass) {\n    foundClass = { id, styles: [], textStyles: [] };\n    classes.set(id, foundClass);\n  }\n  if (styleAttributes !== void 0 && styleAttributes !== null) {\n    styleAttributes.split(STYLECLASS_SEP).forEach((attrib) => {\n      const fixedAttrib = attrib.replace(/([^;]*);/, \"$1\").trim();\n      if (RegExp(COLOR_KEYWORD).exec(attrib)) {\n        const newStyle1 = fixedAttrib.replace(FILL_KEYWORD, BG_FILL);\n        const newStyle2 = newStyle1.replace(COLOR_KEYWORD, FILL_KEYWORD);\n        foundClass.textStyles.push(newStyle2);\n      }\n      foundClass.styles.push(fixedAttrib);\n    });\n  }\n}, \"addStyleClass\");\nvar addStyle2Node = /* @__PURE__ */ __name(function(id, styles = \"\") {\n  const foundBlock = blockDatabase.get(id);\n  if (styles !== void 0 && styles !== null) {\n    foundBlock.styles = styles.split(STYLECLASS_SEP);\n  }\n}, \"addStyle2Node\");\nvar setCssClass = /* @__PURE__ */ __name(function(itemIds, cssClassName) {\n  itemIds.split(\",\").forEach(function(id) {\n    let foundBlock = blockDatabase.get(id);\n    if (foundBlock === void 0) {\n      const trimmedId = id.trim();\n      foundBlock = { id: trimmedId, type: \"na\", children: [] };\n      blockDatabase.set(trimmedId, foundBlock);\n    }\n    if (!foundBlock.classes) {\n      foundBlock.classes = [];\n    }\n    foundBlock.classes.push(cssClassName);\n  });\n}, \"setCssClass\");\nvar populateBlockDatabase = /* @__PURE__ */ __name((_blockList, parent) => {\n  const blockList = _blockList.flat();\n  const children = [];\n  for (const block of blockList) {\n    if (block.label) {\n      block.label = sanitizeText2(block.label);\n    }\n    if (block.type === \"classDef\") {\n      addStyleClass(block.id, block.css);\n      continue;\n    }\n    if (block.type === \"applyClass\") {\n      setCssClass(block.id, block?.styleClass ?? \"\");\n      continue;\n    }\n    if (block.type === \"applyStyles\") {\n      if (block?.stylesStr) {\n        addStyle2Node(block.id, block?.stylesStr);\n      }\n      continue;\n    }\n    if (block.type === \"column-setting\") {\n      parent.columns = block.columns ?? -1;\n    } else if (block.type === \"edge\") {\n      const count = (edgeCount.get(block.id) ?? 0) + 1;\n      edgeCount.set(block.id, count);\n      block.id = count + \"-\" + block.id;\n      edgeList.push(block);\n    } else {\n      if (!block.label) {\n        if (block.type === \"composite\") {\n          block.label = \"\";\n        } else {\n          block.label = block.id;\n        }\n      }\n      const existingBlock = blockDatabase.get(block.id);\n      if (existingBlock === void 0) {\n        blockDatabase.set(block.id, block);\n      } else {\n        if (block.type !== \"na\") {\n          existingBlock.type = block.type;\n        }\n        if (block.label !== block.id) {\n          existingBlock.label = block.label;\n        }\n      }\n      if (block.children) {\n        populateBlockDatabase(block.children, block);\n      }\n      if (block.type === \"space\") {\n        const w = block.width ?? 1;\n        for (let j = 0; j < w; j++) {\n          const newBlock = clone(block);\n          newBlock.id = newBlock.id + \"-\" + j;\n          blockDatabase.set(newBlock.id, newBlock);\n          children.push(newBlock);\n        }\n      } else if (existingBlock === void 0) {\n        children.push(block);\n      }\n    }\n  }\n  parent.children = children;\n}, \"populateBlockDatabase\");\nvar blocks = [];\nvar rootBlock = { id: \"root\", type: \"composite\", children: [], columns: -1 };\nvar clear2 = /* @__PURE__ */ __name(() => {\n  log.debug(\"Clear called\");\n  clear();\n  rootBlock = { id: \"root\", type: \"composite\", children: [], columns: -1 };\n  blockDatabase = /* @__PURE__ */ new Map([[\"root\", rootBlock]]);\n  blocks = [];\n  classes = /* @__PURE__ */ new Map();\n  edgeList = [];\n  edgeCount = /* @__PURE__ */ new Map();\n}, \"clear\");\nfunction typeStr2Type(typeStr) {\n  log.debug(\"typeStr2Type\", typeStr);\n  switch (typeStr) {\n    case \"[]\":\n      return \"square\";\n    case \"()\":\n      log.debug(\"we have a round\");\n      return \"round\";\n    case \"(())\":\n      return \"circle\";\n    case \">]\":\n      return \"rect_left_inv_arrow\";\n    case \"{}\":\n      return \"diamond\";\n    case \"{{}}\":\n      return \"hexagon\";\n    case \"([])\":\n      return \"stadium\";\n    case \"[[]]\":\n      return \"subroutine\";\n    case \"[()]\":\n      return \"cylinder\";\n    case \"((()))\":\n      return \"doublecircle\";\n    case \"[//]\":\n      return \"lean_right\";\n    case \"[\\\\\\\\]\":\n      return \"lean_left\";\n    case \"[/\\\\]\":\n      return \"trapezoid\";\n    case \"[\\\\/]\":\n      return \"inv_trapezoid\";\n    case \"<[]>\":\n      return \"block_arrow\";\n    default:\n      return \"na\";\n  }\n}\n__name(typeStr2Type, \"typeStr2Type\");\nfunction edgeTypeStr2Type(typeStr) {\n  log.debug(\"typeStr2Type\", typeStr);\n  switch (typeStr) {\n    case \"==\":\n      return \"thick\";\n    default:\n      return \"normal\";\n  }\n}\n__name(edgeTypeStr2Type, \"edgeTypeStr2Type\");\nfunction edgeStrToEdgeData(typeStr) {\n  switch (typeStr.trim()) {\n    case \"--x\":\n      return \"arrow_cross\";\n    case \"--o\":\n      return \"arrow_circle\";\n    default:\n      return \"arrow_point\";\n  }\n}\n__name(edgeStrToEdgeData, \"edgeStrToEdgeData\");\nvar cnt = 0;\nvar generateId = /* @__PURE__ */ __name(() => {\n  cnt++;\n  return \"id-\" + Math.random().toString(36).substr(2, 12) + \"-\" + cnt;\n}, \"generateId\");\nvar setHierarchy = /* @__PURE__ */ __name((block) => {\n  rootBlock.children = block;\n  populateBlockDatabase(block, rootBlock);\n  blocks = rootBlock.children;\n}, \"setHierarchy\");\nvar getColumns = /* @__PURE__ */ __name((blockId) => {\n  const block = blockDatabase.get(blockId);\n  if (!block) {\n    return -1;\n  }\n  if (block.columns) {\n    return block.columns;\n  }\n  if (!block.children) {\n    return -1;\n  }\n  return block.children.length;\n}, \"getColumns\");\nvar getBlocksFlat = /* @__PURE__ */ __name(() => {\n  return [...blockDatabase.values()];\n}, \"getBlocksFlat\");\nvar getBlocks = /* @__PURE__ */ __name(() => {\n  return blocks || [];\n}, \"getBlocks\");\nvar getEdges = /* @__PURE__ */ __name(() => {\n  return edgeList;\n}, \"getEdges\");\nvar getBlock = /* @__PURE__ */ __name((id) => {\n  return blockDatabase.get(id);\n}, \"getBlock\");\nvar setBlock = /* @__PURE__ */ __name((block) => {\n  blockDatabase.set(block.id, block);\n}, \"setBlock\");\nvar getLogger = /* @__PURE__ */ __name(() => console, \"getLogger\");\nvar getClasses = /* @__PURE__ */ __name(function() {\n  return classes;\n}, \"getClasses\");\nvar db = {\n  getConfig: /* @__PURE__ */ __name(() => getConfig().block, \"getConfig\"),\n  typeStr2Type,\n  edgeTypeStr2Type,\n  edgeStrToEdgeData,\n  getLogger,\n  getBlocksFlat,\n  getBlocks,\n  getEdges,\n  setHierarchy,\n  getBlock,\n  setBlock,\n  getColumns,\n  getClasses,\n  clear: clear2,\n  generateId\n};\nvar blockDB_default = db;\n\n// src/diagrams/block/styles.ts\nimport * as khroma from \"khroma\";\nvar fade = /* @__PURE__ */ __name((color, opacity) => {\n  const channel2 = khroma.channel;\n  const r = channel2(color, \"r\");\n  const g = channel2(color, \"g\");\n  const b = channel2(color, \"b\");\n  return khroma.rgba(r, g, b, opacity);\n}, \"fade\");\nvar getStyles = /* @__PURE__ */ __name((options) => `.label {\n    font-family: ${options.fontFamily};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .cluster-label text {\n    fill: ${options.titleColor};\n  }\n  .cluster-label span,p {\n    color: ${options.titleColor};\n  }\n\n\n\n  .label text,span,p {\n    fill: ${options.nodeTextColor || options.textColor};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n  .flowchart-label text {\n    text-anchor: middle;\n  }\n  // .flowchart-label .text-outer-tspan {\n  //   text-anchor: middle;\n  // }\n  // .flowchart-label .text-inner-tspan {\n  //   text-anchor: start;\n  // }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 2.0px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n      background-color: ${options.edgeLabelBackground};\n      fill: ${options.edgeLabelBackground};\n    }\n    text-align: center;\n  }\n\n  /* For html labels only */\n  .labelBkg {\n    background-color: ${fade(options.edgeLabelBackground, 0.5)};\n    // background-color:\n  }\n\n  .node .cluster {\n    // fill: ${fade(options.mainBkg, 0.5)};\n    fill: ${fade(options.clusterBkg, 0.5)};\n    stroke: ${fade(options.clusterBorder, 0.2)};\n    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;\n    stroke-width: 1px;\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  .cluster span,p {\n    color: ${options.titleColor};\n  }\n  /* .cluster div {\n    color: ${options.titleColor};\n  } */\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${options.fontFamily};\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .flowchartTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.textColor};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/block/blockRenderer.ts\nimport { select as d3select } from \"d3\";\n\n// src/dagre-wrapper/markers.js\nvar insertMarkers = /* @__PURE__ */ __name((elem, markerArray, type, id) => {\n  markerArray.forEach((markerName) => {\n    markers[markerName](elem, type, id);\n  });\n}, \"insertMarkers\");\nvar extension = /* @__PURE__ */ __name((elem, type, id) => {\n  log.trace(\"Making markers for \", id);\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-extensionStart\").attr(\"class\", \"marker extension \" + type).attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 1,7 L18,13 V 1 Z\");\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-extensionEnd\").attr(\"class\", \"marker extension \" + type).attr(\"refX\", 1).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 1,1 V 13 L18,7 Z\");\n}, \"extension\");\nvar composition = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-compositionStart\").attr(\"class\", \"marker composition \" + type).attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L1,7 L9,1 Z\");\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-compositionEnd\").attr(\"class\", \"marker composition \" + type).attr(\"refX\", 1).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L1,7 L9,1 Z\");\n}, \"composition\");\nvar aggregation = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-aggregationStart\").attr(\"class\", \"marker aggregation \" + type).attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L1,7 L9,1 Z\");\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-aggregationEnd\").attr(\"class\", \"marker aggregation \" + type).attr(\"refX\", 1).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L1,7 L9,1 Z\");\n}, \"aggregation\");\nvar dependency = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-dependencyStart\").attr(\"class\", \"marker dependency \" + type).attr(\"refX\", 6).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 5,7 L9,13 L1,7 L9,1 Z\");\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-dependencyEnd\").attr(\"class\", \"marker dependency \" + type).attr(\"refX\", 13).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n}, \"dependency\");\nvar lollipop = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-lollipopStart\").attr(\"class\", \"marker lollipop \" + type).attr(\"refX\", 13).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"circle\").attr(\"stroke\", \"black\").attr(\"fill\", \"transparent\").attr(\"cx\", 7).attr(\"cy\", 7).attr(\"r\", 6);\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-lollipopEnd\").attr(\"class\", \"marker lollipop \" + type).attr(\"refX\", 1).attr(\"refY\", 7).attr(\"markerWidth\", 190).attr(\"markerHeight\", 240).attr(\"orient\", \"auto\").append(\"circle\").attr(\"stroke\", \"black\").attr(\"fill\", \"transparent\").attr(\"cx\", 7).attr(\"cy\", 7).attr(\"r\", 6);\n}, \"lollipop\");\nvar point = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-pointEnd\").attr(\"class\", \"marker \" + type).attr(\"viewBox\", \"0 0 10 10\").attr(\"refX\", 6).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 0 L 10 5 L 0 10 z\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 1).style(\"stroke-dasharray\", \"1,0\");\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-pointStart\").attr(\"class\", \"marker \" + type).attr(\"viewBox\", \"0 0 10 10\").attr(\"refX\", 4.5).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 5 L 10 10 L 10 0 z\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 1).style(\"stroke-dasharray\", \"1,0\");\n}, \"point\");\nvar circle = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-circleEnd\").attr(\"class\", \"marker \" + type).attr(\"viewBox\", \"0 0 10 10\").attr(\"refX\", 11).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 11).attr(\"markerHeight\", 11).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", \"5\").attr(\"cy\", \"5\").attr(\"r\", \"5\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 1).style(\"stroke-dasharray\", \"1,0\");\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-circleStart\").attr(\"class\", \"marker \" + type).attr(\"viewBox\", \"0 0 10 10\").attr(\"refX\", -1).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 11).attr(\"markerHeight\", 11).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", \"5\").attr(\"cy\", \"5\").attr(\"r\", \"5\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 1).style(\"stroke-dasharray\", \"1,0\");\n}, \"circle\");\nvar cross = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-crossEnd\").attr(\"class\", \"marker cross \" + type).attr(\"viewBox\", \"0 0 11 11\").attr(\"refX\", 12).attr(\"refY\", 5.2).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 11).attr(\"markerHeight\", 11).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 1,1 l 9,9 M 10,1 l -9,9\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 2).style(\"stroke-dasharray\", \"1,0\");\n  elem.append(\"marker\").attr(\"id\", id + \"_\" + type + \"-crossStart\").attr(\"class\", \"marker cross \" + type).attr(\"viewBox\", \"0 0 11 11\").attr(\"refX\", -1).attr(\"refY\", 5.2).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 11).attr(\"markerHeight\", 11).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 1,1 l 9,9 M 10,1 l -9,9\").attr(\"class\", \"arrowMarkerPath\").style(\"stroke-width\", 2).style(\"stroke-dasharray\", \"1,0\");\n}, \"cross\");\nvar barb = /* @__PURE__ */ __name((elem, type, id) => {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", id + \"_\" + type + \"-barbEnd\").attr(\"refX\", 19).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 14).attr(\"markerUnits\", \"strokeWidth\").attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 19,7 L9,13 L14,7 L9,1 Z\");\n}, \"barb\");\nvar markers = {\n  extension,\n  composition,\n  aggregation,\n  dependency,\n  lollipop,\n  point,\n  circle,\n  cross,\n  barb\n};\nvar markers_default = insertMarkers;\n\n// src/diagrams/block/layout.ts\nvar padding = getConfig2()?.block?.padding ?? 8;\nfunction calculateBlockPosition(columns, position) {\n  if (columns === 0 || !Number.isInteger(columns)) {\n    throw new Error(\"Columns must be an integer !== 0.\");\n  }\n  if (position < 0 || !Number.isInteger(position)) {\n    throw new Error(\"Position must be a non-negative integer.\" + position);\n  }\n  if (columns < 0) {\n    return { px: position, py: 0 };\n  }\n  if (columns === 1) {\n    return { px: 0, py: position };\n  }\n  const px = position % columns;\n  const py = Math.floor(position / columns);\n  return { px, py };\n}\n__name(calculateBlockPosition, \"calculateBlockPosition\");\nvar getMaxChildSize = /* @__PURE__ */ __name((block) => {\n  let maxWidth = 0;\n  let maxHeight = 0;\n  for (const child of block.children) {\n    const { width, height, x, y } = child.size ?? { width: 0, height: 0, x: 0, y: 0 };\n    log.debug(\n      \"getMaxChildSize abc95 child:\",\n      child.id,\n      \"width:\",\n      width,\n      \"height:\",\n      height,\n      \"x:\",\n      x,\n      \"y:\",\n      y,\n      child.type\n    );\n    if (child.type === \"space\") {\n      continue;\n    }\n    if (width > maxWidth) {\n      maxWidth = width / (block.widthInColumns ?? 1);\n    }\n    if (height > maxHeight) {\n      maxHeight = height;\n    }\n  }\n  return { width: maxWidth, height: maxHeight };\n}, \"getMaxChildSize\");\nfunction setBlockSizes(block, db2, siblingWidth = 0, siblingHeight = 0) {\n  log.debug(\n    \"setBlockSizes abc95 (start)\",\n    block.id,\n    block?.size?.x,\n    \"block width =\",\n    block?.size,\n    \"sieblingWidth\",\n    siblingWidth\n  );\n  if (!block?.size?.width) {\n    block.size = {\n      width: siblingWidth,\n      height: siblingHeight,\n      x: 0,\n      y: 0\n    };\n  }\n  let maxWidth = 0;\n  let maxHeight = 0;\n  if (block.children?.length > 0) {\n    for (const child of block.children) {\n      setBlockSizes(child, db2);\n    }\n    const childSize = getMaxChildSize(block);\n    maxWidth = childSize.width;\n    maxHeight = childSize.height;\n    log.debug(\"setBlockSizes abc95 maxWidth of\", block.id, \":s children is \", maxWidth, maxHeight);\n    for (const child of block.children) {\n      if (child.size) {\n        log.debug(\n          `abc95 Setting size of children of ${block.id} id=${child.id} ${maxWidth} ${maxHeight} ${JSON.stringify(child.size)}`\n        );\n        child.size.width = maxWidth * (child.widthInColumns ?? 1) + padding * ((child.widthInColumns ?? 1) - 1);\n        child.size.height = maxHeight;\n        child.size.x = 0;\n        child.size.y = 0;\n        log.debug(\n          `abc95 updating size of ${block.id} children child:${child.id} maxWidth:${maxWidth} maxHeight:${maxHeight}`\n        );\n      }\n    }\n    for (const child of block.children) {\n      setBlockSizes(child, db2, maxWidth, maxHeight);\n    }\n    const columns = block.columns ?? -1;\n    let numItems = 0;\n    for (const child of block.children) {\n      numItems += child.widthInColumns ?? 1;\n    }\n    let xSize = block.children.length;\n    if (columns > 0 && columns < numItems) {\n      xSize = columns;\n    }\n    const ySize = Math.ceil(numItems / xSize);\n    let width = xSize * (maxWidth + padding) + padding;\n    let height = ySize * (maxHeight + padding) + padding;\n    if (width < siblingWidth) {\n      log.debug(\n        `Detected to small siebling: abc95 ${block.id} sieblingWidth ${siblingWidth} sieblingHeight ${siblingHeight} width ${width}`\n      );\n      width = siblingWidth;\n      height = siblingHeight;\n      const childWidth = (siblingWidth - xSize * padding - padding) / xSize;\n      const childHeight = (siblingHeight - ySize * padding - padding) / ySize;\n      log.debug(\"Size indata abc88\", block.id, \"childWidth\", childWidth, \"maxWidth\", maxWidth);\n      log.debug(\"Size indata abc88\", block.id, \"childHeight\", childHeight, \"maxHeight\", maxHeight);\n      log.debug(\"Size indata abc88 xSize\", xSize, \"padding\", padding);\n      for (const child of block.children) {\n        if (child.size) {\n          child.size.width = childWidth;\n          child.size.height = childHeight;\n          child.size.x = 0;\n          child.size.y = 0;\n        }\n      }\n    }\n    log.debug(\n      `abc95 (finale calc) ${block.id} xSize ${xSize} ySize ${ySize} columns ${columns}${block.children.length} width=${Math.max(width, block.size?.width || 0)}`\n    );\n    if (width < (block?.size?.width || 0)) {\n      width = block?.size?.width || 0;\n      const num = columns > 0 ? Math.min(block.children.length, columns) : block.children.length;\n      if (num > 0) {\n        const childWidth = (width - num * padding - padding) / num;\n        log.debug(\"abc95 (growing to fit) width\", block.id, width, block.size?.width, childWidth);\n        for (const child of block.children) {\n          if (child.size) {\n            child.size.width = childWidth;\n          }\n        }\n      }\n    }\n    block.size = {\n      width,\n      height,\n      x: 0,\n      y: 0\n    };\n  }\n  log.debug(\n    \"setBlockSizes abc94 (done)\",\n    block.id,\n    block?.size?.x,\n    block?.size?.width,\n    block?.size?.y,\n    block?.size?.height\n  );\n}\n__name(setBlockSizes, \"setBlockSizes\");\nfunction layoutBlocks(block, db2) {\n  log.debug(\n    `abc85 layout blocks (=>layoutBlocks) ${block.id} x: ${block?.size?.x} y: ${block?.size?.y} width: ${block?.size?.width}`\n  );\n  const columns = block.columns ?? -1;\n  log.debug(\"layoutBlocks columns abc95\", block.id, \"=>\", columns, block);\n  if (block.children && // find max width of children\n  block.children.length > 0) {\n    const width = block?.children[0]?.size?.width ?? 0;\n    const widthOfChildren = block.children.length * width + (block.children.length - 1) * padding;\n    log.debug(\"widthOfChildren 88\", widthOfChildren, \"posX\");\n    let columnPos = 0;\n    log.debug(\"abc91 block?.size?.x\", block.id, block?.size?.x);\n    let startingPosX = block?.size?.x ? block?.size?.x + (-block?.size?.width / 2 || 0) : -padding;\n    let rowPos = 0;\n    for (const child of block.children) {\n      const parent = block;\n      if (!child.size) {\n        continue;\n      }\n      const { width: width2, height } = child.size;\n      const { px, py } = calculateBlockPosition(columns, columnPos);\n      if (py != rowPos) {\n        rowPos = py;\n        startingPosX = block?.size?.x ? block?.size?.x + (-block?.size?.width / 2 || 0) : -padding;\n        log.debug(\"New row in layout for block\", block.id, \" and child \", child.id, rowPos);\n      }\n      log.debug(\n        `abc89 layout blocks (child) id: ${child.id} Pos: ${columnPos} (px, py) ${px},${py} (${parent?.size?.x},${parent?.size?.y}) parent: ${parent.id} width: ${width2}${padding}`\n      );\n      if (parent.size) {\n        const halfWidth = width2 / 2;\n        child.size.x = startingPosX + padding + halfWidth;\n        log.debug(\n          `abc91 layout blocks (calc) px, pyid:${child.id} startingPos=X${startingPosX} new startingPosX${child.size.x} ${halfWidth} padding=${padding} width=${width2} halfWidth=${halfWidth} => x:${child.size.x} y:${child.size.y} ${child.widthInColumns} (width * (child?.w || 1)) / 2 ${width2 * (child?.widthInColumns ?? 1) / 2}`\n        );\n        startingPosX = child.size.x + halfWidth;\n        child.size.y = parent.size.y - parent.size.height / 2 + py * (height + padding) + height / 2 + padding;\n        log.debug(\n          `abc88 layout blocks (calc) px, pyid:${child.id}startingPosX${startingPosX}${padding}${halfWidth}=>x:${child.size.x}y:${child.size.y}${child.widthInColumns}(width * (child?.w || 1)) / 2${width2 * (child?.widthInColumns ?? 1) / 2}`\n        );\n      }\n      if (child.children) {\n        layoutBlocks(child, db2);\n      }\n      columnPos += child?.widthInColumns ?? 1;\n      log.debug(\"abc88 columnsPos\", child, columnPos);\n    }\n  }\n  log.debug(\n    `layout blocks (<==layoutBlocks) ${block.id} x: ${block?.size?.x} y: ${block?.size?.y} width: ${block?.size?.width}`\n  );\n}\n__name(layoutBlocks, \"layoutBlocks\");\nfunction findBounds(block, { minX, minY, maxX, maxY } = { minX: 0, minY: 0, maxX: 0, maxY: 0 }) {\n  if (block.size && block.id !== \"root\") {\n    const { x, y, width, height } = block.size;\n    if (x - width / 2 < minX) {\n      minX = x - width / 2;\n    }\n    if (y - height / 2 < minY) {\n      minY = y - height / 2;\n    }\n    if (x + width / 2 > maxX) {\n      maxX = x + width / 2;\n    }\n    if (y + height / 2 > maxY) {\n      maxY = y + height / 2;\n    }\n  }\n  if (block.children) {\n    for (const child of block.children) {\n      ({ minX, minY, maxX, maxY } = findBounds(child, { minX, minY, maxX, maxY }));\n    }\n  }\n  return { minX, minY, maxX, maxY };\n}\n__name(findBounds, \"findBounds\");\nfunction layout(db2) {\n  const root = db2.getBlock(\"root\");\n  if (!root) {\n    return;\n  }\n  setBlockSizes(root, db2, 0, 0);\n  layoutBlocks(root, db2);\n  log.debug(\"getBlocks\", JSON.stringify(root, null, 2));\n  const { minX, minY, maxX, maxY } = findBounds(root);\n  const height = maxY - minY;\n  const width = maxX - minX;\n  return { x: minX, y: minY, width, height };\n}\n__name(layout, \"layout\");\n\n// src/diagrams/block/renderHelpers.ts\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/dagre-wrapper/createLabel.js\nimport { select } from \"d3\";\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr(\"style\", styleFn);\n  }\n}\n__name(applyStyle, \"applyStyle\");\nfunction addHtmlLabel(node) {\n  const fo = select(document.createElementNS(\"http://www.w3.org/2000/svg\", \"foreignObject\"));\n  const div = fo.append(\"xhtml:div\");\n  const label = node.label;\n  const labelClass = node.isNode ? \"nodeLabel\" : \"edgeLabel\";\n  const span = div.append(\"span\");\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr(\"class\", labelClass);\n  applyStyle(div, node.labelStyle);\n  div.style(\"display\", \"inline-block\");\n  div.style(\"white-space\", \"nowrap\");\n  div.attr(\"xmlns\", \"http://www.w3.org/1999/xhtml\");\n  return fo.node();\n}\n__name(addHtmlLabel, \"addHtmlLabel\");\nvar createLabel = /* @__PURE__ */ __name((_vertexText, style, isTitle, isNode) => {\n  let vertexText = _vertexText || \"\";\n  if (typeof vertexText === \"object\") {\n    vertexText = vertexText[0];\n  }\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    vertexText = vertexText.replace(/\\\\n|\\n/g, \"<br />\");\n    log.debug(\"vertexText\" + vertexText);\n    const node = {\n      isNode,\n      label: replaceIconSubstring(decodeEntities(vertexText)),\n      labelStyle: style.replace(\"fill:\", \"color:\")\n    };\n    let vertexNode = addHtmlLabel(node);\n    return vertexNode;\n  } else {\n    const svgLabel = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n    svgLabel.setAttribute(\"style\", style.replace(\"color:\", \"fill:\"));\n    let rows = [];\n    if (typeof vertexText === \"string\") {\n      rows = vertexText.split(/\\\\n|\\n|<br\\s*\\/?>/gi);\n    } else if (Array.isArray(vertexText)) {\n      rows = vertexText;\n    } else {\n      rows = [];\n    }\n    for (const row of rows) {\n      const tspan = document.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n      tspan.setAttributeNS(\"http://www.w3.org/XML/1998/namespace\", \"xml:space\", \"preserve\");\n      tspan.setAttribute(\"dy\", \"1em\");\n      tspan.setAttribute(\"x\", \"0\");\n      if (isTitle) {\n        tspan.setAttribute(\"class\", \"title-row\");\n      } else {\n        tspan.setAttribute(\"class\", \"row\");\n      }\n      tspan.textContent = row.trim();\n      svgLabel.appendChild(tspan);\n    }\n    return svgLabel;\n  }\n}, \"createLabel\");\nvar createLabel_default = createLabel;\n\n// src/dagre-wrapper/edges.js\nimport { line, curveBasis, select as select2 } from \"d3\";\n\n// src/dagre-wrapper/edgeMarker.ts\nvar addEdgeMarkers = /* @__PURE__ */ __name((svgPath, edge, url, id, diagramType) => {\n  if (edge.arrowTypeStart) {\n    addEdgeMarker(svgPath, \"start\", edge.arrowTypeStart, url, id, diagramType);\n  }\n  if (edge.arrowTypeEnd) {\n    addEdgeMarker(svgPath, \"end\", edge.arrowTypeEnd, url, id, diagramType);\n  }\n}, \"addEdgeMarkers\");\nvar arrowTypesMap = {\n  arrow_cross: \"cross\",\n  arrow_point: \"point\",\n  arrow_barb: \"barb\",\n  arrow_circle: \"circle\",\n  aggregation: \"aggregation\",\n  extension: \"extension\",\n  composition: \"composition\",\n  dependency: \"dependency\",\n  lollipop: \"lollipop\"\n};\nvar addEdgeMarker = /* @__PURE__ */ __name((svgPath, position, arrowType, url, id, diagramType) => {\n  const endMarkerType = arrowTypesMap[arrowType];\n  if (!endMarkerType) {\n    log.warn(`Unknown arrow type: ${arrowType}`);\n    return;\n  }\n  const suffix = position === \"start\" ? \"Start\" : \"End\";\n  svgPath.attr(`marker-${position}`, `url(${url}#${id}_${diagramType}-${endMarkerType}${suffix})`);\n}, \"addEdgeMarker\");\n\n// src/dagre-wrapper/edges.js\nvar edgeLabels = {};\nvar terminalLabels = {};\nvar insertEdgeLabel = /* @__PURE__ */ __name((elem, edge) => {\n  const config2 = getConfig2();\n  const useHtmlLabels = evaluate(config2.flowchart.htmlLabels);\n  const labelElement = edge.labelType === \"markdown\" ? createText(\n    elem,\n    edge.label,\n    {\n      style: edge.labelStyle,\n      useHtmlLabels,\n      addSvgBackground: true\n    },\n    config2\n  ) : createLabel_default(edge.label, edge.labelStyle);\n  const edgeLabel = elem.insert(\"g\").attr(\"class\", \"edgeLabel\");\n  const label = edgeLabel.insert(\"g\").attr(\"class\", \"label\");\n  label.node().appendChild(labelElement);\n  let bbox = labelElement.getBBox();\n  if (useHtmlLabels) {\n    const div = labelElement.children[0];\n    const dv = select2(labelElement);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  label.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  edgeLabels[edge.id] = edgeLabel;\n  edge.width = bbox.width;\n  edge.height = bbox.height;\n  let fo;\n  if (edge.startLabelLeft) {\n    const startLabelElement = createLabel_default(edge.startLabelLeft, edge.labelStyle);\n    const startEdgeLabelLeft = elem.insert(\"g\").attr(\"class\", \"edgeTerminals\");\n    const inner = startEdgeLabelLeft.insert(\"g\").attr(\"class\", \"inner\");\n    fo = inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr(\"transform\", \"translate(\" + -slBox.width / 2 + \", \" + -slBox.height / 2 + \")\");\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].startLeft = startEdgeLabelLeft;\n    setTerminalWidth(fo, edge.startLabelLeft);\n  }\n  if (edge.startLabelRight) {\n    const startLabelElement = createLabel_default(edge.startLabelRight, edge.labelStyle);\n    const startEdgeLabelRight = elem.insert(\"g\").attr(\"class\", \"edgeTerminals\");\n    const inner = startEdgeLabelRight.insert(\"g\").attr(\"class\", \"inner\");\n    fo = startEdgeLabelRight.node().appendChild(startLabelElement);\n    inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr(\"transform\", \"translate(\" + -slBox.width / 2 + \", \" + -slBox.height / 2 + \")\");\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].startRight = startEdgeLabelRight;\n    setTerminalWidth(fo, edge.startLabelRight);\n  }\n  if (edge.endLabelLeft) {\n    const endLabelElement = createLabel_default(edge.endLabelLeft, edge.labelStyle);\n    const endEdgeLabelLeft = elem.insert(\"g\").attr(\"class\", \"edgeTerminals\");\n    const inner = endEdgeLabelLeft.insert(\"g\").attr(\"class\", \"inner\");\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr(\"transform\", \"translate(\" + -slBox.width / 2 + \", \" + -slBox.height / 2 + \")\");\n    endEdgeLabelLeft.node().appendChild(endLabelElement);\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].endLeft = endEdgeLabelLeft;\n    setTerminalWidth(fo, edge.endLabelLeft);\n  }\n  if (edge.endLabelRight) {\n    const endLabelElement = createLabel_default(edge.endLabelRight, edge.labelStyle);\n    const endEdgeLabelRight = elem.insert(\"g\").attr(\"class\", \"edgeTerminals\");\n    const inner = endEdgeLabelRight.insert(\"g\").attr(\"class\", \"inner\");\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr(\"transform\", \"translate(\" + -slBox.width / 2 + \", \" + -slBox.height / 2 + \")\");\n    endEdgeLabelRight.node().appendChild(endLabelElement);\n    if (!terminalLabels[edge.id]) {\n      terminalLabels[edge.id] = {};\n    }\n    terminalLabels[edge.id].endRight = endEdgeLabelRight;\n    setTerminalWidth(fo, edge.endLabelRight);\n  }\n  return labelElement;\n}, \"insertEdgeLabel\");\nfunction setTerminalWidth(fo, value) {\n  if (getConfig2().flowchart.htmlLabels && fo) {\n    fo.style.width = value.length * 9 + \"px\";\n    fo.style.height = \"12px\";\n  }\n}\n__name(setTerminalWidth, \"setTerminalWidth\");\nvar positionEdgeLabel = /* @__PURE__ */ __name((edge, paths) => {\n  log.debug(\"Moving label abc88 \", edge.id, edge.label, edgeLabels[edge.id], paths);\n  let path = paths.updatedPath ? paths.updatedPath : paths.originalPath;\n  const siteConfig = getConfig2();\n  const { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  if (edge.label) {\n    const el = edgeLabels[edge.id];\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils_default.calcLabelPosition(path);\n      log.debug(\n        \"Moving label \" + edge.label + \" from (\",\n        x,\n        \",\",\n        y,\n        \") to (\",\n        pos.x,\n        \",\",\n        pos.y,\n        \") abc88\"\n      );\n      if (paths.updatedPath) {\n        x = pos.x;\n        y = pos.y;\n      }\n    }\n    el.attr(\"transform\", `translate(${x}, ${y + subGraphTitleTotalMargin / 2})`);\n  }\n  if (edge.startLabelLeft) {\n    const el = terminalLabels[edge.id].startLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils_default.calcTerminalLabelPosition(edge.arrowTypeStart ? 10 : 0, \"start_left\", path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr(\"transform\", `translate(${x}, ${y})`);\n  }\n  if (edge.startLabelRight) {\n    const el = terminalLabels[edge.id].startRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils_default.calcTerminalLabelPosition(\n        edge.arrowTypeStart ? 10 : 0,\n        \"start_right\",\n        path\n      );\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr(\"transform\", `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelLeft) {\n    const el = terminalLabels[edge.id].endLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils_default.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, \"end_left\", path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr(\"transform\", `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelRight) {\n    const el = terminalLabels[edge.id].endRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils_default.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, \"end_right\", path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr(\"transform\", `translate(${x}, ${y})`);\n  }\n}, \"positionEdgeLabel\");\nvar outsideNode = /* @__PURE__ */ __name((node, point2) => {\n  const x = node.x;\n  const y = node.y;\n  const dx = Math.abs(point2.x - x);\n  const dy = Math.abs(point2.y - y);\n  const w = node.width / 2;\n  const h = node.height / 2;\n  if (dx >= w || dy >= h) {\n    return true;\n  }\n  return false;\n}, \"outsideNode\");\nvar intersection = /* @__PURE__ */ __name((node, outsidePoint, insidePoint) => {\n  log.debug(`intersection calc abc89:\n  outsidePoint: ${JSON.stringify(outsidePoint)}\n  insidePoint : ${JSON.stringify(insidePoint)}\n  node        : x:${node.x} y:${node.y} w:${node.width} h:${node.height}`);\n  const x = node.x;\n  const y = node.y;\n  const dx = Math.abs(x - insidePoint.x);\n  const w = node.width / 2;\n  let r = insidePoint.x < outsidePoint.x ? w - dx : w + dx;\n  const h = node.height / 2;\n  const Q = Math.abs(outsidePoint.y - insidePoint.y);\n  const R = Math.abs(outsidePoint.x - insidePoint.x);\n  if (Math.abs(y - outsidePoint.y) * w > Math.abs(x - outsidePoint.x) * h) {\n    let q = insidePoint.y < outsidePoint.y ? outsidePoint.y - h - y : y - h - outsidePoint.y;\n    r = R * q / Q;\n    const res = {\n      x: insidePoint.x < outsidePoint.x ? insidePoint.x + r : insidePoint.x - R + r,\n      y: insidePoint.y < outsidePoint.y ? insidePoint.y + Q - q : insidePoint.y - Q + q\n    };\n    if (r === 0) {\n      res.x = outsidePoint.x;\n      res.y = outsidePoint.y;\n    }\n    if (R === 0) {\n      res.x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      res.y = outsidePoint.y;\n    }\n    log.debug(`abc89 topp/bott calc, Q ${Q}, q ${q}, R ${R}, r ${r}`, res);\n    return res;\n  } else {\n    if (insidePoint.x < outsidePoint.x) {\n      r = outsidePoint.x - w - x;\n    } else {\n      r = x - w - outsidePoint.x;\n    }\n    let q = Q * r / R;\n    let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : insidePoint.x - R + r;\n    let _y = insidePoint.y < outsidePoint.y ? insidePoint.y + q : insidePoint.y - q;\n    log.debug(`sides calc abc89, Q ${Q}, q ${q}, R ${R}, r ${r}`, { _x, _y });\n    if (r === 0) {\n      _x = outsidePoint.x;\n      _y = outsidePoint.y;\n    }\n    if (R === 0) {\n      _x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      _y = outsidePoint.y;\n    }\n    return { x: _x, y: _y };\n  }\n}, \"intersection\");\nvar cutPathAtIntersect = /* @__PURE__ */ __name((_points, boundaryNode) => {\n  log.debug(\"abc88 cutPathAtIntersect\", _points, boundaryNode);\n  let points = [];\n  let lastPointOutside = _points[0];\n  let isInside = false;\n  _points.forEach((point2) => {\n    if (!outsideNode(boundaryNode, point2) && !isInside) {\n      const inter = intersection(boundaryNode, lastPointOutside, point2);\n      let pointPresent = false;\n      points.forEach((p) => {\n        pointPresent = pointPresent || p.x === inter.x && p.y === inter.y;\n      });\n      if (!points.some((e) => e.x === inter.x && e.y === inter.y)) {\n        points.push(inter);\n      }\n      isInside = true;\n    } else {\n      lastPointOutside = point2;\n      if (!isInside) {\n        points.push(point2);\n      }\n    }\n  });\n  return points;\n}, \"cutPathAtIntersect\");\nvar insertEdge = /* @__PURE__ */ __name(function(elem, e, edge, clusterDb, diagramType, graph, id) {\n  let points = edge.points;\n  log.debug(\"abc88 InsertEdge: edge=\", edge, \"e=\", e);\n  let pointsHasChanged = false;\n  const tail = graph.node(e.v);\n  var head = graph.node(e.w);\n  if (head?.intersect && tail?.intersect) {\n    points = points.slice(1, edge.points.length - 1);\n    points.unshift(tail.intersect(points[0]));\n    points.push(head.intersect(points[points.length - 1]));\n  }\n  if (edge.toCluster) {\n    log.debug(\"to cluster abc88\", clusterDb[edge.toCluster]);\n    points = cutPathAtIntersect(edge.points, clusterDb[edge.toCluster].node);\n    pointsHasChanged = true;\n  }\n  if (edge.fromCluster) {\n    log.debug(\"from cluster abc88\", clusterDb[edge.fromCluster]);\n    points = cutPathAtIntersect(points.reverse(), clusterDb[edge.fromCluster].node).reverse();\n    pointsHasChanged = true;\n  }\n  const lineData = points.filter((p) => !Number.isNaN(p.y));\n  let curve = curveBasis;\n  if (edge.curve && (diagramType === \"graph\" || diagramType === \"flowchart\")) {\n    curve = edge.curve;\n  }\n  const { x, y } = getLineFunctionsWithOffset(edge);\n  const lineFunction = line().x(x).y(y).curve(curve);\n  let strokeClasses;\n  switch (edge.thickness) {\n    case \"normal\":\n      strokeClasses = \"edge-thickness-normal\";\n      break;\n    case \"thick\":\n      strokeClasses = \"edge-thickness-thick\";\n      break;\n    case \"invisible\":\n      strokeClasses = \"edge-thickness-thick\";\n      break;\n    default:\n      strokeClasses = \"\";\n  }\n  switch (edge.pattern) {\n    case \"solid\":\n      strokeClasses += \" edge-pattern-solid\";\n      break;\n    case \"dotted\":\n      strokeClasses += \" edge-pattern-dotted\";\n      break;\n    case \"dashed\":\n      strokeClasses += \" edge-pattern-dashed\";\n      break;\n  }\n  const svgPath = elem.append(\"path\").attr(\"d\", lineFunction(lineData)).attr(\"id\", edge.id).attr(\"class\", \" \" + strokeClasses + (edge.classes ? \" \" + edge.classes : \"\")).attr(\"style\", edge.style);\n  let url = \"\";\n  if (getConfig2().flowchart.arrowMarkerAbsolute || getConfig2().state.arrowMarkerAbsolute) {\n    url = window.location.protocol + \"//\" + window.location.host + window.location.pathname + window.location.search;\n    url = url.replace(/\\(/g, \"\\\\(\");\n    url = url.replace(/\\)/g, \"\\\\)\");\n  }\n  addEdgeMarkers(svgPath, edge, url, id, diagramType);\n  let paths = {};\n  if (pointsHasChanged) {\n    paths.updatedPath = points;\n  }\n  paths.originalPath = edge.points;\n  return paths;\n}, \"insertEdge\");\n\n// src/dagre-wrapper/nodes.js\nimport { select as select4 } from \"d3\";\n\n// src/dagre-wrapper/blockArrowHelper.ts\nvar expandAndDeduplicateDirections = /* @__PURE__ */ __name((directions) => {\n  const uniqueDirections = /* @__PURE__ */ new Set();\n  for (const direction of directions) {\n    switch (direction) {\n      case \"x\":\n        uniqueDirections.add(\"right\");\n        uniqueDirections.add(\"left\");\n        break;\n      case \"y\":\n        uniqueDirections.add(\"up\");\n        uniqueDirections.add(\"down\");\n        break;\n      default:\n        uniqueDirections.add(direction);\n        break;\n    }\n  }\n  return uniqueDirections;\n}, \"expandAndDeduplicateDirections\");\nvar getArrowPoints = /* @__PURE__ */ __name((duplicatedDirections, bbox, node) => {\n  const directions = expandAndDeduplicateDirections(duplicatedDirections);\n  const f = 2;\n  const height = bbox.height + 2 * node.padding;\n  const midpoint = height / f;\n  const width = bbox.width + 2 * midpoint + node.padding;\n  const padding2 = node.padding / 2;\n  if (directions.has(\"right\") && directions.has(\"left\") && directions.has(\"up\") && directions.has(\"down\")) {\n    return [\n      // Bottom\n      { x: 0, y: 0 },\n      { x: midpoint, y: 0 },\n      { x: width / 2, y: 2 * padding2 },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: 0 },\n      // Right\n      { x: width, y: -height / 3 },\n      { x: width + 2 * padding2, y: -height / 2 },\n      { x: width, y: -2 * height / 3 },\n      { x: width, y: -height },\n      // Top\n      { x: width - midpoint, y: -height },\n      { x: width / 2, y: -height - 2 * padding2 },\n      { x: midpoint, y: -height },\n      // Left\n      { x: 0, y: -height },\n      { x: 0, y: -2 * height / 3 },\n      { x: -2 * padding2, y: -height / 2 },\n      { x: 0, y: -height / 3 }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"left\") && directions.has(\"up\")) {\n    return [\n      { x: midpoint, y: 0 },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"left\") && directions.has(\"down\")) {\n    return [\n      { x: 0, y: 0 },\n      { x: midpoint, y: -height },\n      { x: width - midpoint, y: -height },\n      { x: width, y: 0 }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"up\") && directions.has(\"down\")) {\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: -midpoint },\n      { x: width, y: -height + midpoint },\n      { x: 0, y: -height }\n    ];\n  }\n  if (directions.has(\"left\") && directions.has(\"up\") && directions.has(\"down\")) {\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: -midpoint },\n      { x: 0, y: -height + midpoint },\n      { x: width, y: -height }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"left\")) {\n    return [\n      { x: midpoint, y: 0 },\n      { x: midpoint, y: -padding2 },\n      { x: width - midpoint, y: -padding2 },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: width - midpoint, y: -height + padding2 },\n      { x: midpoint, y: -height + padding2 },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 }\n    ];\n  }\n  if (directions.has(\"up\") && directions.has(\"down\")) {\n    return [\n      // Bottom center\n      { x: width / 2, y: 0 },\n      // Left pont of bottom arrow\n      { x: 0, y: -padding2 },\n      { x: midpoint, y: -padding2 },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding2 },\n      { x: 0, y: -height + padding2 },\n      // Top of arrow\n      { x: width / 2, y: -height },\n      { x: width, y: -height + padding2 },\n      // Top of right vertical bar\n      { x: width - midpoint, y: -height + padding2 },\n      { x: width - midpoint, y: -padding2 },\n      { x: width, y: -padding2 }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"up\")) {\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: -midpoint },\n      { x: 0, y: -height }\n    ];\n  }\n  if (directions.has(\"right\") && directions.has(\"down\")) {\n    return [\n      { x: 0, y: 0 },\n      { x: width, y: 0 },\n      { x: 0, y: -height }\n    ];\n  }\n  if (directions.has(\"left\") && directions.has(\"up\")) {\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: -midpoint },\n      { x: width, y: -height }\n    ];\n  }\n  if (directions.has(\"left\") && directions.has(\"down\")) {\n    return [\n      { x: width, y: 0 },\n      { x: 0, y: 0 },\n      { x: width, y: -height }\n    ];\n  }\n  if (directions.has(\"right\")) {\n    return [\n      { x: midpoint, y: -padding2 },\n      { x: midpoint, y: -padding2 },\n      { x: width - midpoint, y: -padding2 },\n      { x: width - midpoint, y: 0 },\n      { x: width, y: -height / 2 },\n      { x: width - midpoint, y: -height },\n      { x: width - midpoint, y: -height + padding2 },\n      // top left corner of arrow\n      { x: midpoint, y: -height + padding2 },\n      { x: midpoint, y: -height + padding2 }\n    ];\n  }\n  if (directions.has(\"left\")) {\n    return [\n      { x: midpoint, y: 0 },\n      { x: midpoint, y: -padding2 },\n      // Two points, the right corners\n      { x: width - midpoint, y: -padding2 },\n      { x: width - midpoint, y: -height + padding2 },\n      { x: midpoint, y: -height + padding2 },\n      { x: midpoint, y: -height },\n      { x: 0, y: -height / 2 }\n    ];\n  }\n  if (directions.has(\"up\")) {\n    return [\n      // Bottom center\n      { x: midpoint, y: -padding2 },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding2 },\n      { x: 0, y: -height + padding2 },\n      // Top of arrow\n      { x: width / 2, y: -height },\n      { x: width, y: -height + padding2 },\n      // Top of right vertical bar\n      { x: width - midpoint, y: -height + padding2 },\n      { x: width - midpoint, y: -padding2 }\n    ];\n  }\n  if (directions.has(\"down\")) {\n    return [\n      // Bottom center\n      { x: width / 2, y: 0 },\n      // Left pont of bottom arrow\n      { x: 0, y: -padding2 },\n      { x: midpoint, y: -padding2 },\n      // Left top over vertical section\n      { x: midpoint, y: -height + padding2 },\n      { x: width - midpoint, y: -height + padding2 },\n      { x: width - midpoint, y: -padding2 },\n      { x: width, y: -padding2 }\n    ];\n  }\n  return [{ x: 0, y: 0 }];\n}, \"getArrowPoints\");\n\n// src/dagre-wrapper/intersect/intersect-node.js\nfunction intersectNode(node, point2) {\n  return node.intersect(point2);\n}\n__name(intersectNode, \"intersectNode\");\nvar intersect_node_default = intersectNode;\n\n// src/dagre-wrapper/intersect/intersect-ellipse.js\nfunction intersectEllipse(node, rx, ry, point2) {\n  var cx = node.x;\n  var cy = node.y;\n  var px = cx - point2.x;\n  var py = cy - point2.y;\n  var det = Math.sqrt(rx * rx * py * py + ry * ry * px * px);\n  var dx = Math.abs(rx * ry * px / det);\n  if (point2.x < cx) {\n    dx = -dx;\n  }\n  var dy = Math.abs(rx * ry * py / det);\n  if (point2.y < cy) {\n    dy = -dy;\n  }\n  return { x: cx + dx, y: cy + dy };\n}\n__name(intersectEllipse, \"intersectEllipse\");\nvar intersect_ellipse_default = intersectEllipse;\n\n// src/dagre-wrapper/intersect/intersect-circle.js\nfunction intersectCircle(node, rx, point2) {\n  return intersect_ellipse_default(node, rx, rx, point2);\n}\n__name(intersectCircle, \"intersectCircle\");\nvar intersect_circle_default = intersectCircle;\n\n// src/dagre-wrapper/intersect/intersect-line.js\nfunction intersectLine(p1, p2, q1, q2) {\n  var a1, a2, b1, b2, c1, c2;\n  var r1, r2, r3, r4;\n  var denom, offset, num;\n  var x, y;\n  a1 = p2.y - p1.y;\n  b1 = p1.x - p2.x;\n  c1 = p2.x * p1.y - p1.x * p2.y;\n  r3 = a1 * q1.x + b1 * q1.y + c1;\n  r4 = a1 * q2.x + b1 * q2.y + c1;\n  if (r3 !== 0 && r4 !== 0 && sameSign(r3, r4)) {\n    return;\n  }\n  a2 = q2.y - q1.y;\n  b2 = q1.x - q2.x;\n  c2 = q2.x * q1.y - q1.x * q2.y;\n  r1 = a2 * p1.x + b2 * p1.y + c2;\n  r2 = a2 * p2.x + b2 * p2.y + c2;\n  if (r1 !== 0 && r2 !== 0 && sameSign(r1, r2)) {\n    return;\n  }\n  denom = a1 * b2 - a2 * b1;\n  if (denom === 0) {\n    return;\n  }\n  offset = Math.abs(denom / 2);\n  num = b1 * c2 - b2 * c1;\n  x = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n  num = a2 * c1 - a1 * c2;\n  y = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n  return { x, y };\n}\n__name(intersectLine, \"intersectLine\");\nfunction sameSign(r1, r2) {\n  return r1 * r2 > 0;\n}\n__name(sameSign, \"sameSign\");\nvar intersect_line_default = intersectLine;\n\n// src/dagre-wrapper/intersect/intersect-polygon.js\nvar intersect_polygon_default = intersectPolygon;\nfunction intersectPolygon(node, polyPoints, point2) {\n  var x1 = node.x;\n  var y1 = node.y;\n  var intersections = [];\n  var minX = Number.POSITIVE_INFINITY;\n  var minY = Number.POSITIVE_INFINITY;\n  if (typeof polyPoints.forEach === \"function\") {\n    polyPoints.forEach(function(entry) {\n      minX = Math.min(minX, entry.x);\n      minY = Math.min(minY, entry.y);\n    });\n  } else {\n    minX = Math.min(minX, polyPoints.x);\n    minY = Math.min(minY, polyPoints.y);\n  }\n  var left = x1 - node.width / 2 - minX;\n  var top = y1 - node.height / 2 - minY;\n  for (var i = 0; i < polyPoints.length; i++) {\n    var p1 = polyPoints[i];\n    var p2 = polyPoints[i < polyPoints.length - 1 ? i + 1 : 0];\n    var intersect = intersect_line_default(\n      node,\n      point2,\n      { x: left + p1.x, y: top + p1.y },\n      { x: left + p2.x, y: top + p2.y }\n    );\n    if (intersect) {\n      intersections.push(intersect);\n    }\n  }\n  if (!intersections.length) {\n    return node;\n  }\n  if (intersections.length > 1) {\n    intersections.sort(function(p, q) {\n      var pdx = p.x - point2.x;\n      var pdy = p.y - point2.y;\n      var distp = Math.sqrt(pdx * pdx + pdy * pdy);\n      var qdx = q.x - point2.x;\n      var qdy = q.y - point2.y;\n      var distq = Math.sqrt(qdx * qdx + qdy * qdy);\n      return distp < distq ? -1 : distp === distq ? 0 : 1;\n    });\n  }\n  return intersections[0];\n}\n__name(intersectPolygon, \"intersectPolygon\");\n\n// src/dagre-wrapper/intersect/intersect-rect.js\nvar intersectRect = /* @__PURE__ */ __name((node, point2) => {\n  var x = node.x;\n  var y = node.y;\n  var dx = point2.x - x;\n  var dy = point2.y - y;\n  var w = node.width / 2;\n  var h = node.height / 2;\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = dy === 0 ? 0 : h * dx / dy;\n    sy = h;\n  } else {\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = dx === 0 ? 0 : w * dy / dx;\n  }\n  return { x: x + sx, y: y + sy };\n}, \"intersectRect\");\nvar intersect_rect_default = intersectRect;\n\n// src/dagre-wrapper/intersect/index.js\nvar intersect_default = {\n  node: intersect_node_default,\n  circle: intersect_circle_default,\n  ellipse: intersect_ellipse_default,\n  polygon: intersect_polygon_default,\n  rect: intersect_rect_default\n};\n\n// src/dagre-wrapper/shapes/util.js\nimport { select as select3 } from \"d3\";\nvar labelHelper = /* @__PURE__ */ __name(async (parent, node, _classes, isNode) => {\n  const config2 = getConfig2();\n  let classes2;\n  const useHtmlLabels = node.useHtmlLabels || evaluate(config2.flowchart.htmlLabels);\n  if (!_classes) {\n    classes2 = \"node default\";\n  } else {\n    classes2 = _classes;\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", classes2).attr(\"id\", node.domId || node.id);\n  const label = shapeSvg.insert(\"g\").attr(\"class\", \"label\").attr(\"style\", node.labelStyle);\n  let labelText;\n  if (node.labelText === void 0) {\n    labelText = \"\";\n  } else {\n    labelText = typeof node.labelText === \"string\" ? node.labelText : node.labelText[0];\n  }\n  const textNode = label.node();\n  let text;\n  if (node.labelType === \"markdown\") {\n    text = createText(\n      label,\n      sanitizeText(decodeEntities(labelText), config2),\n      {\n        useHtmlLabels,\n        width: node.width || config2.flowchart.wrappingWidth,\n        classes: \"markdown-node-label\"\n      },\n      config2\n    );\n  } else {\n    text = textNode.appendChild(\n      createLabel_default(sanitizeText(decodeEntities(labelText), config2), node.labelStyle, false, isNode)\n    );\n  }\n  let bbox = text.getBBox();\n  const halfPadding = node.padding / 2;\n  if (evaluate(config2.flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select3(text);\n    const images = div.getElementsByTagName(\"img\");\n    if (images) {\n      const noImgText = labelText.replace(/<img[^>]*>/g, \"\").trim() === \"\";\n      await Promise.all(\n        [...images].map(\n          (img) => new Promise((res) => {\n            function setupImage() {\n              img.style.display = \"flex\";\n              img.style.flexDirection = \"column\";\n              if (noImgText) {\n                const bodyFontSize = config2.fontSize ? config2.fontSize : window.getComputedStyle(document.body).fontSize;\n                const enlargingFactor = 5;\n                const width = parseInt(bodyFontSize, 10) * enlargingFactor + \"px\";\n                img.style.minWidth = width;\n                img.style.maxWidth = width;\n              } else {\n                img.style.width = \"100%\";\n              }\n              res(img);\n            }\n            __name(setupImage, \"setupImage\");\n            setTimeout(() => {\n              if (img.complete) {\n                setupImage();\n              }\n            });\n            img.addEventListener(\"error\", setupImage);\n            img.addEventListener(\"load\", setupImage);\n          })\n        )\n      );\n    }\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  if (useHtmlLabels) {\n    label.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  } else {\n    label.attr(\"transform\", \"translate(0, \" + -bbox.height / 2 + \")\");\n  }\n  if (node.centerLabel) {\n    label.attr(\"transform\", \"translate(\" + -bbox.width / 2 + \", \" + -bbox.height / 2 + \")\");\n  }\n  label.insert(\"rect\", \":first-child\");\n  return { shapeSvg, bbox, halfPadding, label };\n}, \"labelHelper\");\nvar updateNodeBounds = /* @__PURE__ */ __name((node, element) => {\n  const bbox = element.node().getBBox();\n  node.width = bbox.width;\n  node.height = bbox.height;\n}, \"updateNodeBounds\");\nfunction insertPolygonShape(parent, w, h, points) {\n  return parent.insert(\"polygon\", \":first-child\").attr(\n    \"points\",\n    points.map(function(d) {\n      return d.x + \",\" + d.y;\n    }).join(\" \")\n  ).attr(\"class\", \"label-container\").attr(\"transform\", \"translate(\" + -w / 2 + \",\" + h / 2 + \")\");\n}\n__name(insertPolygonShape, \"insertPolygonShape\");\n\n// src/dagre-wrapper/shapes/note.js\nvar note = /* @__PURE__ */ __name(async (parent, node) => {\n  const useHtmlLabels = node.useHtmlLabels || getConfig2().flowchart.htmlLabels;\n  if (!useHtmlLabels) {\n    node.centerLabel = true;\n  }\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    \"node \" + node.classes,\n    true\n  );\n  log.info(\"Classes = \", node.classes);\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  rect2.attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", -bbox.width / 2 - halfPadding).attr(\"y\", -bbox.height / 2 - halfPadding).attr(\"width\", bbox.width + node.padding).attr(\"height\", bbox.height + node.padding);\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"note\");\nvar note_default = note;\n\n// src/dagre-wrapper/nodes.js\nvar formatClass = /* @__PURE__ */ __name((str) => {\n  if (str) {\n    return \" \" + str;\n  }\n  return \"\";\n}, \"formatClass\");\nvar getClassesFromNode = /* @__PURE__ */ __name((node, otherClasses) => {\n  return `${otherClasses ? otherClasses : \"node default\"}${formatClass(node.classes)} ${formatClass(\n    node.class\n  )}`;\n}, \"getClassesFromNode\");\nvar question = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const s = w + h;\n  const points = [\n    { x: s / 2, y: 0 },\n    { x: s, y: -s / 2 },\n    { x: s / 2, y: -s },\n    { x: 0, y: -s / 2 }\n  ];\n  log.info(\"Question main (Circle)\");\n  const questionElem = insertPolygonShape(shapeSvg, s, s, points);\n  questionElem.attr(\"style\", node.style);\n  updateNodeBounds(node, questionElem);\n  node.intersect = function(point2) {\n    log.warn(\"Intersect called\");\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"question\");\nvar choice = /* @__PURE__ */ __name((parent, node) => {\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  const s = 28;\n  const points = [\n    { x: 0, y: s / 2 },\n    { x: s / 2, y: 0 },\n    { x: 0, y: -s / 2 },\n    { x: -s / 2, y: 0 }\n  ];\n  const choice2 = shapeSvg.insert(\"polygon\", \":first-child\").attr(\n    \"points\",\n    points.map(function(d) {\n      return d.x + \",\" + d.y;\n    }).join(\" \")\n  );\n  choice2.attr(\"class\", \"state-start\").attr(\"r\", 7).attr(\"width\", 28).attr(\"height\", 28);\n  node.width = 28;\n  node.height = 28;\n  node.intersect = function(point2) {\n    return intersect_default.circle(node, 14, point2);\n  };\n  return shapeSvg;\n}, \"choice\");\nvar hexagon = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const f = 4;\n  const h = bbox.height + node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n  const points = [\n    { x: m, y: 0 },\n    { x: w - m, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w - m, y: -h },\n    { x: m, y: -h },\n    { x: 0, y: -h / 2 }\n  ];\n  const hex = insertPolygonShape(shapeSvg, w, h, points);\n  hex.attr(\"style\", node.style);\n  updateNodeBounds(node, hex);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"hexagon\");\nvar block_arrow = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(parent, node, void 0, true);\n  const f = 2;\n  const h = bbox.height + 2 * node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n  const points = getArrowPoints(node.directions, bbox, node);\n  const blockArrow = insertPolygonShape(shapeSvg, w, h, points);\n  blockArrow.attr(\"style\", node.style);\n  updateNodeBounds(node, blockArrow);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"block_arrow\");\nvar rect_left_inv_arrow = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: -h / 2, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: -h / 2, y: -h },\n    { x: 0, y: -h / 2 }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  node.width = w + h;\n  node.height = h;\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"rect_left_inv_arrow\");\nvar lean_right = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getClassesFromNode(node), true);\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: -2 * h / 6, y: 0 },\n    { x: w - h / 6, y: 0 },\n    { x: w + 2 * h / 6, y: -h },\n    { x: h / 6, y: -h }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"lean_right\");\nvar lean_left = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: 2 * h / 6, y: 0 },\n    { x: w + h / 6, y: 0 },\n    { x: w - 2 * h / 6, y: -h },\n    { x: -h / 6, y: -h }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"lean_left\");\nvar trapezoid = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: -2 * h / 6, y: 0 },\n    { x: w + 2 * h / 6, y: 0 },\n    { x: w - h / 6, y: -h },\n    { x: h / 6, y: -h }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"trapezoid\");\nvar inv_trapezoid = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: h / 6, y: 0 },\n    { x: w - h / 6, y: 0 },\n    { x: w + 2 * h / 6, y: -h },\n    { x: -2 * h / 6, y: -h }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"inv_trapezoid\");\nvar rect_right_inv_arrow = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: 0, y: 0 },\n    { x: w + h / 2, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w + h / 2, y: -h },\n    { x: 0, y: -h }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"rect_right_inv_arrow\");\nvar cylinder = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = bbox.height + ry + node.padding;\n  const shape = \"M 0,\" + ry + \" a \" + rx + \",\" + ry + \" 0,0,0 \" + w + \" 0 a \" + rx + \",\" + ry + \" 0,0,0 \" + -w + \" 0 l 0,\" + h + \" a \" + rx + \",\" + ry + \" 0,0,0 \" + w + \" 0 l 0,\" + -h;\n  const el = shapeSvg.attr(\"label-offset-y\", ry).insert(\"path\", \":first-child\").attr(\"style\", node.style).attr(\"d\", shape).attr(\"transform\", \"translate(\" + -w / 2 + \",\" + -(h / 2 + ry) + \")\");\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    const pos = intersect_default.rect(node, point2);\n    const x = pos.x - node.x;\n    if (rx != 0 && (Math.abs(x) < node.width / 2 || Math.abs(x) == node.width / 2 && Math.abs(pos.y - node.y) > node.height / 2 - ry)) {\n      let y = ry * ry * (1 - x * x / (rx * rx));\n      if (y != 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point2.y - node.y > 0) {\n        y = -y;\n      }\n      pos.y += y;\n    }\n    return pos;\n  };\n  return shapeSvg;\n}, \"cylinder\");\nvar rect = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    \"node \" + node.classes + \" \" + node.class,\n    true\n  );\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const totalWidth = node.positioned ? node.width : bbox.width + node.padding;\n  const totalHeight = node.positioned ? node.height : bbox.height + node.padding;\n  const x = node.positioned ? -totalWidth / 2 : -bbox.width / 2 - halfPadding;\n  const y = node.positioned ? -totalHeight / 2 : -bbox.height / 2 - halfPadding;\n  rect2.attr(\"class\", \"basic label-container\").attr(\"style\", node.style).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", x).attr(\"y\", y).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect2, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete(\"borders\");\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"rect\");\nvar composite = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    \"node \" + node.classes,\n    true\n  );\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const totalWidth = node.positioned ? node.width : bbox.width + node.padding;\n  const totalHeight = node.positioned ? node.height : bbox.height + node.padding;\n  const x = node.positioned ? -totalWidth / 2 : -bbox.width / 2 - halfPadding;\n  const y = node.positioned ? -totalHeight / 2 : -bbox.height / 2 - halfPadding;\n  rect2.attr(\"class\", \"basic cluster composite label-container\").attr(\"style\", node.style).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"x\", x).attr(\"y\", y).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect2, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete(\"borders\");\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"composite\");\nvar labelRect = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg } = await labelHelper(parent, node, \"label\", true);\n  log.trace(\"Classes = \", node.class);\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const totalWidth = 0;\n  const totalHeight = 0;\n  rect2.attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  shapeSvg.attr(\"class\", \"label edgeLabel\");\n  if (node.props) {\n    const propKeys = new Set(Object.keys(node.props));\n    if (node.props.borders) {\n      applyNodePropertyBorders(rect2, node.props.borders, totalWidth, totalHeight);\n      propKeys.delete(\"borders\");\n    }\n    propKeys.forEach((propKey) => {\n      log.warn(`Unknown node property ${propKey}`);\n    });\n  }\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"labelRect\");\nfunction applyNodePropertyBorders(rect2, borders, totalWidth, totalHeight) {\n  const strokeDashArray = [];\n  const addBorder = /* @__PURE__ */ __name((length) => {\n    strokeDashArray.push(length, 0);\n  }, \"addBorder\");\n  const skipBorder = /* @__PURE__ */ __name((length) => {\n    strokeDashArray.push(0, length);\n  }, \"skipBorder\");\n  if (borders.includes(\"t\")) {\n    log.debug(\"add top border\");\n    addBorder(totalWidth);\n  } else {\n    skipBorder(totalWidth);\n  }\n  if (borders.includes(\"r\")) {\n    log.debug(\"add right border\");\n    addBorder(totalHeight);\n  } else {\n    skipBorder(totalHeight);\n  }\n  if (borders.includes(\"b\")) {\n    log.debug(\"add bottom border\");\n    addBorder(totalWidth);\n  } else {\n    skipBorder(totalWidth);\n  }\n  if (borders.includes(\"l\")) {\n    log.debug(\"add left border\");\n    addBorder(totalHeight);\n  } else {\n    skipBorder(totalHeight);\n  }\n  rect2.attr(\"stroke-dasharray\", strokeDashArray.join(\" \"));\n}\n__name(applyNodePropertyBorders, \"applyNodePropertyBorders\");\nvar rectWithTitle = /* @__PURE__ */ __name((parent, node) => {\n  let classes2;\n  if (!node.classes) {\n    classes2 = \"node default\";\n  } else {\n    classes2 = \"node \" + node.classes;\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", classes2).attr(\"id\", node.domId || node.id);\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const innerLine = shapeSvg.insert(\"line\");\n  const label = shapeSvg.insert(\"g\").attr(\"class\", \"label\");\n  const text2 = node.labelText.flat ? node.labelText.flat() : node.labelText;\n  let title = \"\";\n  if (typeof text2 === \"object\") {\n    title = text2[0];\n  } else {\n    title = text2;\n  }\n  log.info(\"Label text abc79\", title, text2, typeof text2 === \"object\");\n  const text = label.node().appendChild(createLabel_default(title, node.labelStyle, true, true));\n  let bbox = { width: 0, height: 0 };\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select4(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  log.info(\"Text 2\", text2);\n  const textRows = text2.slice(1, text2.length);\n  let titleBox = text.getBBox();\n  const descr = label.node().appendChild(\n    createLabel_default(textRows.join ? textRows.join(\"<br/>\") : textRows, node.labelStyle, true, true)\n  );\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    const div = descr.children[0];\n    const dv = select4(descr);\n    bbox = div.getBoundingClientRect();\n    dv.attr(\"width\", bbox.width);\n    dv.attr(\"height\", bbox.height);\n  }\n  const halfPadding = node.padding / 2;\n  select4(descr).attr(\n    \"transform\",\n    \"translate( \" + // (titleBox.width - bbox.width) / 2 +\n    (bbox.width > titleBox.width ? 0 : (titleBox.width - bbox.width) / 2) + \", \" + (titleBox.height + halfPadding + 5) + \")\"\n  );\n  select4(text).attr(\n    \"transform\",\n    \"translate( \" + // (titleBox.width - bbox.width) / 2 +\n    (bbox.width < titleBox.width ? 0 : -(titleBox.width - bbox.width) / 2) + \", 0)\"\n  );\n  bbox = label.node().getBBox();\n  label.attr(\n    \"transform\",\n    \"translate(\" + -bbox.width / 2 + \", \" + (-bbox.height / 2 - halfPadding + 3) + \")\"\n  );\n  rect2.attr(\"class\", \"outer title-state\").attr(\"x\", -bbox.width / 2 - halfPadding).attr(\"y\", -bbox.height / 2 - halfPadding).attr(\"width\", bbox.width + node.padding).attr(\"height\", bbox.height + node.padding);\n  innerLine.attr(\"class\", \"divider\").attr(\"x1\", -bbox.width / 2 - halfPadding).attr(\"x2\", bbox.width / 2 + halfPadding).attr(\"y1\", -bbox.height / 2 - halfPadding + titleBox.height + halfPadding).attr(\"y2\", -bbox.height / 2 - halfPadding + titleBox.height + halfPadding);\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"rectWithTitle\");\nvar stadium = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const h = bbox.height + node.padding;\n  const w = bbox.width + h / 4 + node.padding;\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\").attr(\"style\", node.style).attr(\"rx\", h / 2).attr(\"ry\", h / 2).attr(\"x\", -w / 2).attr(\"y\", -h / 2).attr(\"width\", w).attr(\"height\", h);\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"stadium\");\nvar circle2 = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const circle3 = shapeSvg.insert(\"circle\", \":first-child\");\n  circle3.attr(\"style\", node.style).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"r\", bbox.width / 2 + halfPadding).attr(\"width\", bbox.width + node.padding).attr(\"height\", bbox.height + node.padding);\n  log.info(\"Circle main\");\n  updateNodeBounds(node, circle3);\n  node.intersect = function(point2) {\n    log.info(\"Circle intersect\", node, bbox.width / 2 + halfPadding, point2);\n    return intersect_default.circle(node, bbox.width / 2 + halfPadding, point2);\n  };\n  return shapeSvg;\n}, \"circle\");\nvar doublecircle = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const gap = 5;\n  const circleGroup = shapeSvg.insert(\"g\", \":first-child\");\n  const outerCircle = circleGroup.insert(\"circle\");\n  const innerCircle = circleGroup.insert(\"circle\");\n  circleGroup.attr(\"class\", node.class);\n  outerCircle.attr(\"style\", node.style).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"r\", bbox.width / 2 + halfPadding + gap).attr(\"width\", bbox.width + node.padding + gap * 2).attr(\"height\", bbox.height + node.padding + gap * 2);\n  innerCircle.attr(\"style\", node.style).attr(\"rx\", node.rx).attr(\"ry\", node.ry).attr(\"r\", bbox.width / 2 + halfPadding).attr(\"width\", bbox.width + node.padding).attr(\"height\", bbox.height + node.padding);\n  log.info(\"DoubleCircle main\");\n  updateNodeBounds(node, outerCircle);\n  node.intersect = function(point2) {\n    log.info(\"DoubleCircle intersect\", node, bbox.width / 2 + halfPadding + gap, point2);\n    return intersect_default.circle(node, bbox.width / 2 + halfPadding + gap, point2);\n  };\n  return shapeSvg;\n}, \"doublecircle\");\nvar subroutine = /* @__PURE__ */ __name(async (parent, node) => {\n  const { shapeSvg, bbox } = await labelHelper(\n    parent,\n    node,\n    getClassesFromNode(node, void 0),\n    true\n  );\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: 0, y: -h },\n    { x: 0, y: 0 },\n    { x: -8, y: 0 },\n    { x: w + 8, y: 0 },\n    { x: w + 8, y: -h },\n    { x: -8, y: -h },\n    { x: -8, y: 0 }\n  ];\n  const el = insertPolygonShape(shapeSvg, w, h, points);\n  el.attr(\"style\", node.style);\n  updateNodeBounds(node, el);\n  node.intersect = function(point2) {\n    return intersect_default.polygon(node, points, point2);\n  };\n  return shapeSvg;\n}, \"subroutine\");\nvar start = /* @__PURE__ */ __name((parent, node) => {\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  const circle3 = shapeSvg.insert(\"circle\", \":first-child\");\n  circle3.attr(\"class\", \"state-start\").attr(\"r\", 7).attr(\"width\", 14).attr(\"height\", 14);\n  updateNodeBounds(node, circle3);\n  node.intersect = function(point2) {\n    return intersect_default.circle(node, 7, point2);\n  };\n  return shapeSvg;\n}, \"start\");\nvar forkJoin = /* @__PURE__ */ __name((parent, node, dir) => {\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  let width = 70;\n  let height = 10;\n  if (dir === \"LR\") {\n    width = 10;\n    height = 70;\n  }\n  const shape = shapeSvg.append(\"rect\").attr(\"x\", -1 * width / 2).attr(\"y\", -1 * height / 2).attr(\"width\", width).attr(\"height\", height).attr(\"class\", \"fork-join\");\n  updateNodeBounds(node, shape);\n  node.height = node.height + node.padding / 2;\n  node.width = node.width + node.padding / 2;\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"forkJoin\");\nvar end = /* @__PURE__ */ __name((parent, node) => {\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", \"node default\").attr(\"id\", node.domId || node.id);\n  const innerCircle = shapeSvg.insert(\"circle\", \":first-child\");\n  const circle3 = shapeSvg.insert(\"circle\", \":first-child\");\n  circle3.attr(\"class\", \"state-start\").attr(\"r\", 7).attr(\"width\", 14).attr(\"height\", 14);\n  innerCircle.attr(\"class\", \"state-end\").attr(\"r\", 5).attr(\"width\", 10).attr(\"height\", 10);\n  updateNodeBounds(node, circle3);\n  node.intersect = function(point2) {\n    return intersect_default.circle(node, 7, point2);\n  };\n  return shapeSvg;\n}, \"end\");\nvar class_box = /* @__PURE__ */ __name((parent, node) => {\n  const halfPadding = node.padding / 2;\n  const rowPadding = 4;\n  const lineHeight = 8;\n  let classes2;\n  if (!node.classes) {\n    classes2 = \"node default\";\n  } else {\n    classes2 = \"node \" + node.classes;\n  }\n  const shapeSvg = parent.insert(\"g\").attr(\"class\", classes2).attr(\"id\", node.domId || node.id);\n  const rect2 = shapeSvg.insert(\"rect\", \":first-child\");\n  const topLine = shapeSvg.insert(\"line\");\n  const bottomLine = shapeSvg.insert(\"line\");\n  let maxWidth = 0;\n  let maxHeight = rowPadding;\n  const labelContainer = shapeSvg.insert(\"g\").attr(\"class\", \"label\");\n  let verticalPos = 0;\n  const hasInterface = node.classData.annotations?.[0];\n  const interfaceLabelText = node.classData.annotations[0] ? \"\\xAB\" + node.classData.annotations[0] + \"\\xBB\" : \"\";\n  const interfaceLabel = labelContainer.node().appendChild(createLabel_default(interfaceLabelText, node.labelStyle, true, true));\n  let interfaceBBox = interfaceLabel.getBBox();\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    const div = interfaceLabel.children[0];\n    const dv = select4(interfaceLabel);\n    interfaceBBox = div.getBoundingClientRect();\n    dv.attr(\"width\", interfaceBBox.width);\n    dv.attr(\"height\", interfaceBBox.height);\n  }\n  if (node.classData.annotations[0]) {\n    maxHeight += interfaceBBox.height + rowPadding;\n    maxWidth += interfaceBBox.width;\n  }\n  let classTitleString = node.classData.label;\n  if (node.classData.type !== void 0 && node.classData.type !== \"\") {\n    if (getConfig2().flowchart.htmlLabels) {\n      classTitleString += \"&lt;\" + node.classData.type + \"&gt;\";\n    } else {\n      classTitleString += \"<\" + node.classData.type + \">\";\n    }\n  }\n  const classTitleLabel = labelContainer.node().appendChild(createLabel_default(classTitleString, node.labelStyle, true, true));\n  select4(classTitleLabel).attr(\"class\", \"classTitle\");\n  let classTitleBBox = classTitleLabel.getBBox();\n  if (evaluate(getConfig2().flowchart.htmlLabels)) {\n    const div = classTitleLabel.children[0];\n    const dv = select4(classTitleLabel);\n    classTitleBBox = div.getBoundingClientRect();\n    dv.attr(\"width\", classTitleBBox.width);\n    dv.attr(\"height\", classTitleBBox.height);\n  }\n  maxHeight += classTitleBBox.height + rowPadding;\n  if (classTitleBBox.width > maxWidth) {\n    maxWidth = classTitleBBox.width;\n  }\n  const classAttributes = [];\n  node.classData.members.forEach((member) => {\n    const parsedInfo = member.getDisplayDetails();\n    let parsedText = parsedInfo.displayText;\n    if (getConfig2().flowchart.htmlLabels) {\n      parsedText = parsedText.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    const lbl = labelContainer.node().appendChild(\n      createLabel_default(\n        parsedText,\n        parsedInfo.cssStyle ? parsedInfo.cssStyle : node.labelStyle,\n        true,\n        true\n      )\n    );\n    let bbox = lbl.getBBox();\n    if (evaluate(getConfig2().flowchart.htmlLabels)) {\n      const div = lbl.children[0];\n      const dv = select4(lbl);\n      bbox = div.getBoundingClientRect();\n      dv.attr(\"width\", bbox.width);\n      dv.attr(\"height\", bbox.height);\n    }\n    if (bbox.width > maxWidth) {\n      maxWidth = bbox.width;\n    }\n    maxHeight += bbox.height + rowPadding;\n    classAttributes.push(lbl);\n  });\n  maxHeight += lineHeight;\n  const classMethods = [];\n  node.classData.methods.forEach((member) => {\n    const parsedInfo = member.getDisplayDetails();\n    let displayText = parsedInfo.displayText;\n    if (getConfig2().flowchart.htmlLabels) {\n      displayText = displayText.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    const lbl = labelContainer.node().appendChild(\n      createLabel_default(\n        displayText,\n        parsedInfo.cssStyle ? parsedInfo.cssStyle : node.labelStyle,\n        true,\n        true\n      )\n    );\n    let bbox = lbl.getBBox();\n    if (evaluate(getConfig2().flowchart.htmlLabels)) {\n      const div = lbl.children[0];\n      const dv = select4(lbl);\n      bbox = div.getBoundingClientRect();\n      dv.attr(\"width\", bbox.width);\n      dv.attr(\"height\", bbox.height);\n    }\n    if (bbox.width > maxWidth) {\n      maxWidth = bbox.width;\n    }\n    maxHeight += bbox.height + rowPadding;\n    classMethods.push(lbl);\n  });\n  maxHeight += lineHeight;\n  if (hasInterface) {\n    let diffX2 = (maxWidth - interfaceBBox.width) / 2;\n    select4(interfaceLabel).attr(\n      \"transform\",\n      \"translate( \" + (-1 * maxWidth / 2 + diffX2) + \", \" + -1 * maxHeight / 2 + \")\"\n    );\n    verticalPos = interfaceBBox.height + rowPadding;\n  }\n  let diffX = (maxWidth - classTitleBBox.width) / 2;\n  select4(classTitleLabel).attr(\n    \"transform\",\n    \"translate( \" + (-1 * maxWidth / 2 + diffX) + \", \" + (-1 * maxHeight / 2 + verticalPos) + \")\"\n  );\n  verticalPos += classTitleBBox.height + rowPadding;\n  topLine.attr(\"class\", \"divider\").attr(\"x1\", -maxWidth / 2 - halfPadding).attr(\"x2\", maxWidth / 2 + halfPadding).attr(\"y1\", -maxHeight / 2 - halfPadding + lineHeight + verticalPos).attr(\"y2\", -maxHeight / 2 - halfPadding + lineHeight + verticalPos);\n  verticalPos += lineHeight;\n  classAttributes.forEach((lbl) => {\n    select4(lbl).attr(\n      \"transform\",\n      \"translate( \" + -maxWidth / 2 + \", \" + (-1 * maxHeight / 2 + verticalPos + lineHeight / 2) + \")\"\n    );\n    const memberBBox = lbl?.getBBox();\n    verticalPos += (memberBBox?.height ?? 0) + rowPadding;\n  });\n  verticalPos += lineHeight;\n  bottomLine.attr(\"class\", \"divider\").attr(\"x1\", -maxWidth / 2 - halfPadding).attr(\"x2\", maxWidth / 2 + halfPadding).attr(\"y1\", -maxHeight / 2 - halfPadding + lineHeight + verticalPos).attr(\"y2\", -maxHeight / 2 - halfPadding + lineHeight + verticalPos);\n  verticalPos += lineHeight;\n  classMethods.forEach((lbl) => {\n    select4(lbl).attr(\n      \"transform\",\n      \"translate( \" + -maxWidth / 2 + \", \" + (-1 * maxHeight / 2 + verticalPos) + \")\"\n    );\n    const memberBBox = lbl?.getBBox();\n    verticalPos += (memberBBox?.height ?? 0) + rowPadding;\n  });\n  rect2.attr(\"style\", node.style).attr(\"class\", \"outer title-state\").attr(\"x\", -maxWidth / 2 - halfPadding).attr(\"y\", -(maxHeight / 2) - halfPadding).attr(\"width\", maxWidth + node.padding).attr(\"height\", maxHeight + node.padding);\n  updateNodeBounds(node, rect2);\n  node.intersect = function(point2) {\n    return intersect_default.rect(node, point2);\n  };\n  return shapeSvg;\n}, \"class_box\");\nvar shapes = {\n  rhombus: question,\n  composite,\n  question,\n  rect,\n  labelRect,\n  rectWithTitle,\n  choice,\n  circle: circle2,\n  doublecircle,\n  stadium,\n  hexagon,\n  block_arrow,\n  rect_left_inv_arrow,\n  lean_right,\n  lean_left,\n  trapezoid,\n  inv_trapezoid,\n  rect_right_inv_arrow,\n  cylinder,\n  start,\n  end,\n  note: note_default,\n  subroutine,\n  fork: forkJoin,\n  join: forkJoin,\n  class_box\n};\nvar nodeElems = {};\nvar insertNode = /* @__PURE__ */ __name(async (elem, node, renderOptions) => {\n  let newEl;\n  let el;\n  if (node.link) {\n    let target;\n    if (getConfig2().securityLevel === \"sandbox\") {\n      target = \"_top\";\n    } else if (node.linkTarget) {\n      target = node.linkTarget || \"_blank\";\n    }\n    newEl = elem.insert(\"svg:a\").attr(\"xlink:href\", node.link).attr(\"target\", target);\n    el = await shapes[node.shape](newEl, node, renderOptions);\n  } else {\n    el = await shapes[node.shape](elem, node, renderOptions);\n    newEl = el;\n  }\n  if (node.tooltip) {\n    el.attr(\"title\", node.tooltip);\n  }\n  if (node.class) {\n    el.attr(\"class\", \"node default \" + node.class);\n  }\n  nodeElems[node.id] = newEl;\n  if (node.haveCallback) {\n    nodeElems[node.id].attr(\"class\", nodeElems[node.id].attr(\"class\") + \" clickable\");\n  }\n  return newEl;\n}, \"insertNode\");\nvar positionNode = /* @__PURE__ */ __name((node) => {\n  const el = nodeElems[node.id];\n  log.trace(\n    \"Transforming node\",\n    node.diff,\n    node,\n    \"translate(\" + (node.x - node.width / 2 - 5) + \", \" + node.width / 2 + \")\"\n  );\n  const padding2 = 8;\n  const diff = node.diff || 0;\n  if (node.clusterNode) {\n    el.attr(\n      \"transform\",\n      \"translate(\" + (node.x + diff - node.width / 2) + \", \" + (node.y - node.height / 2 - padding2) + \")\"\n    );\n  } else {\n    el.attr(\"transform\", \"translate(\" + node.x + \", \" + node.y + \")\");\n  }\n  return diff;\n}, \"positionNode\");\n\n// src/diagrams/block/renderHelpers.ts\nfunction getNodeFromBlock(block, db2, positioned = false) {\n  const vertex = block;\n  let classStr = \"default\";\n  if ((vertex?.classes?.length || 0) > 0) {\n    classStr = (vertex?.classes ?? []).join(\" \");\n  }\n  classStr = classStr + \" flowchart-label\";\n  let radius = 0;\n  let shape = \"\";\n  let padding2;\n  switch (vertex.type) {\n    case \"round\":\n      radius = 5;\n      shape = \"rect\";\n      break;\n    case \"composite\":\n      radius = 0;\n      shape = \"composite\";\n      padding2 = 0;\n      break;\n    case \"square\":\n      shape = \"rect\";\n      break;\n    case \"diamond\":\n      shape = \"question\";\n      break;\n    case \"hexagon\":\n      shape = \"hexagon\";\n      break;\n    case \"block_arrow\":\n      shape = \"block_arrow\";\n      break;\n    case \"odd\":\n      shape = \"rect_left_inv_arrow\";\n      break;\n    case \"lean_right\":\n      shape = \"lean_right\";\n      break;\n    case \"lean_left\":\n      shape = \"lean_left\";\n      break;\n    case \"trapezoid\":\n      shape = \"trapezoid\";\n      break;\n    case \"inv_trapezoid\":\n      shape = \"inv_trapezoid\";\n      break;\n    case \"rect_left_inv_arrow\":\n      shape = \"rect_left_inv_arrow\";\n      break;\n    case \"circle\":\n      shape = \"circle\";\n      break;\n    case \"ellipse\":\n      shape = \"ellipse\";\n      break;\n    case \"stadium\":\n      shape = \"stadium\";\n      break;\n    case \"subroutine\":\n      shape = \"subroutine\";\n      break;\n    case \"cylinder\":\n      shape = \"cylinder\";\n      break;\n    case \"group\":\n      shape = \"rect\";\n      break;\n    case \"doublecircle\":\n      shape = \"doublecircle\";\n      break;\n    default:\n      shape = \"rect\";\n  }\n  const styles = getStylesFromArray(vertex?.styles ?? []);\n  const vertexText = vertex.label;\n  const bounds = vertex.size ?? { width: 0, height: 0, x: 0, y: 0 };\n  const node = {\n    labelStyle: styles.labelStyle,\n    shape,\n    labelText: vertexText,\n    rx: radius,\n    ry: radius,\n    class: classStr,\n    style: styles.style,\n    id: vertex.id,\n    directions: vertex.directions,\n    width: bounds.width,\n    height: bounds.height,\n    x: bounds.x,\n    y: bounds.y,\n    positioned,\n    intersect: void 0,\n    type: vertex.type,\n    padding: padding2 ?? getConfig()?.block?.padding ?? 0\n  };\n  return node;\n}\n__name(getNodeFromBlock, \"getNodeFromBlock\");\nasync function calculateBlockSize(elem, block, db2) {\n  const node = getNodeFromBlock(block, db2, false);\n  if (node.type === \"group\") {\n    return;\n  }\n  const config2 = getConfig();\n  const nodeEl = await insertNode(elem, node, { config: config2 });\n  const boundingBox = nodeEl.node().getBBox();\n  const obj = db2.getBlock(node.id);\n  obj.size = { width: boundingBox.width, height: boundingBox.height, x: 0, y: 0, node: nodeEl };\n  db2.setBlock(obj);\n  nodeEl.remove();\n}\n__name(calculateBlockSize, \"calculateBlockSize\");\nasync function insertBlockPositioned(elem, block, db2) {\n  const node = getNodeFromBlock(block, db2, true);\n  const obj = db2.getBlock(node.id);\n  if (obj.type !== \"space\") {\n    const config2 = getConfig();\n    await insertNode(elem, node, { config: config2 });\n    block.intersect = node?.intersect;\n    positionNode(node);\n  }\n}\n__name(insertBlockPositioned, \"insertBlockPositioned\");\nasync function performOperations(elem, blocks2, db2, operation) {\n  for (const block of blocks2) {\n    await operation(elem, block, db2);\n    if (block.children) {\n      await performOperations(elem, block.children, db2, operation);\n    }\n  }\n}\n__name(performOperations, \"performOperations\");\nasync function calculateBlockSizes(elem, blocks2, db2) {\n  await performOperations(elem, blocks2, db2, calculateBlockSize);\n}\n__name(calculateBlockSizes, \"calculateBlockSizes\");\nasync function insertBlocks(elem, blocks2, db2) {\n  await performOperations(elem, blocks2, db2, insertBlockPositioned);\n}\n__name(insertBlocks, \"insertBlocks\");\nasync function insertEdges(elem, edges, blocks2, db2, id) {\n  const g = new graphlib.Graph({\n    multigraph: true,\n    compound: true\n  });\n  g.setGraph({\n    rankdir: \"TB\",\n    nodesep: 10,\n    ranksep: 10,\n    marginx: 8,\n    marginy: 8\n  });\n  for (const block of blocks2) {\n    if (block.size) {\n      g.setNode(block.id, {\n        width: block.size.width,\n        height: block.size.height,\n        intersect: block.intersect\n      });\n    }\n  }\n  for (const edge of edges) {\n    if (edge.start && edge.end) {\n      const startBlock = db2.getBlock(edge.start);\n      const endBlock = db2.getBlock(edge.end);\n      if (startBlock?.size && endBlock?.size) {\n        const start2 = startBlock.size;\n        const end2 = endBlock.size;\n        const points = [\n          { x: start2.x, y: start2.y },\n          { x: start2.x + (end2.x - start2.x) / 2, y: start2.y + (end2.y - start2.y) / 2 },\n          { x: end2.x, y: end2.y }\n        ];\n        insertEdge(\n          elem,\n          { v: edge.start, w: edge.end, name: edge.id },\n          {\n            ...edge,\n            arrowTypeEnd: edge.arrowTypeEnd,\n            arrowTypeStart: edge.arrowTypeStart,\n            points,\n            classes: \"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1\"\n          },\n          void 0,\n          \"block\",\n          g,\n          id\n        );\n        if (edge.label) {\n          await insertEdgeLabel(elem, {\n            ...edge,\n            label: edge.label,\n            labelStyle: \"stroke: #333; stroke-width: 1.5px;fill:none;\",\n            arrowTypeEnd: edge.arrowTypeEnd,\n            arrowTypeStart: edge.arrowTypeStart,\n            points,\n            classes: \"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1\"\n          });\n          positionEdgeLabel(\n            { ...edge, x: points[1].x, y: points[1].y },\n            {\n              originalPath: points\n            }\n          );\n        }\n      }\n    }\n  }\n}\n__name(insertEdges, \"insertEdges\");\n\n// src/diagrams/block/blockRenderer.ts\nvar getClasses2 = /* @__PURE__ */ __name(function(text, diagObj) {\n  return diagObj.db.getClasses();\n}, \"getClasses\");\nvar draw = /* @__PURE__ */ __name(async function(text, id, _version, diagObj) {\n  const { securityLevel, block: conf } = getConfig();\n  const db2 = diagObj.db;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = d3select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? d3select(sandboxElement.nodes()[0].contentDocument.body) : d3select(\"body\");\n  const svg = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n  const markers2 = [\"point\", \"circle\", \"cross\"];\n  markers_default(svg, markers2, diagObj.type, id);\n  const bl = db2.getBlocks();\n  const blArr = db2.getBlocksFlat();\n  const edges = db2.getEdges();\n  const nodes = svg.insert(\"g\").attr(\"class\", \"block\");\n  await calculateBlockSizes(nodes, bl, db2);\n  const bounds = layout(db2);\n  await insertBlocks(nodes, bl, db2);\n  await insertEdges(nodes, edges, blArr, db2, id);\n  if (bounds) {\n    const bounds2 = bounds;\n    const magicFactor = Math.max(1, Math.round(0.125 * (bounds2.width / bounds2.height)));\n    const height = bounds2.height + magicFactor + 10;\n    const width = bounds2.width + 10;\n    const { useMaxWidth } = conf;\n    configureSvgSize(svg, height, width, !!useMaxWidth);\n    log.debug(\"Here Bounds\", bounds, bounds2);\n    svg.attr(\n      \"viewBox\",\n      `${bounds2.x - 5} ${bounds2.y - 5} ${bounds2.width + 10} ${bounds2.height + 10}`\n    );\n  }\n}, \"draw\");\nvar blockRenderer_default = {\n  draw,\n  getClasses: getClasses2\n};\n\n// src/diagrams/block/blockDiagram.ts\nvar diagram = {\n  parser: block_default,\n  db: blockDB_default,\n  renderer: blockRenderer_default,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}