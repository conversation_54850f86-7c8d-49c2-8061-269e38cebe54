{"version": 3, "file": "270.dced80a7f5cbf1705712.js?v=dced80a7f5cbf1705712", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,sBAAsB,mBAAO,CAAC,KAAuB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6EAA6E,oBAAoB;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;AChQa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,sBAAsB;AACtB,uBAAuB,mBAAO,CAAC,KAAkB;AACjD;AACA;AACA;AACA,sBAAsB;AACtB;;;;;;;ACRa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,0BAA0B;AAC1B;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,mCAAmC;AACnC;AACA;AACA;AACA,wFAAwF,oBAAoB;AAC5G;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,UAAU;AAC1F;AACA,oFAAoF,yBAAyB;AAC7G;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA,qFAAqF,UAAU;AAC/F;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA,sEAAsE,UAAU;AAChF;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,0BAA0B;AAC1B", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/adaptors/HTMLAdaptor.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/adaptors/browserAdaptor.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/DOMAdaptor.js"], "sourcesContent": ["\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.HTMLAdaptor = void 0;\nvar DOMAdaptor_js_1 = require(\"../core/DOMAdaptor.js\");\nvar HTMLAdaptor = (function (_super) {\n    __extends(HTMLAdaptor, _super);\n    function HTMLAdaptor(window) {\n        var _this = _super.call(this, window.document) || this;\n        _this.window = window;\n        _this.parser = new window.DOMParser();\n        return _this;\n    }\n    HTMLAdaptor.prototype.parse = function (text, format) {\n        if (format === void 0) { format = 'text/html'; }\n        return this.parser.parseFromString(text, format);\n    };\n    HTMLAdaptor.prototype.create = function (kind, ns) {\n        return (ns ?\n            this.document.createElementNS(ns, kind) :\n            this.document.createElement(kind));\n    };\n    HTMLAdaptor.prototype.text = function (text) {\n        return this.document.createTextNode(text);\n    };\n    HTMLAdaptor.prototype.head = function (doc) {\n        return doc.head || doc;\n    };\n    HTMLAdaptor.prototype.body = function (doc) {\n        return doc.body || doc;\n    };\n    HTMLAdaptor.prototype.root = function (doc) {\n        return doc.documentElement || doc;\n    };\n    HTMLAdaptor.prototype.doctype = function (doc) {\n        return (doc.doctype ? \"<!DOCTYPE \".concat(doc.doctype.name, \">\") : '');\n    };\n    HTMLAdaptor.prototype.tags = function (node, name, ns) {\n        if (ns === void 0) { ns = null; }\n        var nodes = (ns ? node.getElementsByTagNameNS(ns, name) : node.getElementsByTagName(name));\n        return Array.from(nodes);\n    };\n    HTMLAdaptor.prototype.getElements = function (nodes, _document) {\n        var e_1, _a;\n        var containers = [];\n        try {\n            for (var nodes_1 = __values(nodes), nodes_1_1 = nodes_1.next(); !nodes_1_1.done; nodes_1_1 = nodes_1.next()) {\n                var node = nodes_1_1.value;\n                if (typeof (node) === 'string') {\n                    containers = containers.concat(Array.from(this.document.querySelectorAll(node)));\n                }\n                else if (Array.isArray(node)) {\n                    containers = containers.concat(Array.from(node));\n                }\n                else if (node instanceof this.window.NodeList || node instanceof this.window.HTMLCollection) {\n                    containers = containers.concat(Array.from(node));\n                }\n                else {\n                    containers.push(node);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (nodes_1_1 && !nodes_1_1.done && (_a = nodes_1.return)) _a.call(nodes_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return containers;\n    };\n    HTMLAdaptor.prototype.contains = function (container, node) {\n        return container.contains(node);\n    };\n    HTMLAdaptor.prototype.parent = function (node) {\n        return node.parentNode;\n    };\n    HTMLAdaptor.prototype.append = function (node, child) {\n        return node.appendChild(child);\n    };\n    HTMLAdaptor.prototype.insert = function (nchild, ochild) {\n        return this.parent(ochild).insertBefore(nchild, ochild);\n    };\n    HTMLAdaptor.prototype.remove = function (child) {\n        return this.parent(child).removeChild(child);\n    };\n    HTMLAdaptor.prototype.replace = function (nnode, onode) {\n        return this.parent(onode).replaceChild(nnode, onode);\n    };\n    HTMLAdaptor.prototype.clone = function (node) {\n        return node.cloneNode(true);\n    };\n    HTMLAdaptor.prototype.split = function (node, n) {\n        return node.splitText(n);\n    };\n    HTMLAdaptor.prototype.next = function (node) {\n        return node.nextSibling;\n    };\n    HTMLAdaptor.prototype.previous = function (node) {\n        return node.previousSibling;\n    };\n    HTMLAdaptor.prototype.firstChild = function (node) {\n        return node.firstChild;\n    };\n    HTMLAdaptor.prototype.lastChild = function (node) {\n        return node.lastChild;\n    };\n    HTMLAdaptor.prototype.childNodes = function (node) {\n        return Array.from(node.childNodes);\n    };\n    HTMLAdaptor.prototype.childNode = function (node, i) {\n        return node.childNodes[i];\n    };\n    HTMLAdaptor.prototype.kind = function (node) {\n        var n = node.nodeType;\n        return (n === 1 || n === 3 || n === 8 ? node.nodeName.toLowerCase() : '');\n    };\n    HTMLAdaptor.prototype.value = function (node) {\n        return node.nodeValue || '';\n    };\n    HTMLAdaptor.prototype.textContent = function (node) {\n        return node.textContent;\n    };\n    HTMLAdaptor.prototype.innerHTML = function (node) {\n        return node.innerHTML;\n    };\n    HTMLAdaptor.prototype.outerHTML = function (node) {\n        return node.outerHTML;\n    };\n    HTMLAdaptor.prototype.serializeXML = function (node) {\n        var serializer = new this.window.XMLSerializer();\n        return serializer.serializeToString(node);\n    };\n    HTMLAdaptor.prototype.setAttribute = function (node, name, value, ns) {\n        if (ns === void 0) { ns = null; }\n        if (!ns) {\n            return node.setAttribute(name, value);\n        }\n        name = ns.replace(/.*\\//, '') + ':' + name.replace(/^.*:/, '');\n        return node.setAttributeNS(ns, name, value);\n    };\n    HTMLAdaptor.prototype.getAttribute = function (node, name) {\n        return node.getAttribute(name);\n    };\n    HTMLAdaptor.prototype.removeAttribute = function (node, name) {\n        return node.removeAttribute(name);\n    };\n    HTMLAdaptor.prototype.hasAttribute = function (node, name) {\n        return node.hasAttribute(name);\n    };\n    HTMLAdaptor.prototype.allAttributes = function (node) {\n        return Array.from(node.attributes).map(function (x) {\n            return { name: x.name, value: x.value };\n        });\n    };\n    HTMLAdaptor.prototype.addClass = function (node, name) {\n        if (node.classList) {\n            node.classList.add(name);\n        }\n        else {\n            node.className = (node.className + ' ' + name).trim();\n        }\n    };\n    HTMLAdaptor.prototype.removeClass = function (node, name) {\n        if (node.classList) {\n            node.classList.remove(name);\n        }\n        else {\n            node.className = node.className.split(/ /).filter(function (c) { return c !== name; }).join(' ');\n        }\n    };\n    HTMLAdaptor.prototype.hasClass = function (node, name) {\n        if (node.classList) {\n            return node.classList.contains(name);\n        }\n        return node.className.split(/ /).indexOf(name) >= 0;\n    };\n    HTMLAdaptor.prototype.setStyle = function (node, name, value) {\n        node.style[name] = value;\n    };\n    HTMLAdaptor.prototype.getStyle = function (node, name) {\n        return node.style[name];\n    };\n    HTMLAdaptor.prototype.allStyles = function (node) {\n        return node.style.cssText;\n    };\n    HTMLAdaptor.prototype.insertRules = function (node, rules) {\n        var e_2, _a;\n        try {\n            for (var _b = __values(rules.reverse()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var rule = _c.value;\n                try {\n                    node.sheet.insertRule(rule, 0);\n                }\n                catch (e) {\n                    console.warn(\"MathJax: can't insert css rule '\".concat(rule, \"': \").concat(e.message));\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    HTMLAdaptor.prototype.fontSize = function (node) {\n        var style = this.window.getComputedStyle(node);\n        return parseFloat(style.fontSize);\n    };\n    HTMLAdaptor.prototype.fontFamily = function (node) {\n        var style = this.window.getComputedStyle(node);\n        return style.fontFamily || '';\n    };\n    HTMLAdaptor.prototype.nodeSize = function (node, em, local) {\n        if (em === void 0) { em = 1; }\n        if (local === void 0) { local = false; }\n        if (local && node.getBBox) {\n            var _a = node.getBBox(), width = _a.width, height = _a.height;\n            return [width / em, height / em];\n        }\n        return [node.offsetWidth / em, node.offsetHeight / em];\n    };\n    HTMLAdaptor.prototype.nodeBBox = function (node) {\n        var _a = node.getBoundingClientRect(), left = _a.left, right = _a.right, top = _a.top, bottom = _a.bottom;\n        return { left: left, right: right, top: top, bottom: bottom };\n    };\n    return HTMLAdaptor;\n}(DOMAdaptor_js_1.AbstractDOMAdaptor));\nexports.HTMLAdaptor = HTMLAdaptor;\n//# sourceMappingURL=HTMLAdaptor.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.browserAdaptor = void 0;\nvar HTMLAdaptor_js_1 = require(\"./HTMLAdaptor.js\");\nfunction browserAdaptor() {\n    return new HTMLAdaptor_js_1.HTMLAdaptor(window);\n}\nexports.browserAdaptor = browserAdaptor;\n//# sourceMappingURL=browserAdaptor.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractDOMAdaptor = void 0;\nvar AbstractDOMAdaptor = (function () {\n    function AbstractDOMAdaptor(document) {\n        if (document === void 0) { document = null; }\n        this.document = document;\n    }\n    AbstractDOMAdaptor.prototype.node = function (kind, def, children, ns) {\n        var e_1, _a;\n        if (def === void 0) { def = {}; }\n        if (children === void 0) { children = []; }\n        var node = this.create(kind, ns);\n        this.setAttributes(node, def);\n        try {\n            for (var children_1 = __values(children), children_1_1 = children_1.next(); !children_1_1.done; children_1_1 = children_1.next()) {\n                var child = children_1_1.value;\n                this.append(node, child);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (children_1_1 && !children_1_1.done && (_a = children_1.return)) _a.call(children_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return node;\n    };\n    AbstractDOMAdaptor.prototype.setAttributes = function (node, def) {\n        var e_2, _a, e_3, _b, e_4, _c;\n        if (def.style && typeof (def.style) !== 'string') {\n            try {\n                for (var _d = __values(Object.keys(def.style)), _e = _d.next(); !_e.done; _e = _d.next()) {\n                    var key = _e.value;\n                    this.setStyle(node, key.replace(/-([a-z])/g, function (_m, c) { return c.toUpperCase(); }), def.style[key]);\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n        if (def.properties) {\n            try {\n                for (var _f = __values(Object.keys(def.properties)), _g = _f.next(); !_g.done; _g = _f.next()) {\n                    var key = _g.value;\n                    node[key] = def.properties[key];\n                }\n            }\n            catch (e_3_1) { e_3 = { error: e_3_1 }; }\n            finally {\n                try {\n                    if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                }\n                finally { if (e_3) throw e_3.error; }\n            }\n        }\n        try {\n            for (var _h = __values(Object.keys(def)), _j = _h.next(); !_j.done; _j = _h.next()) {\n                var key = _j.value;\n                if ((key !== 'style' || typeof (def.style) === 'string') && key !== 'properties') {\n                    this.setAttribute(node, key, def[key]);\n                }\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n    };\n    AbstractDOMAdaptor.prototype.replace = function (nnode, onode) {\n        this.insert(nnode, onode);\n        this.remove(onode);\n        return onode;\n    };\n    AbstractDOMAdaptor.prototype.childNode = function (node, i) {\n        return this.childNodes(node)[i];\n    };\n    AbstractDOMAdaptor.prototype.allClasses = function (node) {\n        var classes = this.getAttribute(node, 'class');\n        return (!classes ? [] :\n            classes.replace(/  +/g, ' ').replace(/^ /, '').replace(/ $/, '').split(/ /));\n    };\n    return AbstractDOMAdaptor;\n}());\nexports.AbstractDOMAdaptor = AbstractDOMAdaptor;\n//# sourceMappingURL=DOMAdaptor.js.map"], "names": [], "sourceRoot": ""}