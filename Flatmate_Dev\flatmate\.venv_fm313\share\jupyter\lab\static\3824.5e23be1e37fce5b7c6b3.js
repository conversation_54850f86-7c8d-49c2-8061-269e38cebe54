(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[3824],{55580:(r,t,e)=>{var n=e(56110),o=e(9325);var a=n(o,"DataView");r.exports=a},21549:(r,t,e)=>{var n=e(22032),o=e(63862),a=e(66721),i=e(12749),u=e(35749);function s(r){var t=-1,e=r==null?0:r.length;this.clear();while(++t<e){var n=r[t];this.set(n[0],n[1])}}s.prototype.clear=n;s.prototype["delete"]=o;s.prototype.get=a;s.prototype.has=i;s.prototype.set=u;r.exports=s},80079:(r,t,e)=>{var n=e(63702),o=e(70080),a=e(24739),i=e(48655),u=e(31175);function s(r){var t=-1,e=r==null?0:r.length;this.clear();while(++t<e){var n=r[t];this.set(n[0],n[1])}}s.prototype.clear=n;s.prototype["delete"]=o;s.prototype.get=a;s.prototype.has=i;s.prototype.set=u;r.exports=s},68223:(r,t,e)=>{var n=e(56110),o=e(9325);var a=n(o,"Map");r.exports=a},53661:(r,t,e)=>{var n=e(63040),o=e(17670),a=e(90289),i=e(4509),u=e(72949);function s(r){var t=-1,e=r==null?0:r.length;this.clear();while(++t<e){var n=r[t];this.set(n[0],n[1])}}s.prototype.clear=n;s.prototype["delete"]=o;s.prototype.get=a;s.prototype.has=i;s.prototype.set=u;r.exports=s},32804:(r,t,e)=>{var n=e(56110),o=e(9325);var a=n(o,"Promise");r.exports=a},76545:(r,t,e)=>{var n=e(56110),o=e(9325);var a=n(o,"Set");r.exports=a},37217:(r,t,e)=>{var n=e(80079),o=e(51420),a=e(90938),i=e(63605),u=e(29817),s=e(80945);function c(r){var t=this.__data__=new n(r);this.size=t.size}c.prototype.clear=o;c.prototype["delete"]=a;c.prototype.get=i;c.prototype.has=u;c.prototype.set=s;r.exports=c},51873:(r,t,e)=>{var n=e(9325);var o=n.Symbol;r.exports=o},37828:(r,t,e)=>{var n=e(9325);var o=n.Uint8Array;r.exports=o},28303:(r,t,e)=>{var n=e(56110),o=e(9325);var a=n(o,"WeakMap");r.exports=a},79770:r=>{function t(r,t){var e=-1,n=r==null?0:r.length,o=0,a=[];while(++e<n){var i=r[e];if(t(i,e,r)){a[o++]=i}}return a}r.exports=t},70695:(r,t,e)=>{var n=e(78096),o=e(72428),a=e(56449),i=e(3656),u=e(30361),s=e(37167);var c=Object.prototype;var p=c.hasOwnProperty;function v(r,t){var e=a(r),c=!e&&o(r),v=!e&&!c&&i(r),f=!e&&!c&&!v&&s(r),l=e||c||v||f,h=l?n(r.length,String):[],y=h.length;for(var _ in r){if((t||p.call(r,_))&&!(l&&(_=="length"||v&&(_=="offset"||_=="parent")||f&&(_=="buffer"||_=="byteLength"||_=="byteOffset")||u(_,y)))){h.push(_)}}return h}r.exports=v},34932:r=>{function t(r,t){var e=-1,n=r==null?0:r.length,o=Array(n);while(++e<n){o[e]=t(r[e],e,r)}return o}r.exports=t},14528:r=>{function t(r,t){var e=-1,n=t.length,o=r.length;while(++e<n){r[o+e]=t[e]}return r}r.exports=t},26025:(r,t,e)=>{var n=e(75288);function o(r,t){var e=r.length;while(e--){if(n(r[e][0],t)){return e}}return-1}r.exports=o},47422:(r,t,e)=>{var n=e(31769),o=e(77797);function a(r,t){t=n(t,r);var e=0,a=t.length;while(r!=null&&e<a){r=r[o(t[e++])]}return e&&e==a?r:undefined}r.exports=a},82199:(r,t,e)=>{var n=e(14528),o=e(56449);function a(r,t,e){var a=t(r);return o(r)?a:n(a,e(r))}r.exports=a},72552:(r,t,e)=>{var n=e(51873),o=e(659),a=e(59350);var i="[object Null]",u="[object Undefined]";var s=n?n.toStringTag:undefined;function c(r){if(r==null){return r===undefined?u:i}return s&&s in Object(r)?o(r):a(r)}r.exports=c},27534:(r,t,e)=>{var n=e(72552),o=e(40346);var a="[object Arguments]";function i(r){return o(r)&&n(r)==a}r.exports=i},45083:(r,t,e)=>{var n=e(1882),o=e(87296),a=e(23805),i=e(47473);var u=/[\\^$.*+?()[\]{}|]/g;var s=/^\[object .+?Constructor\]$/;var c=Function.prototype,p=Object.prototype;var v=c.toString;var f=p.hasOwnProperty;var l=RegExp("^"+v.call(f).replace(u,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function h(r){if(!a(r)||o(r)){return false}var t=n(r)?l:s;return t.test(i(r))}r.exports=h},4901:(r,t,e)=>{var n=e(72552),o=e(30294),a=e(40346);var i="[object Arguments]",u="[object Array]",s="[object Boolean]",c="[object Date]",p="[object Error]",v="[object Function]",f="[object Map]",l="[object Number]",h="[object Object]",y="[object RegExp]",_="[object Set]",b="[object String]",x="[object WeakMap]";var d="[object ArrayBuffer]",j="[object DataView]",g="[object Float32Array]",w="[object Float64Array]",O="[object Int8Array]",m="[object Int16Array]",A="[object Int32Array]",z="[object Uint8Array]",S="[object Uint8ClampedArray]",P="[object Uint16Array]",k="[object Uint32Array]";var $={};$[g]=$[w]=$[O]=$[m]=$[A]=$[z]=$[S]=$[P]=$[k]=true;$[i]=$[u]=$[d]=$[s]=$[j]=$[c]=$[p]=$[v]=$[f]=$[l]=$[h]=$[y]=$[_]=$[b]=$[x]=false;function F(r){return a(r)&&o(r.length)&&!!$[n(r)]}r.exports=F},88984:(r,t,e)=>{var n=e(55527),o=e(3650);var a=Object.prototype;var i=a.hasOwnProperty;function u(r){if(!n(r)){return o(r)}var t=[];for(var e in Object(r)){if(i.call(r,e)&&e!="constructor"){t.push(e)}}return t}r.exports=u},78096:r=>{function t(r,t){var e=-1,n=Array(r);while(++e<r){n[e]=t(e)}return n}r.exports=t},77556:(r,t,e)=>{var n=e(51873),o=e(34932),a=e(56449),i=e(44394);var u=1/0;var s=n?n.prototype:undefined,c=s?s.toString:undefined;function p(r){if(typeof r=="string"){return r}if(a(r)){return o(r,p)+""}if(i(r)){return c?c.call(r):""}var t=r+"";return t=="0"&&1/r==-u?"-0":t}r.exports=p},27301:r=>{function t(r){return function(t){return r(t)}}r.exports=t},31769:(r,t,e)=>{var n=e(56449),o=e(28586),a=e(61802),i=e(13222);function u(r,t){if(n(r)){return r}return o(r,t)?[r]:a(i(r))}r.exports=u},55481:(r,t,e)=>{var n=e(9325);var o=n["__core-js_shared__"];r.exports=o},34840:(r,t,e)=>{var n=typeof e.g=="object"&&e.g&&e.g.Object===Object&&e.g;r.exports=n},50002:(r,t,e)=>{var n=e(82199),o=e(4664),a=e(95950);function i(r){return n(r,a,o)}r.exports=i},12651:(r,t,e)=>{var n=e(74218);function o(r,t){var e=r.__data__;return n(t)?e[typeof t=="string"?"string":"hash"]:e.map}r.exports=o},56110:(r,t,e)=>{var n=e(45083),o=e(10392);function a(r,t){var e=o(r,t);return n(e)?e:undefined}r.exports=a},659:(r,t,e)=>{var n=e(51873);var o=Object.prototype;var a=o.hasOwnProperty;var i=o.toString;var u=n?n.toStringTag:undefined;function s(r){var t=a.call(r,u),e=r[u];try{r[u]=undefined;var n=true}catch(s){}var o=i.call(r);if(n){if(t){r[u]=e}else{delete r[u]}}return o}r.exports=s},4664:(r,t,e)=>{var n=e(79770),o=e(63345);var a=Object.prototype;var i=a.propertyIsEnumerable;var u=Object.getOwnPropertySymbols;var s=!u?o:function(r){if(r==null){return[]}r=Object(r);return n(u(r),(function(t){return i.call(r,t)}))};r.exports=s},5861:(r,t,e)=>{var n=e(55580),o=e(68223),a=e(32804),i=e(76545),u=e(28303),s=e(72552),c=e(47473);var p="[object Map]",v="[object Object]",f="[object Promise]",l="[object Set]",h="[object WeakMap]";var y="[object DataView]";var _=c(n),b=c(o),x=c(a),d=c(i),j=c(u);var g=s;if(n&&g(new n(new ArrayBuffer(1)))!=y||o&&g(new o)!=p||a&&g(a.resolve())!=f||i&&g(new i)!=l||u&&g(new u)!=h){g=function(r){var t=s(r),e=t==v?r.constructor:undefined,n=e?c(e):"";if(n){switch(n){case _:return y;case b:return p;case x:return f;case d:return l;case j:return h}}return t}}r.exports=g},10392:r=>{function t(r,t){return r==null?undefined:r[t]}r.exports=t},22032:(r,t,e)=>{var n=e(81042);function o(){this.__data__=n?n(null):{};this.size=0}r.exports=o},63862:r=>{function t(r){var t=this.has(r)&&delete this.__data__[r];this.size-=t?1:0;return t}r.exports=t},66721:(r,t,e)=>{var n=e(81042);var o="__lodash_hash_undefined__";var a=Object.prototype;var i=a.hasOwnProperty;function u(r){var t=this.__data__;if(n){var e=t[r];return e===o?undefined:e}return i.call(t,r)?t[r]:undefined}r.exports=u},12749:(r,t,e)=>{var n=e(81042);var o=Object.prototype;var a=o.hasOwnProperty;function i(r){var t=this.__data__;return n?t[r]!==undefined:a.call(t,r)}r.exports=i},35749:(r,t,e)=>{var n=e(81042);var o="__lodash_hash_undefined__";function a(r,t){var e=this.__data__;this.size+=this.has(r)?0:1;e[r]=n&&t===undefined?o:t;return this}r.exports=a},30361:r=>{var t=9007199254740991;var e=/^(?:0|[1-9]\d*)$/;function n(r,n){var o=typeof r;n=n==null?t:n;return!!n&&(o=="number"||o!="symbol"&&e.test(r))&&(r>-1&&r%1==0&&r<n)}r.exports=n},28586:(r,t,e)=>{var n=e(56449),o=e(44394);var a=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,i=/^\w*$/;function u(r,t){if(n(r)){return false}var e=typeof r;if(e=="number"||e=="symbol"||e=="boolean"||r==null||o(r)){return true}return i.test(r)||!a.test(r)||t!=null&&r in Object(t)}r.exports=u},74218:r=>{function t(r){var t=typeof r;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?r!=="__proto__":r===null}r.exports=t},87296:(r,t,e)=>{var n=e(55481);var o=function(){var r=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}();function a(r){return!!o&&o in r}r.exports=a},55527:r=>{var t=Object.prototype;function e(r){var e=r&&r.constructor,n=typeof e=="function"&&e.prototype||t;return r===n}r.exports=e},63702:r=>{function t(){this.__data__=[];this.size=0}r.exports=t},70080:(r,t,e)=>{var n=e(26025);var o=Array.prototype;var a=o.splice;function i(r){var t=this.__data__,e=n(t,r);if(e<0){return false}var o=t.length-1;if(e==o){t.pop()}else{a.call(t,e,1)}--this.size;return true}r.exports=i},24739:(r,t,e)=>{var n=e(26025);function o(r){var t=this.__data__,e=n(t,r);return e<0?undefined:t[e][1]}r.exports=o},48655:(r,t,e)=>{var n=e(26025);function o(r){return n(this.__data__,r)>-1}r.exports=o},31175:(r,t,e)=>{var n=e(26025);function o(r,t){var e=this.__data__,o=n(e,r);if(o<0){++this.size;e.push([r,t])}else{e[o][1]=t}return this}r.exports=o},63040:(r,t,e)=>{var n=e(21549),o=e(80079),a=e(68223);function i(){this.size=0;this.__data__={hash:new n,map:new(a||o),string:new n}}r.exports=i},17670:(r,t,e)=>{var n=e(12651);function o(r){var t=n(this,r)["delete"](r);this.size-=t?1:0;return t}r.exports=o},90289:(r,t,e)=>{var n=e(12651);function o(r){return n(this,r).get(r)}r.exports=o},4509:(r,t,e)=>{var n=e(12651);function o(r){return n(this,r).has(r)}r.exports=o},72949:(r,t,e)=>{var n=e(12651);function o(r,t){var e=n(this,r),o=e.size;e.set(r,t);this.size+=e.size==o?0:1;return this}r.exports=o},62224:(r,t,e)=>{var n=e(50104);var o=500;function a(r){var t=n(r,(function(r){if(e.size===o){e.clear()}return r}));var e=t.cache;return t}r.exports=a},81042:(r,t,e)=>{var n=e(56110);var o=n(Object,"create");r.exports=o},3650:(r,t,e)=>{var n=e(74335);var o=n(Object.keys,Object);r.exports=o},86009:(r,t,e)=>{r=e.nmd(r);var n=e(34840);var o=true&&t&&!t.nodeType&&t;var a=o&&"object"=="object"&&r&&!r.nodeType&&r;var i=a&&a.exports===o;var u=i&&n.process;var s=function(){try{var r=a&&a.require&&a.require("util").types;if(r){return r}return u&&u.binding&&u.binding("util")}catch(t){}}();r.exports=s},59350:r=>{var t=Object.prototype;var e=t.toString;function n(r){return e.call(r)}r.exports=n},74335:r=>{function t(r,t){return function(e){return r(t(e))}}r.exports=t},9325:(r,t,e)=>{var n=e(34840);var o=typeof self=="object"&&self&&self.Object===Object&&self;var a=n||o||Function("return this")();r.exports=a},51420:(r,t,e)=>{var n=e(80079);function o(){this.__data__=new n;this.size=0}r.exports=o},90938:r=>{function t(r){var t=this.__data__,e=t["delete"](r);this.size=t.size;return e}r.exports=t},63605:r=>{function t(r){return this.__data__.get(r)}r.exports=t},29817:r=>{function t(r){return this.__data__.has(r)}r.exports=t},80945:(r,t,e)=>{var n=e(80079),o=e(68223),a=e(53661);var i=200;function u(r,t){var e=this.__data__;if(e instanceof n){var u=e.__data__;if(!o||u.length<i-1){u.push([r,t]);this.size=++e.size;return this}e=this.__data__=new a(u)}e.set(r,t);this.size=e.size;return this}r.exports=u},61802:(r,t,e)=>{var n=e(62224);var o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;var a=/\\(\\)?/g;var i=n((function(r){var t=[];if(r.charCodeAt(0)===46){t.push("")}r.replace(o,(function(r,e,n,o){t.push(n?o.replace(a,"$1"):e||r)}));return t}));r.exports=i},77797:(r,t,e)=>{var n=e(44394);var o=1/0;function a(r){if(typeof r=="string"||n(r)){return r}var t=r+"";return t=="0"&&1/r==-o?"-0":t}r.exports=a},47473:r=>{var t=Function.prototype;var e=t.toString;function n(r){if(r!=null){try{return e.call(r)}catch(t){}try{return r+""}catch(t){}}return""}r.exports=n},75288:r=>{function t(r,t){return r===t||r!==r&&t!==t}r.exports=t},58156:(r,t,e)=>{var n=e(47422);function o(r,t,e){var o=r==null?undefined:n(r,t);return o===undefined?e:o}r.exports=o},72428:(r,t,e)=>{var n=e(27534),o=e(40346);var a=Object.prototype;var i=a.hasOwnProperty;var u=a.propertyIsEnumerable;var s=n(function(){return arguments}())?n:function(r){return o(r)&&i.call(r,"callee")&&!u.call(r,"callee")};r.exports=s},56449:r=>{var t=Array.isArray;r.exports=t},64894:(r,t,e)=>{var n=e(1882),o=e(30294);function a(r){return r!=null&&o(r.length)&&!n(r)}r.exports=a},3656:(r,t,e)=>{r=e.nmd(r);var n=e(9325),o=e(89935);var a=true&&t&&!t.nodeType&&t;var i=a&&"object"=="object"&&r&&!r.nodeType&&r;var u=i&&i.exports===a;var s=u?n.Buffer:undefined;var c=s?s.isBuffer:undefined;var p=c||o;r.exports=p},1882:(r,t,e)=>{var n=e(72552),o=e(23805);var a="[object AsyncFunction]",i="[object Function]",u="[object GeneratorFunction]",s="[object Proxy]";function c(r){if(!o(r)){return false}var t=n(r);return t==i||t==u||t==a||t==s}r.exports=c},30294:r=>{var t=9007199254740991;function e(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=t}r.exports=e},23805:r=>{function t(r){var t=typeof r;return r!=null&&(t=="object"||t=="function")}r.exports=t},40346:r=>{function t(r){return r!=null&&typeof r=="object"}r.exports=t},44394:(r,t,e)=>{var n=e(72552),o=e(40346);var a="[object Symbol]";function i(r){return typeof r=="symbol"||o(r)&&n(r)==a}r.exports=i},37167:(r,t,e)=>{var n=e(4901),o=e(27301),a=e(86009);var i=a&&a.isTypedArray;var u=i?o(i):n;r.exports=u},95950:(r,t,e)=>{var n=e(70695),o=e(88984),a=e(64894);function i(r){return a(r)?n(r):o(r)}r.exports=i},50104:(r,t,e)=>{var n=e(53661);var o="Expected a function";function a(r,t){if(typeof r!="function"||t!=null&&typeof t!="function"){throw new TypeError(o)}var e=function(){var n=arguments,o=t?t.apply(this,n):n[0],a=e.cache;if(a.has(o)){return a.get(o)}var i=r.apply(this,n);e.cache=a.set(o,i)||a;return i};e.cache=new(a.Cache||n);return e}a.Cache=n;r.exports=a},63345:r=>{function t(){return[]}r.exports=t},89935:r=>{function t(){return false}r.exports=t},13222:(r,t,e)=>{var n=e(77556);function o(r){return r==null?"":n(r)}r.exports=o}}]);