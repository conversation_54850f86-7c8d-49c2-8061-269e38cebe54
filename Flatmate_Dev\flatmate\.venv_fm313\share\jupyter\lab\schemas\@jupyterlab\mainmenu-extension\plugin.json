{"title": "Main Menu", "description": "Main JupyterLab menu settings.", "jupyter.lab.menus": {"main": [{"id": "jp-mainmenu-file", "label": "File", "items": [{"type": "submenu", "submenu": {"id": "jp-mainmenu-file-new", "label": "New", "items": []}, "rank": 0}, {"type": "separator", "rank": 2}, {"command": "filemenu:create-console", "rank": 2.1}, {"command": "filemenu:close-and-cleanup", "rank": 3.1}, {"type": "separator", "rank": 99}, {"command": "filemenu:logout", "rank": 99}, {"command": "filemenu:shutdown", "rank": 99}], "rank": 1}, {"id": "jp-mainmenu-edit", "label": "Edit", "items": [{"command": "editmenu:undo", "rank": 0}, {"command": "editmenu:redo", "rank": 0}, {"type": "separator", "rank": 10}, {"command": "editmenu:clear-current", "rank": 10}, {"command": "editmenu:clear-all", "rank": 10}, {"type": "separator", "rank": 200}, {"command": "editmenu:go-to-line", "rank": 200}], "rank": 2}, {"id": "jp-mainmenu-view", "label": "View", "items": [{"type": "separator", "rank": 10}, {"command": "viewmenu:line-numbering", "rank": 10}, {"command": "viewmenu:match-brackets", "rank": 10}, {"command": "viewmenu:word-wrap", "rank": 10}], "rank": 3}, {"id": "jp-mainmenu-run", "label": "Run", "items": [{"command": "runmenu:run", "rank": 0}, {"type": "separator"}, {"command": "runmenu:run-all", "rank": 999}, {"command": "runmenu:restart-and-run-all", "rank": 999}], "rank": 4}, {"id": "jp-mainmenu-kernel", "label": "<PERSON><PERSON>", "items": [{"command": "kernelmenu:interrupt", "rank": 0}, {"type": "separator", "rank": 1}, {"command": "kernelmenu:restart", "rank": 1}, {"command": "kernelmenu:restart-and-clear", "rank": 1}, {"command": "runmenu:restart-and-run-all", "rank": 1.1}, {"type": "separator", "rank": 1.5}, {"command": "kernelmenu:reconnect-to-kernel", "rank": 1.5}, {"type": "separator", "rank": 2}, {"command": "kernelmenu:shutdown", "rank": 2}, {"command": "kernelmenu:shutdownAll", "rank": 2}, {"type": "separator", "rank": 3}, {"command": "kernelmenu:change", "rank": 3}], "rank": 5}, {"id": "jp-mainmenu-tabs", "label": "Tabs", "items": [{"command": "application:activate-next-tab", "rank": 0}, {"command": "application:activate-previous-tab", "rank": 0}, {"command": "application:activate-next-tab-bar", "rank": 0}, {"command": "application:activate-previous-tab-bar", "rank": 0}, {"command": "tabsmenu:activate-previously-used-tab", "rank": 0}], "rank": 500}, {"id": "jp-mainmenu-settings", "label": "Settings", "items": [{"command": "settingeditor:open", "rank": 1000}], "rank": 999}, {"id": "jp-mainmenu-help", "label": "Help", "items": [], "rank": 1000}], "context": [{"command": "filemenu:create-console", "selector": "[data-type=\"document-title\"].jp-mod-current", "rank": 10}, {"command": "recentmenu:reopen-last", "selector": "#jp-main-dock-panel .lm-DockPanel-tabBar .lm-TabBar-tab", "rank": 6}]}, "jupyter.lab.shortcuts": [{"command": "editmenu:clear-all", "keys": [""], "selector": "[data-jp-undoer]"}, {"command": "editmenu:clear-current", "keys": [""], "selector": "[data-jp-undoer]"}, {"command": "editmenu:find", "keys": [""], "selector": "[data-jp-undoer]"}, {"command": "editmenu:find-and-replace", "keys": [""], "selector": "[data-jp-undoer]"}, {"command": "editmenu:redo", "keys": ["Accel Shift Z"], "selector": "[data-jp-undoer]"}, {"command": "editmenu:undo", "keys": ["Accel Z"], "selector": "[data-jp-undoer]"}, {"command": "filemenu:close-and-cleanup", "keys": ["Ctrl Shift Q"], "selector": ".jp-Activity"}, {"command": "kernelmenu:interrupt", "keys": ["I", "I"], "selector": "[data-jp-kernel-user]:not(.jp-mod-readWrite) :focus:not(:read-write)"}, {"command": "kernelmenu:restart", "keys": ["0", "0"], "selector": "[data-jp-kernel-user]:not(.jp-mod-readWrite) :focus:not(:read-write)"}, {"command": "kernelmenu:restart-and-clear", "keys": [""], "selector": "[data-jp-kernel-user]:not(.jp-mod-readWrite) :focus:not(:read-write)"}, {"command": "kernelmenu:shutdown", "keys": [""], "selector": "[data-jp-kernel-user]:not(.jp-mod-readWrite) :focus:not(:read-write)"}, {"command": "runmenu:restart-and-run-all", "keys": [""], "selector": "[data-jp-code-runner]"}, {"command": "runmenu:run", "keys": ["Shift Enter"], "selector": "[data-jp-code-runner]"}, {"command": "runmenu:run-all", "keys": [""], "selector": "[data-jp-code-runner]"}, {"command": "tabsmenu:activate-previously-used-tab", "keys": ["Accel Shift '"], "selector": "body"}, {"command": "recentmenu:reopen-last", "keys": ["Accel Shift T"], "selector": "body"}], "jupyter.lab.transform": true, "properties": {"menus": {"title": "The application menu description.", "description": "Note: To disable a menu or a menu item,\ncopy it to User Preferences and add the\n\"disabled\" key. The following example will disable\nthe \"Tabs\" menu and \"Restart Kernel and Run up to Selected Cell\"\nitem:\n{\n  \"menus\": [\n    {\n      \"id\": \"jp-mainmenu-tabs\",\n      \"disabled\": true\n    },\n    {\n      \"id\": \"jp-mainmenu-kernel\",\n      \"items\": [\n        {\n          \"command\": \"notebook:restart-and-run-to-selected\",\n          \"disabled\": true\n        }\n      ]\n    }\n  ]\n}\n\nMenu description:", "items": {"$ref": "#/definitions/menu"}, "type": "array", "default": []}}, "additionalProperties": false, "definitions": {"menu": {"properties": {"disabled": {"description": "Whether the menu is disabled or not", "type": "boolean", "default": false}, "icon": {"description": "Menu icon id", "type": "string"}, "id": {"description": "Menu unique id", "type": "string", "pattern": "[a-z][a-z0-9\\-_]+"}, "items": {"description": "Menu items", "type": "array", "items": {"$ref": "#/definitions/menuItem"}}, "label": {"description": "Menu label", "type": "string"}, "mnemonic": {"description": "Mnemonic index for the label", "type": "number", "minimum": -1, "default": -1}, "rank": {"description": "Menu rank", "type": "number", "minimum": 0}}, "required": ["id"], "additionalProperties": false, "type": "object"}, "menuItem": {"properties": {"args": {"description": "Command arguments", "type": "object"}, "command": {"description": "Command id", "type": "string"}, "disabled": {"description": "Whether the item is disabled or not", "type": "boolean", "default": false}, "type": {"description": "Item type", "type": "string", "enum": ["command", "submenu", "separator"], "default": "command"}, "rank": {"description": "Item rank", "type": "number", "minimum": 0}, "submenu": {"description": "Submenu definition", "oneOf": [{"$ref": "#/definitions/menu"}, {"type": "null"}]}}, "additionalProperties": false, "type": "object"}}, "type": "object"}