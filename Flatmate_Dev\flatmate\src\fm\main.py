#!/usr/bin/env python3
"""
Main entry point for the FlatMate application.
"""

import sys
from pathlib import Path

from PySide6.QtWidgets import QApplication
from .core.config import config
from .core.config.paths import AppPaths
from .core.config.keys import ConfigKeys
from .core.services.event_bus import global_event_bus
from .core.services.logger import log
from .gui.main_window import MainWindow
from .module_coordinator import ModuleCoordinator


def initialize_application() -> tuple:
    """
    Comprehensive application initialization.

    Sets up logging, configuration, and ensures required directories exist.

    Returns:
        tuple: (QApplication, MainWindow, ModuleCoordinator) - The main application objects
    """
    # 0. Ensure all required directories exist
    AppPaths.ensure_directories()

    # 1. Logging is now handled by the log object, which configures itself.
    log.info("Application starting...")
    
    try:
        # 2. Publish debug mode event if enabled
        if config.get_value(ConfigKeys.App.DEBUG_MODE, False):
            global_event_bus.publish('debug_mode_enabled')
        
        # Directory creation is handled by SystemPaths/UserPaths in config.py and by logger.py for its specific log directory.
        # The loop for core_paths has been removed.

        # 3. Initialize and show the main application window
        app = QApplication(sys.argv)
        
        # 5. Load and apply styles
        from .gui.styles import apply_styles
        apply_styles(app)
        
        # 4. Create Main Window and show immediately in fullscreen
        main_window = MainWindow()
        main_window.resize(1200, 800)  # Set initial size for windowed mode
        main_window.showMaximized()  # Open in fullscreen/maximized mode

        # 5. Show splash content inside the main window
        splash_path = AppPaths.RESOURCES_DIR / "splash" / "unify_transparent_bg-Splash.png"
        main_window.show_splash_content(str(splash_path))
        main_window.update_splash_message("Initializing Flatmate...")
        app.processEvents()

        # 6. Initialize database and cache behind splash screen
        log.info("\n=== Initializing Database & Cache ===")
        main_window.update_splash_message("Loading database and cache...")
        app.processEvents()

        # Initialize DBIOService singleton (this will warm the cache)
        from .core.data_services.db_io_service import DBIOService
        db_service = DBIOService()  # Cache warming happens here (0.73s)

        # 7. Initialize Auto-Import Manager (if enabled)
        log.info("\n=== Initializing Auto-Import Manager ===")
        main_window.update_splash_message("Setting up auto-import monitoring...")
        app.processEvents()

        from .core.services.auto_import_manager import AutoImportManager
        auto_import_manager = AutoImportManager()
        if auto_import_manager.enabled:
            auto_import_manager.start()
            log.info("Auto-import monitoring started")
        else:
            log.info("Auto-import is disabled")

        # 8. Create and initialize coordinator
        log.info("\n=== Setting up Module Coordinator ===")
        main_window.update_splash_message("Setting up modules...")
        app.processEvents()

        coordinator = ModuleCoordinator(main_window)

        main_window.update_splash_message("Preparing interface...")
        app.processEvents()

        coordinator.initialize_modules()  # Heavy table rendering happens here (4.46s)

        main_window.update_splash_message("Finalizing interface...")

        # Set the coordinator in the main window
        main_window.set_module_manager(coordinator)

        # 9. Start coordinator
        coordinator.start()  # Start coordinator to transition to home

        # 10. Check for pending auto-import files and navigate if needed
        log.info("\n=== Checking Auto-Import Status ===")
        main_window.update_splash_message("Checking for pending imports...")
        app.processEvents()

        from .modules.update_data.view_context_manager import UpdateDataViewManager
        view_manager = UpdateDataViewManager()

        if view_manager.should_open_update_data_on_startup():
            log.info("Pending auto-import files detected - opening Update Data module")
            coordinator.request_transition("update_data")
        else:
            log.info("No pending auto-import files - staying on home")

        # 11. Hide splash content and show actual application content
        main_window.hide_splash_content()

        log.info("\n=== Application Ready ===")
        return app, main_window, coordinator
        
    except Exception as e:
        log.critical(f"Fatal error during application initialization: {e}")
        import traceback
        log.critical(f"Exception details: {traceback.format_exc()}")
        raise


def main():
    """Application entry point."""
    app, window, coordinator = initialize_application()
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
