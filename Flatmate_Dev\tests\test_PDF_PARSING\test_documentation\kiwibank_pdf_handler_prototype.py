#!/usr/bin/env python3
"""
Kiwibank PDF Statement Handler Prototype

This prototype demonstrates how to extract and process data from Kiwibank PDF statements
based on the test results from the PDF parsing test suite.
"""

import os
import camelot
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import re


class KiwibankPdfHandler:
    """Handler for Kiwibank PDF statements"""
    
    def __init__(self):
        """Initialize the handler"""
        self.account_pattern = re.compile(r'(\d{2}-\d{4}-\d{7}-\d{2})')
    
    def can_handle_file(self, filepath: str) -> bool:
        """
        Determine if this handler can process the given file
        
        Args:
            filepath: Path to the PDF file
            
        Returns:
            bool: True if this handler can process the file, False otherwise
        """
        if not filepath.lower().endswith('.pdf'):
            return False
            
        try:
            # Extract text from first page using pdfplumber for quick check
            import pdfplumber
            with pdfplumber.open(filepath) as pdf:
                first_page_text = pdf.pages[0].extract_text()
                
                # Check for Kiwibank identifiers
                if 'Kiwibank' in first_page_text and 'Statement' in first_page_text:
                    return True
        except Exception as e:
            print(f"Error checking file: {e}")
            
        return False
    
    def process_file(self, filepath: str) -> Dict[str, Any]:
        """
        Process a Kiwibank PDF statement file
        
        Args:
            filepath: Path to the PDF file
            
        Returns:
            Dict containing extracted data:
            {
                'accounts': List of account dictionaries with balances
                'transactions': List of transaction dictionaries (if available)
                'metadata': Statement metadata
            }
        """
        result = {
            'accounts': [],
            'transactions': [],
            'metadata': {
                'statement_date': None,
                'account_holder': None,
                'statement_period': None
            }
        }
        
        # Extract basic metadata using pdfplumber
        self._extract_metadata(filepath, result)
        
        # Extract account balances using Camelot stream
        self._extract_account_balances(filepath, result)
        
        # Extract transactions (if available)
        self._extract_transactions(filepath, result)
        
        return result
    
    def _extract_metadata(self, filepath: str, result: Dict[str, Any]) -> None:
        """Extract metadata from the PDF using pdfplumber"""
        try:
            import pdfplumber
            with pdfplumber.open(filepath) as pdf:
                first_page_text = pdf.pages[0].extract_text()
                
                # Extract account holder
                name_match = re.search(r'([A-Z]\s[A-Z]\s[A-Z\-]+)', first_page_text)
                if name_match:
                    result['metadata']['account_holder'] = name_match.group(1).strip()
                
                # Extract statement date
                date_match = re.search(r'as at (\d{1,2}\s\w+\s\d{4})', first_page_text)
                if date_match:
                    result['metadata']['statement_date'] = date_match.group(1).strip()
                
        except Exception as e:
            print(f"Error extracting metadata: {e}")
    
    def _extract_account_balances(self, filepath: str, result: Dict[str, Any]) -> None:
        """Extract account balances using Camelot stream"""
        try:
            # Extract tables using Camelot stream method (best for Kiwibank based on tests)
            tables = camelot.read_pdf(filepath, flavor='stream')
            
            if len(tables) > 0:
                # Process the first table which contains account balances
                balance_table = tables[0].df
                
                # Clean up the table
                for i in range(3, len(balance_table)):  # Skip header rows
                    account_info = {}
                    
                    # Extract account name
                    if len(balance_table.iloc[i, 0].strip()) > 0:
                        account_info['account_name'] = balance_table.iloc[i, 0].strip()
                    
                    # Extract account number if present
                    account_text = ' '.join([str(balance_table.iloc[i, j]) for j in range(balance_table.shape[1])])
                    account_match = self.account_pattern.search(account_text)
                    if account_match:
                        account_info['account_number'] = account_match.group(1)
                    
                    # Extract balance
                    balance_text = balance_table.iloc[i, -1].strip()
                    if balance_text.startswith('$'):
                        # Remove $ and convert to float
                        try:
                            balance_value = float(balance_text.replace('$', '').replace(',', ''))
                            account_info['balance'] = balance_value
                        except ValueError:
                            account_info['balance'] = balance_text
                    
                    if account_info:
                        result['accounts'].append(account_info)
                        
        except Exception as e:
            print(f"Error extracting account balances: {e}")
    
    def _extract_transactions(self, filepath: str, result: Dict[str, Any]) -> None:
        """
        Extract transactions if available
        Note: This is a placeholder as transaction extraction requires more analysis
        """
        # This would need to be implemented based on further analysis of transaction pages
        pass


def main():
    """Test the handler with a sample PDF"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python kiwibank_pdf_handler_prototype.py <pdf_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        sys.exit(1)
    
    handler = KiwibankPdfHandler()
    
    # Check if handler can process the file
    if handler.can_handle_file(pdf_path):
        print(f"Handler can process: {pdf_path}")
        
        # Process the file
        result = handler.process_file(pdf_path)
        
        # Display results
        print("\nExtracted Data:")
        print(f"Statement Date: {result['metadata']['statement_date']}")
        print(f"Account Holder: {result['metadata']['account_holder']}")
        
        print("\nAccounts:")
        for i, account in enumerate(result['accounts']):
            print(f"  Account {i+1}:")
            for key, value in account.items():
                print(f"    {key}: {value}")
    else:
        print(f"Handler cannot process: {pdf_path}")


if __name__ == "__main__":
    main()
