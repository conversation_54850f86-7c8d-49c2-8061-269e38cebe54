---
description: "Windsurf pointer to unified work session workflow"
type: "pointer_workflow"
target: "flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md"
---

# Work Session Workflow (Windsurf)

**This is a pointer workflow. The complete workflow is located at:**
`flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md`

---

## Quick Start for Windsurf

### 1. **Open Complete Workflow**
```
File → Open → flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md
```

### 2. **Windsurf-Specific Setup**
```bash
# Use Windsurf integrated terminal
Ctrl+` (open terminal)

# Create session folder
mkdir "flatmate/DOCS/_FEATURES/<SESSION_TYPE>_<name>"

# Use Windsurf file explorer to navigate
# Copy template using right-click → Copy/Paste
```

### 3. **Windsurf Optimizations**
- **Split Editor**: Keep SESSION_LOG.md open in split pane
- **Integrated Terminal**: Use for session setup commands
- **File Explorer**: Organize evidence files visually
- **Git Integration**: Use built-in git for commits with session references

---

## Session Type Selection

**Choose your session type and follow the appropriate workflow:**

### **FEATURE Development**
→ Follow: `flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md`
→ Plus: Feature Protocol v1.1 (`.augment/rules/feature-protocol_v1.1.md`)

### **REFACTOR Work**
→ Follow: `flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md`
→ Plus: GUI Component Protocol if applicable

### **TROUBLESHOOT Issues**
→ Follow: `flatmate/DOCS/_PROTOCOLS/WORKFLOWS/troubleshoot-enhanced.md`
→ Integrated with unified session protocol

### **MAINTENANCE Tasks**
→ Follow: `flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md`
→ Standard session protocol applies

---

## Windsurf-Specific Features

### **File Management**
- **Explorer Panel**: Organize session files visually
- **Quick Open**: `Ctrl+P` to quickly access templates
- **File Comparison**: Compare before/after states
- **Folder Structure**: Use explorer to maintain evidence organization

### **Documentation**
- **Markdown Preview**: Preview SESSION_LOG.md as you write
- **Split Editor**: Keep workflow and session log open simultaneously
- **Auto-save**: Enable for continuous documentation
- **Snippets**: Create custom snippets for quick logging

### **Development Integration**
- **Integrated Terminal**: All session setup in one place
- **Git Integration**: Visual git operations with session context
- **Debugging Tools**: Integrated debugging for troubleshooting
- **Extension Ecosystem**: Leverage Windsurf extensions

---

## Quick Commands

### **Session Setup**
```bash
# In Windsurf terminal
mkdir "flatmate/DOCS/_FEATURES/REFACTOR_toolbar_migration"
cd "flatmate/DOCS/_FEATURES/REFACTOR_toolbar_migration"
cp "../../_ARCHITECTURE/SESSION_LOG_TEMPLATE.md" "SESSION_LOG.md"
mkdir -p "EVIDENCE/{error_logs,screenshots,code_samples}"
```

### **Evidence Collection**
```bash
# Save error logs
cp /path/to/error.log "EVIDENCE/error_logs/$(date +%Y%m%d_%H%M%S)_error.log"

# Screenshot using Windsurf
# Use built-in screenshot tools or external tools
```

### **Session Completion**
```bash
# Git commit with session reference
git add .
git commit -m "REFACTOR: Toolbar base class migration

Session: REFACTOR_toolbar_migration
Docs: flatmate/DOCS/_FEATURES/REFACTOR_toolbar_migration/
Lessons: Documented in SESSION_LOG.md"
```

---

## Integration with Windsurf Workflow

### **Project Setup**
1. **Open Project**: Ensure Flatmate project is open in Windsurf
2. **Terminal Setup**: Configure terminal to project root
3. **Extension Setup**: Install markdown, git, and Python extensions

### **During Work**
1. **Split Layout**: Code on left, SESSION_LOG.md on right
2. **Terminal Access**: Use integrated terminal for commands
3. **File Navigation**: Use explorer for evidence organization
4. **Git Integration**: Use built-in git for version control

### **Session End**
1. **Complete Documentation**: Finish SESSION_LOG.md
2. **Create Changelog**: Use template from _ARCHITECTURE folder
3. **Commit Changes**: Include session reference in commit message
4. **Archive Evidence**: Organize files in EVIDENCE folder

---

## Troubleshooting Windsurf Issues

### **Workflow Access Problems**
- **File not found**: Check path `flatmate/DOCS/_PROTOCOLS/WORKFLOWS/`
- **Permission issues**: Ensure read access to DOCS folder
- **Template missing**: Verify `_ARCHITECTURE/SESSION_LOG_TEMPLATE.md` exists

### **Integration Issues**
- **Terminal not working**: Restart Windsurf or check terminal settings
- **Git integration broken**: Check git configuration in Windsurf
- **File explorer issues**: Refresh explorer or restart Windsurf

---

## Next Steps

1. **Open Complete Workflow**: Navigate to the full workflow document
2. **Follow Session Protocol**: Use unified work session workflow
3. **Leverage Windsurf Features**: Use integrated tools for efficiency
4. **Document Everything**: Maintain comprehensive session logs

---

**For the complete workflow details, open:**
`flatmate/DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md`
