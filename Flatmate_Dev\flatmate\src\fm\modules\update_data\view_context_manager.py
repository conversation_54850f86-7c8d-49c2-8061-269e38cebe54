#!/usr/bin/env python3
"""
Manages the view state and component visibility for the Update Data module.

This class centralizes the logic for arranging the UI based on the current
workflow (Database Mode vs. File-Only Mode) and context (e.g., whether
auto-import is configured).
"""

from ...core.services.logger import log


class UpdateDataViewManager:
    """Manages the dynamic state of the UpdateDataView."""

    def configure_view_for_workflow(
        self, view, is_database_mode: bool, auto_import_status: dict = None
    ):
        """Configures the UI layout based on the selected workflow.

        Args:
            view: The UpdateDataView instance to manipulate.
            is_database_mode: True if in Database Mode, False if in File-Only Mode.
            auto_import_status: Dict with auto-import status info (enabled, path, pending_files)
        """
        if auto_import_status is None:
            auto_import_status = self.get_auto_import_status()

        log.debug(
            f"Configuring view for workflow: is_db_mode={is_database_mode}, "
            f"auto_import_enabled={auto_import_status.get('enabled', False)}, "
            f"pending_files={len(auto_import_status.get('pending_files', []))}"
        )

        if is_database_mode:
            self._configure_for_database_mode(view, auto_import_status)
        else:
            self._configure_for_file_only_mode(view)

    def _configure_for_database_mode(self, view, auto_import_status: dict):
        """Set UI for Database Mode."""
        log.debug("Setting UI for DATABASE mode.")

        # --- Left Panel --- #
        # In database mode, hide save location controls (data goes to database)
        view.left_buttons.save_label.setVisible(False)
        view.left_buttons.save_combo.setVisible(False)
        view.left_buttons.save_select_btn.setVisible(False)

        # Show source controls - these will adapt based on auto-import status
        view.left_buttons.source_label.setVisible(True)
        view.left_buttons.source_combo.setVisible(True)

        # --- Center Panel (Dashboard vs. Onboarding) --- #
        if auto_import_status.get('enabled', False) and auto_import_status.get('path'):
            # Auto-import is configured - show dashboard
            self._show_auto_import_dashboard(view, auto_import_status)
        else:
            # Auto-import not configured - show onboarding
            self._show_auto_import_onboarding(view)

    def _configure_for_file_only_mode(self, view):
        """Set UI for File-Only Mode."""
        log.debug("Setting UI for FILE-ONLY mode.")

        # --- Left Panel --- #
        # In file-only mode, both source and save are required
        view.left_buttons.source_label.setVisible(True)
        view.left_buttons.source_combo.setVisible(True)
        view.left_buttons.source_select_btn.setVisible(True)

        view.left_buttons.save_label.setVisible(True)
        view.left_buttons.save_combo.setVisible(True)
        view.left_buttons.save_select_btn.setVisible(True)

        # --- Center Panel --- #
        # Show welcome pane with file-only mode instructions
        view.center_display.show_welcome_pane()

    def get_auto_import_status(self) -> dict:
        """Get current auto-import status including pending files.

        Returns:
            Dict with keys: enabled, path, pending_files, last_check
        """
        from ...core.config import config
        from ...core.config.keys import ConfigKeys

        enabled = config.get_value(ConfigKeys.AutoImport.ENABLED, False)
        import_path = config.get_value(ConfigKeys.AutoImport.IMPORT_PATH, "")

        status = {
            'enabled': enabled,
            'path': import_path,
            'pending_files': [],
            'last_check': None
        }

        # If enabled and path exists, check for pending files
        if enabled and import_path:
            status['pending_files'] = self._scan_for_pending_files(import_path)

        return status

    def _scan_for_pending_files(self, import_path: str) -> list:
        """Scan import folder for new CSV files.

        Args:
            import_path: Path to the auto-import folder

        Returns:
            List of file paths that need to be imported
        """
        try:
            from pathlib import Path
            import_dir = Path(import_path)

            if not import_dir.exists():
                log.warning(f"Auto-import folder does not exist: {import_path}")
                return []

            # Find all CSV files in the import directory
            csv_files = list(import_dir.glob("*.csv"))

            # TODO: Filter out already processed files
            # For now, return all CSV files
            pending_files = [str(f) for f in csv_files]

            log.debug(f"Found {len(pending_files)} pending files in {import_path}")
            return pending_files

        except Exception as e:
            log.error(f"Error scanning auto-import folder: {e}")
            return []

    def _show_auto_import_dashboard(self, view, auto_import_status: dict):
        """Show the auto-import dashboard with pending files."""
        pending_files = auto_import_status.get('pending_files', [])
        import_path = auto_import_status.get('path', '')

        if pending_files:
            # Show file pane with pending files
            view.center_display.set_files(pending_files, import_path)
            view.center_display.show_file_pane()
            log.debug(f"Showing dashboard with {len(pending_files)} pending files")
        else:
            # Show welcome pane with "no files" message
            view.center_display.show_welcome_pane()
            log.debug("Showing dashboard with no pending files")

    def _show_auto_import_onboarding(self, view):
        """Show onboarding message for auto-import setup."""
        view.center_display.show_welcome_pane()
        log.debug("Showing auto-import onboarding")

    def should_open_update_data_on_startup(self) -> bool:
        """Check if app should open to Update Data module on startup.

        This checks if there are pending files in the auto-import folder.

        Returns:
            True if Update Data should be the initial module
        """
        auto_import_status = self.get_auto_import_status()

        # Open to Update Data if auto-import is enabled and has pending files
        if (auto_import_status.get('enabled', False) and
            auto_import_status.get('pending_files')):
            log.info(f"Opening to Update Data - found {len(auto_import_status['pending_files'])} pending files")
            return True

        return False
