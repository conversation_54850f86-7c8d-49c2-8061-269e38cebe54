#!/usr/bin/env python3
"""
Manages the view state and component visibility for the Update Data module.

This class centralizes the logic for arranging the UI based on the current
workflow (Database Mode vs. File-Only Mode) and context (e.g., whether
auto-import is configured).
"""

from ...core.services.logger import log


class UpdateDataViewManager:
    """Manages the dynamic state of the UpdateDataView."""

    def configure_view_for_workflow(
        self, view, is_database_mode: bool, is_auto_import_configured: bool
    ):
        """Configures the UI layout based on the selected workflow.

        Args:
            view: The UpdateDataView instance to manipulate.
            is_database_mode: True if in Database Mode, False if in File-Only Mode.
            is_auto_import_configured: True if the auto-import folder has been set.
        """
        log.debug(
            f"Configuring view for workflow: is_db_mode={is_database_mode}, "
            f"is_auto_import_configured={is_auto_import_configured}"
        )

        if is_database_mode:
            self._configure_for_database_mode(view, is_auto_import_configured)
        else:
            self._configure_for_file_only_mode(view)

    def _configure_for_database_mode(
        self, view, is_auto_import_configured: bool
    ):
        """Set UI for Database Mode."""
        log.debug("Setting UI for DATABASE mode.")
        # --- Left Panel --- #
        # In database mode, the 'save location' is irrelevant, so we hide it.
        view.left_buttons.save_label.setVisible(False)
        view.left_buttons.save_combo.setVisible(False)
        view.left_buttons.save_select_btn.setVisible(False)

        # The source is implicitly the auto-import folder. The UI for selecting
        # this folder remains visible.
        view.left_buttons.source_label.setVisible(True)
        view.left_buttons.source_combo.setVisible(True)
        view.left_buttons.source_select_btn.setVisible(True)

        # --- Center Panel (Dashboard vs. Onboarding) --- #
        if is_auto_import_configured:
            # Show the dashboard with file lists, etc.
            view.center_display.show_file_pane()
        else:
            # Show the onboarding message to configure the auto-import folder.
            view.center_display.show_welcome_pane()

    def _configure_for_file_only_mode(self, view):
        """Set UI for File-Only Mode."""
        log.debug("Setting UI for FILE-ONLY mode.")
        # --- Left Panel --- #
        # In file-only mode, both source and save are required.
        view.left_buttons.source_label.setVisible(True)
        view.left_buttons.source_combo.setVisible(True)
        view.left_buttons.source_select_btn.setVisible(True)

        view.left_buttons.save_label.setVisible(True)
        view.left_buttons.save_combo.setVisible(True)
        view.left_buttons.save_select_btn.setVisible(True)

        # --- Center Panel --- #
        # Start with a clean slate, show the welcome/initial pane.
        view.center_display.show_welcome_pane()
        # --- Left Panel --- #
        # Hide database-centric controls
        view.set_auto_import_visible(False)

        # Show file-only controls
        view.set_save_location_visible(True)

        # --- Center Panel (File Staging) --- #
        # Hide all database-mode panels
        view.set_onboarding_panel_visible(False)
        view.set_dashboard_panel_visible(False)

        # Show the file staging area (to be implemented)
        # view.set_file_staging_visible(True)
