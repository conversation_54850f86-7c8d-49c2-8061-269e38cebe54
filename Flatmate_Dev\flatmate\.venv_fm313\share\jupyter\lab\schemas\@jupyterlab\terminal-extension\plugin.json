{"jupyter.lab.setting-icon": "ui-components:terminal", "jupyter.lab.setting-icon-label": "Terminal", "jupyter.lab.menus": {"context": [{"command": "terminal:copy", "selector": ".jp-Terminal", "rank": 1}, {"command": "terminal:paste", "selector": ".jp-Terminal", "rank": 2}, {"command": "terminal:refresh", "selector": ".jp-Terminal", "rank": 3}]}, "title": "Terminal", "description": "Terminal settings.", "definitions": {"fontFamily": {"type": "string"}, "fontSize": {"type": "integer", "minimum": 9, "maximum": 72}, "lineHeight": {"type": "number", "minimum": 1.0}, "theme": {"enum": ["dark", "light", "inherit"]}, "scrollback": {"type": "number"}, "pasteWithCtrlV": {"type": "boolean"}, "macOptionIsMeta": {"type": "boolean"}}, "properties": {"fontFamily": {"title": "<PERSON>ont family", "description": "The font family used to render text.", "$ref": "#/definitions/fontFamily", "default": "monospace"}, "fontSize": {"title": "Font size", "description": "The font size used to render text.", "$ref": "#/definitions/fontSize", "default": 13}, "lineHeight": {"title": "Line height", "description": "The line height used to render text.", "$ref": "#/definitions/lineHeight", "default": 1.0}, "theme": {"title": "Theme", "description": "The theme for the terminal.", "$ref": "#/definitions/theme", "default": "inherit"}, "screenReaderMode": {"title": "Screen Reader Mode", "description": "Add accessibility elements for use with screen readers.", "type": "boolean", "default": false}, "scrollback": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "The amount of scrollback beyond initial viewport", "$ref": "#/definitions/lineHeight", "default": 1000}, "shutdownOnClose": {"title": "Shut down on close", "description": "Shut down the session when closing the terminal.", "type": "boolean", "default": false}, "closeOnExit": {"title": "Close on exit", "description": "Close the widget when exiting the terminal.", "type": "boolean", "default": true}, "pasteWithCtrlV": {"title": "Paste with Ctrl+V", "description": "Enable pasting with Ctrl+V.  This can be disabled to use Ctrl+V in the vi editor, for instance.  This setting has no effect on macOS, where Cmd+V is available", "type": "boolean", "default": true}, "macOptionIsMeta": {"title": "Treat option as meta key on macOS", "description": "Option key on macOS can be used as meta key. This enables to use shortcuts such as option + f to move cursor forward one word", "type": "boolean", "default": false}, "cursorBlink": {"title": "Blinking cursor", "description": "Whether to blink the cursor. Changes require reopening the terminal.", "type": "boolean", "default": true}, "showStatusBarItem": {"type": "string", "title": "Show the status bar item", "description": "Whether to show the running terminals item in the status bar", "default": "if-any", "oneOf": [{"const": "if-any", "title": "Show if one or more"}, {"const": "always", "title": "Always show"}, {"const": "never", "title": "Don't show"}]}}, "additionalProperties": false, "type": "object"}