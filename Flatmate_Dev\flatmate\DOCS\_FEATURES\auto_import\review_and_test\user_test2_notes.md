new protype pop up gui works 
system works
csv's succesfully imported
shifted to designated archive folder

notes - on auto_import archive folder set 
the save location should default to that folder
else: it should stay "same as source"
behaviour = imported_files should stay in the import folder

in all cases:
there should be immediate feedback in the center panel
(check existing gui infrastructure for this functionality)

There should be a poll activated on app start up 
  'new files detected, update database?'
app should open at update_data module

the option in source location should just be 'auto_import'
select button should appear only if no option has been set
or if auto import is disabled then no source set
auto import should have a check box and lable
it should be set to enabled by default - unless no source folder has been set 


in fact - we could just stick with the original set source folder logic
if auto import is enabled - then that folder is set to the auto import folder
(pros cons use cases)




# other update data ux notes 

- update master csv should be an option 
- update data base should be an option  
-  both **gui shared components check box and label (inline)**

# general ux notes 
- I'm now wondering if nav bar should be on the left 
- should have an expanded state with in line text labels for each option - for new users - or tool tips
- what is now left panel in categorise modules should be right panel and be collapsible upward maximising space for spread sheets ?