# PDF Parsing Test Documentation

This folder contains documentation of the PDF parsing test process, results, and findings.

## Test Overview
- **Test Date:** Current date
- **Test PDF:** `tests/test_pdfs/kiwibank/2024-Dec-23_Personal.pdf`
- **Output Directory:** `tests/test_PDF_PARSING/test_pdf_parser_output`
- **Test Script:** `tests/test_PDF_PARSING/test_pdf_parsing_packages.py`

## Test Process
1. Verified required packages in virtual environment `.venv_fm313`
2. Running test script on target PDF
3. Analyzing results

## Test Results
Results will be documented here after test execution.
