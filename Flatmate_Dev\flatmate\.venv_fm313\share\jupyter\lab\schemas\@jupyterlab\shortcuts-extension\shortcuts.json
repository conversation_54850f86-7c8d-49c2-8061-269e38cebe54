{"jupyter.lab.setting-icon": "ui-components:keyboard", "jupyter.lab.setting-icon-label": "Keyboard Shortcuts", "jupyter.lab.transform": true, "title": "Keyboard Shortcuts", "description": "Keyboard shortcut settings.", "jupyter.lab.menus": {"context": [{"command": "shortcuts:edit-keybinding", "selector": ".jp-Shortcuts-ShortcutKeysContainer", "rank": 0}, {"command": "shortcuts:delete-keybinding", "selector": ".jp-Shortcuts-ShortcutKeysContainer", "rank": 1}, {"command": "shortcuts:add-keybinding", "selector": ".jp-Shortcuts-Row", "rank": 2}, {"command": "shortcuts:toggle-selectors", "selector": ".jp-Shortcuts-Top", "rank": 3}, {"command": "shortcuts:reset-all", "selector": ".jp-Shortcuts-Top", "rank": 4}]}, "type": "object", "additionalProperties": false, "properties": {"shortcuts": {"description": "The list of keyboard shortcuts.", "items": {"$ref": "#/definitions/shortcut"}, "type": "array", "default": []}}, "definitions": {"shortcut": {"properties": {"args": {"type": "object"}, "command": {"type": "string"}, "keys": {"items": {"type": "string"}, "type": "array"}, "winKeys": {"items": {"type": "string"}, "type": "array"}, "macKeys": {"items": {"type": "string"}, "type": "array"}, "linuxKeys": {"items": {"type": "string"}, "type": "array"}, "selector": {"type": "string"}, "preventDefault": {"type": "boolean"}}, "required": ["command", "keys", "selector"], "type": "object"}}}