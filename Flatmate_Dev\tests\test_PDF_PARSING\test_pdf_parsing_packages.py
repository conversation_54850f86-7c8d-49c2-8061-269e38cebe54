#!/usr/bin/env python3
"""
PDF Parsing Package Test Suite

Tests the PDF parsing packages mentioned in the PDF parsing workflow document:
- Camelot (for table extraction with lattice and stream flavors)
- pdfplumber (for complex text and table extraction)
- Basic PDF text extraction capabilities

Outputs results in human-readable CSV format for easy analysis.
"""

import sys
import csv
import time
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import tempfile
import os

# Test if packages are available
PACKAGES_AVAILABLE = {}

try:
    import camelot
    PACKAGES_AVAILABLE['camelot'] = True
    print("[OK] Camelot available")
except ImportError:
    PACKAGES_AVAILABLE['camelot'] = False
    print("[X] Camelot not available - install with: pip install camelot-py[cv]")

try:
    import pdfplumber
    PACKAGES_AVAILABLE['pdfplumber'] = True
    print("[OK] pdfplumber available")
except ImportError:
    PACKAGES_AVAILABLE['pdfplumber'] = False
    print("[X] pdfplumber not available - install with: pip install pdfplumber")

try:
    import pandas as pd
    PACKAGES_AVAILABLE['pandas'] = True
    print("[OK] pandas available")
except ImportError:
    PACKAGES_AVAILABLE['pandas'] = False
    print("[X] pandas not available - install with: pip install pandas")

try:
    import PyPDF2
    PACKAGES_AVAILABLE['pypdf2'] = True
    print("[OK] PyPDF2 available")
except ImportError:
    PACKAGES_AVAILABLE['pypdf2'] = False
    print("[X] PyPDF2 not available - install with: pip install PyPDF2")


class PDFParsingTester:
    """Test suite for PDF parsing packages."""
    
    def __init__(self, output_file: str = "pdf_parsing_test_results.csv"):
        """Initialize the tester with output file path."""
        self.output_file = output_file
        self.results = []
        self.test_start_time = datetime.now()
    
    def create_sample_pdf(self) -> str:
        """Create a simple sample PDF for testing if no PDF files are available."""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            temp_path = temp_file.name
            temp_file.close()
            
            c = canvas.Canvas(temp_path, pagesize=letter)
            
            # Add some sample content that mimics a bank statement
            c.drawString(100, 750, "Sample Bank Statement")
            c.drawString(100, 730, "Account Number: 12-3456-7890123-00")
            c.drawString(100, 710, "Statement Period: 01/01/2024 - 31/01/2024")
            
            # Add a simple table
            c.drawString(100, 680, "Date        Description                Amount")
            c.drawString(100, 660, "01/01/2024  Opening Balance           $1,000.00")
            c.drawString(100, 640, "02/01/2024  Coffee Shop Purchase        -$4.50")
            c.drawString(100, 620, "03/01/2024  Salary Deposit            $2,500.00")
            c.drawString(100, 600, "04/01/2024  Grocery Store              -$85.30")
            
            c.save()
            return temp_path
            
        except ImportError:
            print("[WARNING] reportlab not available - cannot create sample PDF")
            return None
    
    def test_camelot_lattice(self, pdf_path: str) -> Dict[str, Any]:
        """Test Camelot with lattice flavor (for grid-based tables)."""
        if not PACKAGES_AVAILABLE['camelot']:
            return {
                'package': 'camelot',
                'method': 'lattice',
                'status': 'PACKAGE_NOT_AVAILABLE',
                'error': 'Camelot not installed',
                'tables_found': 0,
                'execution_time': 0,
                'data_quality': 'N/A'
            }
        
        start_time = time.time()
        try:
            tables = camelot.read_pdf(pdf_path, flavor='lattice')
            execution_time = time.time() - start_time
            
            # Analyze results
            tables_found = len(tables)
            data_quality = 'GOOD' if tables_found > 0 else 'NO_TABLES'
            
            # Get sample data if available
            sample_data = ""
            if tables_found > 0:
                df = tables[0].df
                sample_data = f"Shape: {df.shape}, Columns: {list(df.columns)[:3]}"
            
            return {
                'package': 'camelot',
                'method': 'lattice',
                'status': 'SUCCESS',
                'error': '',
                'tables_found': tables_found,
                'execution_time': round(execution_time, 3),
                'data_quality': data_quality,
                'sample_data': sample_data
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            return {
                'package': 'camelot',
                'method': 'lattice',
                'status': 'ERROR',
                'error': str(e)[:100],
                'tables_found': 0,
                'execution_time': round(execution_time, 3),
                'data_quality': 'FAILED'
            }
    
    def test_camelot_stream(self, pdf_path: str) -> Dict[str, Any]:
        """Test Camelot with stream flavor (for whitespace-separated tables)."""
        if not PACKAGES_AVAILABLE['camelot']:
            return {
                'package': 'camelot',
                'method': 'stream',
                'status': 'PACKAGE_NOT_AVAILABLE',
                'error': 'Camelot not installed',
                'tables_found': 0,
                'execution_time': 0,
                'data_quality': 'N/A'
            }
        
        start_time = time.time()
        try:
            tables = camelot.read_pdf(pdf_path, flavor='stream')
            execution_time = time.time() - start_time
            
            tables_found = len(tables)
            data_quality = 'GOOD' if tables_found > 0 else 'NO_TABLES'
            
            sample_data = ""
            if tables_found > 0:
                df = tables[0].df
                sample_data = f"Shape: {df.shape}, Non-empty cells: {df.count().sum()}"
            
            return {
                'package': 'camelot',
                'method': 'stream',
                'status': 'SUCCESS',
                'error': '',
                'tables_found': tables_found,
                'execution_time': round(execution_time, 3),
                'data_quality': data_quality,
                'sample_data': sample_data
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            return {
                'package': 'camelot',
                'method': 'stream',
                'status': 'ERROR',
                'error': str(e)[:100],
                'tables_found': 0,
                'execution_time': round(execution_time, 3),
                'data_quality': 'FAILED'
            }
    
    def test_pdfplumber_text(self, pdf_path: str) -> Dict[str, Any]:
        """Test pdfplumber text extraction capabilities."""
        if not PACKAGES_AVAILABLE['pdfplumber']:
            return {
                'package': 'pdfplumber',
                'method': 'extract_text',
                'status': 'PACKAGE_NOT_AVAILABLE',
                'error': 'pdfplumber not installed',
                'pages_processed': 0,
                'execution_time': 0,
                'data_quality': 'N/A'
            }
        
        start_time = time.time()
        try:
            with pdfplumber.open(pdf_path) as pdf:
                pages_processed = len(pdf.pages)
                total_text = ""
                
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        total_text += text
                
                execution_time = time.time() - start_time
                
                # Analyze text quality
                text_length = len(total_text.strip())
                data_quality = 'GOOD' if text_length > 100 else 'POOR' if text_length > 0 else 'NO_TEXT'
                
                # Find potential anchor points
                anchors_found = []
                common_anchors = ['Account', 'Statement', 'Date', 'Amount', 'Description', 'Balance']
                for anchor in common_anchors:
                    if anchor.lower() in total_text.lower():
                        anchors_found.append(anchor)
                
                return {
                    'package': 'pdfplumber',
                    'method': 'extract_text',
                    'status': 'SUCCESS',
                    'error': '',
                    'pages_processed': pages_processed,
                    'execution_time': round(execution_time, 3),
                    'data_quality': data_quality,
                    'sample_data': f"Text length: {text_length}, Anchors: {anchors_found[:3]}"
                }
                
        except Exception as e:
            execution_time = time.time() - start_time
            return {
                'package': 'pdfplumber',
                'method': 'extract_text',
                'status': 'ERROR',
                'error': str(e)[:100],
                'pages_processed': 0,
                'execution_time': round(execution_time, 3),
                'data_quality': 'FAILED'
            }
    
    def test_pdfplumber_tables(self, pdf_path: str) -> Dict[str, Any]:
        """Test pdfplumber table extraction capabilities."""
        if not PACKAGES_AVAILABLE['pdfplumber']:
            return {
                'package': 'pdfplumber',
                'method': 'extract_tables',
                'status': 'PACKAGE_NOT_AVAILABLE',
                'error': 'pdfplumber not installed',
                'tables_found': 0,
                'execution_time': 0,
                'data_quality': 'N/A'
            }
        
        start_time = time.time()
        try:
            with pdfplumber.open(pdf_path) as pdf:
                total_tables = 0
                sample_table_info = ""
                
                for page_num, page in enumerate(pdf.pages):
                    tables = page.extract_tables()
                    if tables:
                        total_tables += len(tables)
                        if not sample_table_info and tables[0]:
                            table = tables[0]
                            sample_table_info = f"Page {page_num+1}: {len(table)} rows, {len(table[0]) if table else 0} cols"
                
                execution_time = time.time() - start_time
                data_quality = 'GOOD' if total_tables > 0 else 'NO_TABLES'
                
                return {
                    'package': 'pdfplumber',
                    'method': 'extract_tables',
                    'status': 'SUCCESS',
                    'error': '',
                    'tables_found': total_tables,
                    'execution_time': round(execution_time, 3),
                    'data_quality': data_quality,
                    'sample_data': sample_table_info
                }
                
        except Exception as e:
            execution_time = time.time() - start_time
            return {
                'package': 'pdfplumber',
                'method': 'extract_tables',
                'status': 'ERROR',
                'error': str(e)[:100],
                'tables_found': 0,
                'execution_time': round(execution_time, 3),
                'data_quality': 'FAILED'
            }
    
    def test_pypdf2_basic(self, pdf_path: str) -> Dict[str, Any]:
        """Test basic PyPDF2 text extraction (baseline comparison)."""
        if not PACKAGES_AVAILABLE['pypdf2']:
            return {
                'package': 'PyPDF2',
                'method': 'extract_text',
                'status': 'PACKAGE_NOT_AVAILABLE',
                'error': 'PyPDF2 not installed',
                'pages_processed': 0,
                'execution_time': 0,
                'data_quality': 'N/A'
            }
        
        start_time = time.time()
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                pages_processed = len(pdf_reader.pages)
                total_text = ""
                
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        total_text += text
                
                execution_time = time.time() - start_time
                text_length = len(total_text.strip())
                data_quality = 'GOOD' if text_length > 50 else 'POOR' if text_length > 0 else 'NO_TEXT'
                
                return {
                    'package': 'PyPDF2',
                    'method': 'extract_text',
                    'status': 'SUCCESS',
                    'error': '',
                    'pages_processed': pages_processed,
                    'execution_time': round(execution_time, 3),
                    'data_quality': data_quality,
                    'sample_data': f"Text length: {text_length}"
                }
                
        except Exception as e:
            execution_time = time.time() - start_time
            return {
                'package': 'PyPDF2',
                'method': 'extract_text',
                'status': 'ERROR',
                'error': str(e)[:100],
                'pages_processed': 0,
                'execution_time': round(execution_time, 3),
                'data_quality': 'FAILED'
            }
    
    def test_pdf_file(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Run all tests on a single PDF file."""
        print(f"\n📄 Testing PDF: {pdf_path}")
        
        file_results = []
        
        # Test all methods
        tests = [
            self.test_camelot_lattice,
            self.test_camelot_stream,
            self.test_pdfplumber_text,
            self.test_pdfplumber_tables,
            self.test_pypdf2_basic
        ]
        
        for test_func in tests:
            print(f"  Running {test_func.__name__}...", end=" ")
            result = test_func(pdf_path)
            result['pdf_file'] = Path(pdf_path).name
            result['test_timestamp'] = datetime.now().isoformat()
            file_results.append(result)
            
            # Print quick status
            status_icon = "✓" if result['status'] == 'SUCCESS' else "✗" if result['status'] == 'ERROR' else "⚠"
            print(f"{status_icon} {result['status']}")
        
        return file_results
    
    def save_results_to_csv(self):
        """Save all test results to CSV file."""
        if not self.results:
            print("⚠️  No results to save")
            return
        
        # Define CSV columns
        fieldnames = [
            'pdf_file', 'package', 'method', 'status', 'error', 
            'tables_found', 'pages_processed', 'execution_time', 
            'data_quality', 'sample_data', 'test_timestamp'
        ]
        
        with open(self.output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in self.results:
                # Ensure all fields exist
                row = {field: result.get(field, '') for field in fieldnames}
                writer.writerow(row)
        
        print(f"\n[RESULTS] Results saved to: {self.output_file}")
    
    def print_summary(self):
        """Print a human-readable summary of test results."""
        if not self.results:
            return
        
        print(f"\n{'='*60}")
        print("PDF PARSING PACKAGE TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total tests run: {len(self.results)}")
        
        # Group by package
        by_package = {}
        for result in self.results:
            package = result['package']
            if package not in by_package:
                by_package[package] = []
            by_package[package].append(result)
        
        for package, results in by_package.items():
            print(f"\n📦 {package.upper()}")
            print("-" * 40)
            
            for result in results:
                method = result.get('method', 'N/A')
                status = result['status']
                quality = result.get('data_quality', 'N/A')
                time_taken = result.get('execution_time', 0)
                
                status_icon = "[OK]" if status == 'SUCCESS' else "[X]" if status == 'ERROR' else "[!]"
                print(f"  {status_icon} {method:15} | {status:20} | Quality: {quality:10} | Time: {time_taken}s")
                
                if result.get('error'):
                    print(f"    Error: {result['error']}")
    
    def run_tests(self, pdf_paths: List[str] = None):
        """Run tests on provided PDF files or create a sample."""
        print("[INFO] Starting PDF Parsing Package Tests")
        print(f"Timestamp: {self.test_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # If no PDFs provided, try to create a sample
        if not pdf_paths:
            print("[INFO] No PDF files provided, creating sample PDF...")
            sample_pdf = self.create_sample_pdf()
            if sample_pdf:
                pdf_paths = [sample_pdf]
                print(f"[INFO] Sample PDF created: {sample_pdf}")
            else:
                print("[INFO] Could not create sample PDF and no PDFs provided")
                return
        
        # Test each PDF
        for pdf_path in pdf_paths:
            if not Path(pdf_path).exists():
                print(f"[WARNING] PDF not found: {pdf_path}")
                continue
            
            file_results = self.test_pdf_file(pdf_path)
            self.results.extend(file_results)
        
        # Output results
        self.save_results_to_csv()
        self.print_summary()
        
        # Cleanup sample PDF if created
        for pdf_path in pdf_paths:
            if pdf_path and pdf_path.startswith(tempfile.gettempdir()):
                try:
                    os.unlink(pdf_path)
                    print(f"[INFO] Cleaned up sample PDF: {pdf_path}")
                except:
                    pass


def main():
    """Main function to run the PDF parsing tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test PDF parsing packages')
    parser.add_argument('pdfs', nargs='*', help='PDF files to test (optional)')
    parser.add_argument('--output', '-o', default='pdf_parsing_test_results.csv', 
                       help='Output CSV file (default: pdf_parsing_test_results.csv)')
    
    args = parser.parse_args()
    
    tester = PDFParsingTester(output_file=args.output)
    tester.run_tests(pdf_paths=args.pdfs if args.pdfs else None)


if __name__ == "__main__":
    main()
