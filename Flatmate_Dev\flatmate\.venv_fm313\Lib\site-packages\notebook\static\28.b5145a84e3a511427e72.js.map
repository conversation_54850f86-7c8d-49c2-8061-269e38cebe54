{"version": 3, "file": "28.b5145a84e3a511427e72.js?v=b5145a84e3a511427e72", "mappings": ";;;;;;AAAa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB,GAAG,aAAa,GAAG,wBAAwB,GAAG,iBAAiB;AAC/E;AACA,8BAA8B;AAC9B,iBAAiB;AACjB,uBAAuB,UAAU,SAAS,QAAQ;AAClD;AACA;AACA,iBAAiB;AACjB;AACA;AACA,kCAAkC;AAClC,gCAAgC,UAAU;AAC1C,8BAA8B,QAAQ;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,CAAC;AACD,wBAAwB;AACxB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;;;;;;;ACvHa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,mBAAmB,mBAAO,CAAC,KAAe;AAC1C,cAAc,mBAAO,CAAC,KAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA,CAAC;AACD,eAAe;AACf;AACA;;;;;;;ACxEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,4CAA4C,oCAAoC;AAC9H;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;AC1Ga;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,kDAAkD,knBAAknB;AAC/sB;AACA,CAAC;AACD,eAAe;AACf;;;;;;;ACjEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,+CAA+C,4CAA4C,qBAAqB;AAChH;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;ACnEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,OAAO;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qGAAqG,UAAU;AAC/G;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,6BAA6B;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kFAAkF,UAAU;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA,gDAAgD,8BAA8B;AAC9E;AACA;AACA;AACA,qCAAqC;AACrC,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,4CAA4C,wCAAwC;AAClI;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;ACnJa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB;AAChB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,gDAAgD,oFAAoF;AAChL;AACA,CAAC;AACD,gBAAgB;AAChB;;;;;;;AChGa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,iDAAiD,2EAA2E;AACzK;AACA,CAAC;AACD,iBAAiB;AACjB;;;;;;;ACzDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa;AACb,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,qCAAqC;AACrC,kCAAkC;AAClC,gCAAgC;AAChC,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA,CAAC;AACD,aAAa;AACb;;;;;;;ACzEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,sBAAsB,GAAG,wBAAwB;AACnE,mBAAmB,mBAAO,CAAC,KAAe;AAC1C,mBAAmB,mBAAO,CAAC,KAAc;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,+BAA+B,4BAA4B;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,4BAA4B;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C;AACA,CAAC;AACD,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA,CAAC;AACD,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA,CAAC;AACD,eAAe;AACf;;;;;;;ACjKa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa;AACb,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,gCAAgC;AAChC;AACA,CAAC;AACD,aAAa;AACb;;;;;;;AChDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,8CAA8C,kDAAkD,yDAAyD;AACzJ;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;AC9Ca;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB;AAChB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA,CAAC;AACD,gBAAgB;AAChB;;;;;;;ACjEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB,GAAG,eAAe;AACzC,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA,CAAC;AACD,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;AC7Pa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,aAAa;AACb,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,yCAAyC,iDAAiD,0BAA0B;AACpH;AACA,CAAC;AACD,aAAa;AACb;;;;;;;AChDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,6CAA6C,iDAAiD,8DAA8D;AAC5J;AACA,CAAC;AACD,iBAAiB;AACjB;;;;;;;AC1Ea;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB;AAChB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA,CAAC;AACD,gBAAgB;AAChB;;;;;;;ACtEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,eAAe,GAAG,kBAAkB;AACtD,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,gDAAgD,0CAA0C;AACxI;AACA,CAAC;AACD,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,kCAAkC;AAClC;AACA,CAAC;AACD,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,kCAAkC;AAClC;AACA,CAAC;AACD,eAAe;AACf;;;;;;;AC/Ia;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C,kBAAkB,mBAAO,CAAC,KAAyB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,qIAAqI,4BAA4B;AACjK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,wBAAwB,4BAA4B;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,6CAA6C,4CAA4C,0EAA0E,KAAK,+RAA+R;AACvc;AACA,CAAC;AACD,iBAAiB;AACjB;;;;;;;ACpLa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc;AACd,mBAAmB,mBAAO,CAAC,KAAe;AAC1C,sBAAsB,mBAAO,CAAC,KAAkB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,gDAAgD,yIAAyI;AACnO;AACA,CAAC;AACD,cAAc;AACd;;;;;;;ACzEa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB;AAChB,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,mCAAmC;AACnC;AACA,CAAC;AACD,gBAAgB;AAChB;;;;;;;ACvDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,GAAG,cAAc;AACtC,mBAAmB,mBAAO,CAAC,KAAe;AAC1C,sBAAsB,mBAAO,CAAC,KAAkB;AAChD,kBAAkB,mBAAO,CAAC,KAAyB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,0CAA0C,4CAA4C,8GAA8G;AACpM;AACA,CAAC;AACD,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,qBAAqB;AACrB;;;;;;;AC9Ka;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB,GAAG,iBAAiB,GAAG,qBAAqB;AAC5D,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA,iDAAiD,gDAAgD,oDAAoD;AACrJ;AACA;AACA,CAAC;AACD,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,oCAAoC;AACpC;AACA,CAAC;AACD,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,mCAAmC;AACnC;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;;;;;;;AC5Ka;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,GAAG,wBAAwB,GAAG,oBAAoB;AACvE,mBAAmB,mBAAO,CAAC,KAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,gDAAgD,gDAAgD,qCAAqC;AACrI;AACA,CAAC;AACD,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,oDAAoD,4CAA4C,4EAA4E;AAC5K;AACA,CAAC;AACD,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL,wCAAwC;AACxC;AACA,CAAC;AACD,qBAAqB;AACrB;;;;;;;ACjGa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,UAAU;AAClF;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MathItem.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/TeXAtom.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/maction.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/math.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/menclose.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mfenced.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mfrac.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mglyph.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mi.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mmultiscripts.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mn.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mpadded.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mroot.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mrow.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/ms.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mspace.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/msqrt.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/msubsup.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mtable.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mtd.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mtext.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/mtr.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/munderover.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlNodes/semantics.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/Tree/Factory.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.newState = exports.STATE = exports.AbstractMathItem = exports.protoItem = void 0;\nfunction protoItem(open, math, close, n, start, end, display) {\n    if (display === void 0) { display = null; }\n    var item = { open: open, math: math, close: close,\n        n: n, start: { n: start }, end: { n: end }, display: display };\n    return item;\n}\nexports.protoItem = protoItem;\nvar AbstractMathItem = (function () {\n    function AbstractMathItem(math, jax, display, start, end) {\n        if (display === void 0) { display = true; }\n        if (start === void 0) { start = { i: 0, n: 0, delim: '' }; }\n        if (end === void 0) { end = { i: 0, n: 0, delim: '' }; }\n        this.root = null;\n        this.typesetRoot = null;\n        this.metrics = {};\n        this.inputData = {};\n        this.outputData = {};\n        this._state = exports.STATE.UNPROCESSED;\n        this.math = math;\n        this.inputJax = jax;\n        this.display = display;\n        this.start = start;\n        this.end = end;\n        this.root = null;\n        this.typesetRoot = null;\n        this.metrics = {};\n        this.inputData = {};\n        this.outputData = {};\n    }\n    Object.defineProperty(AbstractMathItem.prototype, \"isEscaped\", {\n        get: function () {\n            return this.display === null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractMathItem.prototype.render = function (document) {\n        document.renderActions.renderMath(this, document);\n    };\n    AbstractMathItem.prototype.rerender = function (document, start) {\n        if (start === void 0) { start = exports.STATE.RERENDER; }\n        if (this.state() >= start) {\n            this.state(start - 1);\n        }\n        document.renderActions.renderMath(this, document, start);\n    };\n    AbstractMathItem.prototype.convert = function (document, end) {\n        if (end === void 0) { end = exports.STATE.LAST; }\n        document.renderActions.renderConvert(this, document, end);\n    };\n    AbstractMathItem.prototype.compile = function (document) {\n        if (this.state() < exports.STATE.COMPILED) {\n            this.root = this.inputJax.compile(this, document);\n            this.state(exports.STATE.COMPILED);\n        }\n    };\n    AbstractMathItem.prototype.typeset = function (document) {\n        if (this.state() < exports.STATE.TYPESET) {\n            this.typesetRoot = document.outputJax[this.isEscaped ? 'escaped' : 'typeset'](this, document);\n            this.state(exports.STATE.TYPESET);\n        }\n    };\n    AbstractMathItem.prototype.updateDocument = function (_document) { };\n    AbstractMathItem.prototype.removeFromDocument = function (_restore) {\n        if (_restore === void 0) { _restore = false; }\n    };\n    AbstractMathItem.prototype.setMetrics = function (em, ex, cwidth, lwidth, scale) {\n        this.metrics = {\n            em: em, ex: ex,\n            containerWidth: cwidth,\n            lineWidth: lwidth,\n            scale: scale\n        };\n    };\n    AbstractMathItem.prototype.state = function (state, restore) {\n        if (state === void 0) { state = null; }\n        if (restore === void 0) { restore = false; }\n        if (state != null) {\n            if (state < exports.STATE.INSERTED && this._state >= exports.STATE.INSERTED) {\n                this.removeFromDocument(restore);\n            }\n            if (state < exports.STATE.TYPESET && this._state >= exports.STATE.TYPESET) {\n                this.outputData = {};\n            }\n            if (state < exports.STATE.COMPILED && this._state >= exports.STATE.COMPILED) {\n                this.inputData = {};\n            }\n            this._state = state;\n        }\n        return this._state;\n    };\n    AbstractMathItem.prototype.reset = function (restore) {\n        if (restore === void 0) { restore = false; }\n        this.state(exports.STATE.UNPROCESSED, restore);\n    };\n    return AbstractMathItem;\n}());\nexports.AbstractMathItem = AbstractMathItem;\nexports.STATE = {\n    UNPROCESSED: 0,\n    FINDMATH: 10,\n    COMPILED: 20,\n    CONVERT: 100,\n    METRICS: 110,\n    RERENDER: 125,\n    TYPESET: 150,\n    INSERTED: 200,\n    LAST: 10000\n};\nfunction newState(name, state) {\n    if (name in exports.STATE) {\n        throw Error('State ' + name + ' already exists');\n    }\n    exports.STATE[name] = state;\n}\nexports.newState = newState;\n//# sourceMappingURL=MathItem.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TeXAtom = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar mo_js_1 = require(\"./mo.js\");\nvar TeXAtom = (function (_super) {\n    __extends(TeXAtom, _super);\n    function TeXAtom(factory, attributes, children) {\n        var _this = _super.call(this, factory, attributes, children) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        _this.setProperty('texClass', _this.texClass);\n        return _this;\n    }\n    Object.defineProperty(TeXAtom.prototype, \"kind\", {\n        get: function () {\n            return 'TeXAtom';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(TeXAtom.prototype, \"arity\", {\n        get: function () {\n            return -1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(TeXAtom.prototype, \"notParent\", {\n        get: function () {\n            return this.childNodes[0] && this.childNodes[0].childNodes.length === 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    TeXAtom.prototype.setTeXclass = function (prev) {\n        this.childNodes[0].setTeXclass(null);\n        return this.adjustTeXclass(prev);\n    };\n    TeXAtom.prototype.adjustTeXclass = function (prev) {\n        return prev;\n    };\n    TeXAtom.defaults = __assign({}, MmlNode_js_1.AbstractMmlBaseNode.defaults);\n    return TeXAtom;\n}(MmlNode_js_1.AbstractMmlBaseNode));\nexports.TeXAtom = TeXAtom;\nTeXAtom.prototype.adjustTeXclass = mo_js_1.MmlMo.prototype.adjustTeXclass;\n//# sourceMappingURL=TeXAtom.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMaction = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMaction = (function (_super) {\n    __extends(MmlMaction, _super);\n    function MmlMaction() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMaction.prototype, \"kind\", {\n        get: function () {\n            return 'maction';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMaction.prototype, \"arity\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMaction.prototype, \"selected\", {\n        get: function () {\n            var selection = this.attributes.get('selection');\n            var i = Math.max(1, Math.min(this.childNodes.length, selection)) - 1;\n            return this.childNodes[i] || this.factory.create('mrow');\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMaction.prototype, \"isEmbellished\", {\n        get: function () {\n            return this.selected.isEmbellished;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMaction.prototype, \"isSpacelike\", {\n        get: function () {\n            return this.selected.isSpacelike;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMaction.prototype.core = function () {\n        return this.selected.core();\n    };\n    MmlMaction.prototype.coreMO = function () {\n        return this.selected.coreMO();\n    };\n    MmlMaction.prototype.verifyAttributes = function (options) {\n        _super.prototype.verifyAttributes.call(this, options);\n        if (this.attributes.get('actiontype') !== 'toggle' &&\n            this.attributes.getExplicit('selection') !== undefined) {\n            var attributes = this.attributes.getAllAttributes();\n            delete attributes.selection;\n        }\n    };\n    MmlMaction.prototype.setTeXclass = function (prev) {\n        if (this.attributes.get('actiontype') === 'tooltip' && this.childNodes[1]) {\n            this.childNodes[1].setTeXclass(null);\n        }\n        var selected = this.selected;\n        prev = selected.setTeXclass(prev);\n        this.updateTeXclass(selected);\n        return prev;\n    };\n    MmlMaction.prototype.nextToggleSelection = function () {\n        var selection = Math.max(1, this.attributes.get('selection') + 1);\n        if (selection > this.childNodes.length) {\n            selection = 1;\n        }\n        this.attributes.set('selection', selection);\n    };\n    MmlMaction.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlNode.defaults), { actiontype: 'toggle', selection: 1 });\n    return MmlMaction;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMaction = MmlMaction;\n//# sourceMappingURL=maction.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMath = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMath = (function (_super) {\n    __extends(MmlMath, _super);\n    function MmlMath() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMath.prototype, \"kind\", {\n        get: function () {\n            return 'math';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMath.prototype, \"linebreakContainer\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMath.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        if (this.attributes.get('mode') === 'display') {\n            this.attributes.setInherited('display', 'block');\n        }\n        attributes = this.addInheritedAttributes(attributes, this.attributes.getAllAttributes());\n        display = (!!this.attributes.get('displaystyle') ||\n            (!this.attributes.get('displaystyle') && this.attributes.get('display') === 'block'));\n        this.attributes.setInherited('displaystyle', display);\n        level = (this.attributes.get('scriptlevel') ||\n            this.constructor.defaults['scriptlevel']);\n        _super.prototype.setChildInheritedAttributes.call(this, attributes, display, level, prime);\n    };\n    MmlMath.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlLayoutNode.defaults), { mathvariant: 'normal', mathsize: 'normal', mathcolor: '', mathbackground: 'transparent', dir: 'ltr', scriptlevel: 0, displaystyle: false, display: 'inline', maxwidth: '', overflow: 'linebreak', altimg: '', 'altimg-width': '', 'altimg-height': '', 'altimg-valign': '', alttext: '', cdgroup: '', scriptsizemultiplier: 1 / Math.sqrt(2), scriptminsize: '8px', infixlinebreakstyle: 'before', lineleading: '1ex', linebreakmultchar: '\\u2062', indentshift: 'auto', indentalign: 'auto', indenttarget: '', indentalignfirst: 'indentalign', indentshiftfirst: 'indentshift', indentalignlast: 'indentalign', indentshiftlast: 'indentshift' });\n    return MmlMath;\n}(MmlNode_js_1.AbstractMmlLayoutNode));\nexports.MmlMath = MmlMath;\n//# sourceMappingURL=math.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMenclose = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMenclose = (function (_super) {\n    __extends(MmlMenclose, _super);\n    function MmlMenclose() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMenclose.prototype, \"kind\", {\n        get: function () {\n            return 'menclose';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMenclose.prototype, \"arity\", {\n        get: function () {\n            return -1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMenclose.prototype, \"linebreakContininer\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMenclose.prototype.setTeXclass = function (prev) {\n        prev = this.childNodes[0].setTeXclass(prev);\n        this.updateTeXclass(this.childNodes[0]);\n        return prev;\n    };\n    MmlMenclose.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlNode.defaults), { notation: 'longdiv' });\n    return MmlMenclose;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMenclose = MmlMenclose;\n//# sourceMappingURL=menclose.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMfenced = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMfenced = (function (_super) {\n    __extends(MmlMfenced, _super);\n    function MmlMfenced() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.INNER;\n        _this.separators = [];\n        _this.open = null;\n        _this.close = null;\n        return _this;\n    }\n    Object.defineProperty(MmlMfenced.prototype, \"kind\", {\n        get: function () {\n            return 'mfenced';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMfenced.prototype.setTeXclass = function (prev) {\n        this.getPrevClass(prev);\n        if (this.open) {\n            prev = this.open.setTeXclass(prev);\n        }\n        if (this.childNodes[0]) {\n            prev = this.childNodes[0].setTeXclass(prev);\n        }\n        for (var i = 1, m = this.childNodes.length; i < m; i++) {\n            if (this.separators[i - 1]) {\n                prev = this.separators[i - 1].setTeXclass(prev);\n            }\n            if (this.childNodes[i]) {\n                prev = this.childNodes[i].setTeXclass(prev);\n            }\n        }\n        if (this.close) {\n            prev = this.close.setTeXclass(prev);\n        }\n        this.updateTeXclass(this.open);\n        return prev;\n    };\n    MmlMfenced.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        var e_1, _a;\n        this.addFakeNodes();\n        try {\n            for (var _b = __values([this.open, this.close].concat(this.separators)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                if (child) {\n                    child.setInheritedAttributes(attributes, display, level, prime);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        _super.prototype.setChildInheritedAttributes.call(this, attributes, display, level, prime);\n    };\n    MmlMfenced.prototype.addFakeNodes = function () {\n        var e_2, _a;\n        var _b = this.attributes.getList('open', 'close', 'separators'), open = _b.open, close = _b.close, separators = _b.separators;\n        open = open.replace(/[ \\t\\n\\r]/g, '');\n        close = close.replace(/[ \\t\\n\\r]/g, '');\n        separators = separators.replace(/[ \\t\\n\\r]/g, '');\n        if (open) {\n            this.open = this.fakeNode(open, { fence: true, form: 'prefix' }, MmlNode_js_1.TEXCLASS.OPEN);\n        }\n        if (separators) {\n            while (separators.length < this.childNodes.length - 1) {\n                separators += separators.charAt(separators.length - 1);\n            }\n            var i = 0;\n            try {\n                for (var _c = __values(this.childNodes.slice(1)), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var child = _d.value;\n                    if (child) {\n                        this.separators.push(this.fakeNode(separators.charAt(i++)));\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n        if (close) {\n            this.close = this.fakeNode(close, { fence: true, form: 'postfix' }, MmlNode_js_1.TEXCLASS.CLOSE);\n        }\n    };\n    MmlMfenced.prototype.fakeNode = function (c, properties, texClass) {\n        if (properties === void 0) { properties = {}; }\n        if (texClass === void 0) { texClass = null; }\n        var text = this.factory.create('text').setText(c);\n        var node = this.factory.create('mo', properties, [text]);\n        node.texClass = texClass;\n        node.parent = this;\n        return node;\n    };\n    MmlMfenced.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlNode.defaults), { open: '(', close: ')', separators: ',' });\n    return MmlMfenced;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMfenced = MmlMfenced;\n//# sourceMappingURL=mfenced.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMfrac = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMfrac = (function (_super) {\n    __extends(MmlMfrac, _super);\n    function MmlMfrac() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMfrac.prototype, \"kind\", {\n        get: function () {\n            return 'mfrac';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMfrac.prototype, \"arity\", {\n        get: function () {\n            return 2;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMfrac.prototype, \"linebreakContainer\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMfrac.prototype.setTeXclass = function (prev) {\n        var e_1, _a;\n        this.getPrevClass(prev);\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                child.setTeXclass(null);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return this;\n    };\n    MmlMfrac.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        if (!display || level > 0) {\n            level++;\n        }\n        this.childNodes[0].setInheritedAttributes(attributes, false, level, prime);\n        this.childNodes[1].setInheritedAttributes(attributes, false, level, true);\n    };\n    MmlMfrac.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlBaseNode.defaults), { linethickness: 'medium', numalign: 'center', denomalign: 'center', bevelled: false });\n    return MmlMfrac;\n}(MmlNode_js_1.AbstractMmlBaseNode));\nexports.MmlMfrac = MmlMfrac;\n//# sourceMappingURL=mfrac.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMglyph = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMglyph = (function (_super) {\n    __extends(MmlMglyph, _super);\n    function MmlMglyph() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMglyph.prototype, \"kind\", {\n        get: function () {\n            return 'mglyph';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMglyph.prototype.verifyAttributes = function (options) {\n        var _a = this.attributes.getList('src', 'fontfamily', 'index'), src = _a.src, fontfamily = _a.fontfamily, index = _a.index;\n        if (src === '' && (fontfamily === '' || index === '')) {\n            this.mError('mglyph must have either src or fontfamily and index attributes', options, true);\n        }\n        else {\n            _super.prototype.verifyAttributes.call(this, options);\n        }\n    };\n    MmlMglyph.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlTokenNode.defaults), { alt: '', src: '', index: '', width: 'auto', height: 'auto', valign: '0em' });\n    return MmlMglyph;\n}(MmlNode_js_1.AbstractMmlTokenNode));\nexports.MmlMglyph = MmlMglyph;\n//# sourceMappingURL=mglyph.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMi = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMi = (function (_super) {\n    __extends(MmlMi, _super);\n    function MmlMi() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMi.prototype, \"kind\", {\n        get: function () {\n            return 'mi';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMi.prototype.setInheritedAttributes = function (attributes, display, level, prime) {\n        if (attributes === void 0) { attributes = {}; }\n        if (display === void 0) { display = false; }\n        if (level === void 0) { level = 0; }\n        if (prime === void 0) { prime = false; }\n        _super.prototype.setInheritedAttributes.call(this, attributes, display, level, prime);\n        var text = this.getText();\n        if (text.match(MmlMi.singleCharacter) && !attributes.mathvariant) {\n            this.attributes.setInherited('mathvariant', 'italic');\n        }\n    };\n    MmlMi.prototype.setTeXclass = function (prev) {\n        this.getPrevClass(prev);\n        var name = this.getText();\n        if (name.length > 1 && name.match(MmlMi.operatorName) &&\n            this.attributes.get('mathvariant') === 'normal' &&\n            this.getProperty('autoOP') === undefined &&\n            this.getProperty('texClass') === undefined) {\n            this.texClass = MmlNode_js_1.TEXCLASS.OP;\n            this.setProperty('autoOP', true);\n        }\n        return this;\n    };\n    MmlMi.defaults = __assign({}, MmlNode_js_1.AbstractMmlTokenNode.defaults);\n    MmlMi.operatorName = /^[a-z][a-z0-9]*$/i;\n    MmlMi.singleCharacter = /^[\\uD800-\\uDBFF]?.[\\u0300-\\u036F\\u1AB0-\\u1ABE\\u1DC0-\\u1DFF\\u20D0-\\u20EF]*$/;\n    return MmlMi;\n}(MmlNode_js_1.AbstractMmlTokenNode));\nexports.MmlMi = MmlMi;\n//# sourceMappingURL=mi.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlNone = exports.MmlMprescripts = exports.MmlMmultiscripts = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar msubsup_js_1 = require(\"./msubsup.js\");\nvar MmlMmultiscripts = (function (_super) {\n    __extends(MmlMmultiscripts, _super);\n    function MmlMmultiscripts() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMmultiscripts.prototype, \"kind\", {\n        get: function () {\n            return 'mmultiscripts';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMmultiscripts.prototype, \"arity\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMmultiscripts.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        this.childNodes[0].setInheritedAttributes(attributes, display, level, prime);\n        var prescripts = false;\n        for (var i = 1, n = 0; i < this.childNodes.length; i++) {\n            var child = this.childNodes[i];\n            if (child.isKind('mprescripts')) {\n                if (!prescripts) {\n                    prescripts = true;\n                    if (i % 2 === 0) {\n                        var mrow = this.factory.create('mrow');\n                        this.childNodes.splice(i, 0, mrow);\n                        mrow.parent = this;\n                        i++;\n                    }\n                }\n            }\n            else {\n                var primestyle = prime || (n % 2 === 0);\n                child.setInheritedAttributes(attributes, false, level + 1, primestyle);\n                n++;\n            }\n        }\n        if (this.childNodes.length % 2 === (prescripts ? 1 : 0)) {\n            this.appendChild(this.factory.create('mrow'));\n            this.childNodes[this.childNodes.length - 1].setInheritedAttributes(attributes, false, level + 1, prime);\n        }\n    };\n    MmlMmultiscripts.prototype.verifyChildren = function (options) {\n        var prescripts = false;\n        var fix = options['fixMmultiscripts'];\n        for (var i = 0; i < this.childNodes.length; i++) {\n            var child = this.childNodes[i];\n            if (child.isKind('mprescripts')) {\n                if (prescripts) {\n                    child.mError(child.kind + ' can only appear once in ' + this.kind, options, true);\n                }\n                else {\n                    prescripts = true;\n                    if (i % 2 === 0 && !fix) {\n                        this.mError('There must be an equal number of prescripts of each type', options);\n                    }\n                }\n            }\n        }\n        if (this.childNodes.length % 2 === (prescripts ? 1 : 0) && !fix) {\n            this.mError('There must be an equal number of scripts of each type', options);\n        }\n        _super.prototype.verifyChildren.call(this, options);\n    };\n    MmlMmultiscripts.defaults = __assign({}, msubsup_js_1.MmlMsubsup.defaults);\n    return MmlMmultiscripts;\n}(msubsup_js_1.MmlMsubsup));\nexports.MmlMmultiscripts = MmlMmultiscripts;\nvar MmlMprescripts = (function (_super) {\n    __extends(MmlMprescripts, _super);\n    function MmlMprescripts() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMprescripts.prototype, \"kind\", {\n        get: function () {\n            return 'mprescripts';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMprescripts.prototype, \"arity\", {\n        get: function () {\n            return 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMprescripts.prototype.verifyTree = function (options) {\n        _super.prototype.verifyTree.call(this, options);\n        if (this.parent && !this.parent.isKind('mmultiscripts')) {\n            this.mError(this.kind + ' must be a child of mmultiscripts', options, true);\n        }\n    };\n    MmlMprescripts.defaults = __assign({}, MmlNode_js_1.AbstractMmlNode.defaults);\n    return MmlMprescripts;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMprescripts = MmlMprescripts;\nvar MmlNone = (function (_super) {\n    __extends(MmlNone, _super);\n    function MmlNone() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlNone.prototype, \"kind\", {\n        get: function () {\n            return 'none';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlNone.prototype, \"arity\", {\n        get: function () {\n            return 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlNone.prototype.verifyTree = function (options) {\n        _super.prototype.verifyTree.call(this, options);\n        if (this.parent && !this.parent.isKind('mmultiscripts')) {\n            this.mError(this.kind + ' must be a child of mmultiscripts', options, true);\n        }\n    };\n    MmlNone.defaults = __assign({}, MmlNode_js_1.AbstractMmlNode.defaults);\n    return MmlNone;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlNone = MmlNone;\n//# sourceMappingURL=mmultiscripts.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMn = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMn = (function (_super) {\n    __extends(MmlMn, _super);\n    function MmlMn() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMn.prototype, \"kind\", {\n        get: function () {\n            return 'mn';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMn.defaults = __assign({}, MmlNode_js_1.AbstractMmlTokenNode.defaults);\n    return MmlMn;\n}(MmlNode_js_1.AbstractMmlTokenNode));\nexports.MmlMn = MmlMn;\n//# sourceMappingURL=mn.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMpadded = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMpadded = (function (_super) {\n    __extends(MmlMpadded, _super);\n    function MmlMpadded() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMpadded.prototype, \"kind\", {\n        get: function () {\n            return 'mpadded';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMpadded.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlLayoutNode.defaults), { width: '', height: '', depth: '', lspace: 0, voffset: 0 });\n    return MmlMpadded;\n}(MmlNode_js_1.AbstractMmlLayoutNode));\nexports.MmlMpadded = MmlMpadded;\n//# sourceMappingURL=mpadded.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMroot = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMroot = (function (_super) {\n    __extends(MmlMroot, _super);\n    function MmlMroot() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMroot.prototype, \"kind\", {\n        get: function () {\n            return 'mroot';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMroot.prototype, \"arity\", {\n        get: function () {\n            return 2;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMroot.prototype.setTeXclass = function (prev) {\n        this.getPrevClass(prev);\n        this.childNodes[0].setTeXclass(null);\n        this.childNodes[1].setTeXclass(null);\n        return this;\n    };\n    MmlMroot.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        this.childNodes[0].setInheritedAttributes(attributes, display, level, true);\n        this.childNodes[1].setInheritedAttributes(attributes, false, level + 2, prime);\n    };\n    MmlMroot.defaults = __assign({}, MmlNode_js_1.AbstractMmlNode.defaults);\n    return MmlMroot;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMroot = MmlMroot;\n//# sourceMappingURL=mroot.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlInferredMrow = exports.MmlMrow = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMrow = (function (_super) {\n    __extends(MmlMrow, _super);\n    function MmlMrow() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this._core = null;\n        return _this;\n    }\n    Object.defineProperty(MmlMrow.prototype, \"kind\", {\n        get: function () {\n            return 'mrow';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMrow.prototype, \"isSpacelike\", {\n        get: function () {\n            var e_1, _a;\n            try {\n                for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var child = _c.value;\n                    if (!child.isSpacelike) {\n                        return false;\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMrow.prototype, \"isEmbellished\", {\n        get: function () {\n            var e_2, _a;\n            var embellished = false;\n            var i = 0;\n            try {\n                for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var child = _c.value;\n                    if (child) {\n                        if (child.isEmbellished) {\n                            if (embellished) {\n                                return false;\n                            }\n                            embellished = true;\n                            this._core = i;\n                        }\n                        else if (!child.isSpacelike) {\n                            return false;\n                        }\n                    }\n                    i++;\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n            return embellished;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMrow.prototype.core = function () {\n        if (!this.isEmbellished || this._core == null) {\n            return this;\n        }\n        return this.childNodes[this._core];\n    };\n    MmlMrow.prototype.coreMO = function () {\n        if (!this.isEmbellished || this._core == null) {\n            return this;\n        }\n        return this.childNodes[this._core].coreMO();\n    };\n    MmlMrow.prototype.nonSpaceLength = function () {\n        var e_3, _a;\n        var n = 0;\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                if (child && !child.isSpacelike) {\n                    n++;\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return n;\n    };\n    MmlMrow.prototype.firstNonSpace = function () {\n        var e_4, _a;\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                if (child && !child.isSpacelike) {\n                    return child;\n                }\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        return null;\n    };\n    MmlMrow.prototype.lastNonSpace = function () {\n        var i = this.childNodes.length;\n        while (--i >= 0) {\n            var child = this.childNodes[i];\n            if (child && !child.isSpacelike) {\n                return child;\n            }\n        }\n        return null;\n    };\n    MmlMrow.prototype.setTeXclass = function (prev) {\n        var e_5, _a, e_6, _b;\n        if (this.getProperty('open') != null || this.getProperty('close') != null) {\n            this.getPrevClass(prev);\n            prev = null;\n            try {\n                for (var _c = __values(this.childNodes), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var child = _d.value;\n                    prev = child.setTeXclass(prev);\n                }\n            }\n            catch (e_5_1) { e_5 = { error: e_5_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n                }\n                finally { if (e_5) throw e_5.error; }\n            }\n            if (this.texClass == null) {\n                this.texClass = MmlNode_js_1.TEXCLASS.INNER;\n            }\n        }\n        else {\n            try {\n                for (var _e = __values(this.childNodes), _f = _e.next(); !_f.done; _f = _e.next()) {\n                    var child = _f.value;\n                    prev = child.setTeXclass(prev);\n                }\n            }\n            catch (e_6_1) { e_6 = { error: e_6_1 }; }\n            finally {\n                try {\n                    if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                }\n                finally { if (e_6) throw e_6.error; }\n            }\n            if (this.childNodes[0]) {\n                this.updateTeXclass(this.childNodes[0]);\n            }\n        }\n        return prev;\n    };\n    MmlMrow.defaults = __assign({}, MmlNode_js_1.AbstractMmlNode.defaults);\n    return MmlMrow;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMrow = MmlMrow;\nvar MmlInferredMrow = (function (_super) {\n    __extends(MmlInferredMrow, _super);\n    function MmlInferredMrow() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlInferredMrow.prototype, \"kind\", {\n        get: function () {\n            return 'inferredMrow';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlInferredMrow.prototype, \"isInferred\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlInferredMrow.prototype, \"notParent\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlInferredMrow.prototype.toString = function () {\n        return '[' + this.childNodes.join(',') + ']';\n    };\n    MmlInferredMrow.defaults = MmlMrow.defaults;\n    return MmlInferredMrow;\n}(MmlMrow));\nexports.MmlInferredMrow = MmlInferredMrow;\n//# sourceMappingURL=mrow.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMs = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMs = (function (_super) {\n    __extends(MmlMs, _super);\n    function MmlMs() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMs.prototype, \"kind\", {\n        get: function () {\n            return 'ms';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMs.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlTokenNode.defaults), { lquote: '\"', rquote: '\"' });\n    return MmlMs;\n}(MmlNode_js_1.AbstractMmlTokenNode));\nexports.MmlMs = MmlMs;\n//# sourceMappingURL=ms.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMspace = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMspace = (function (_super) {\n    __extends(MmlMspace, _super);\n    function MmlMspace() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.NONE;\n        return _this;\n    }\n    MmlMspace.prototype.setTeXclass = function (prev) {\n        return prev;\n    };\n    Object.defineProperty(MmlMspace.prototype, \"kind\", {\n        get: function () {\n            return 'mspace';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMspace.prototype, \"arity\", {\n        get: function () {\n            return 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMspace.prototype, \"isSpacelike\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMspace.prototype, \"hasNewline\", {\n        get: function () {\n            var attributes = this.attributes;\n            return (attributes.getExplicit('width') == null && attributes.getExplicit('height') == null &&\n                attributes.getExplicit('depth') == null && attributes.get('linebreak') === 'newline');\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMspace.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlTokenNode.defaults), { width: '0em', height: '0ex', depth: '0ex', linebreak: 'auto' });\n    return MmlMspace;\n}(MmlNode_js_1.AbstractMmlTokenNode));\nexports.MmlMspace = MmlMspace;\n//# sourceMappingURL=mspace.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMsqrt = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMsqrt = (function (_super) {\n    __extends(MmlMsqrt, _super);\n    function MmlMsqrt() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMsqrt.prototype, \"kind\", {\n        get: function () {\n            return 'msqrt';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMsqrt.prototype, \"arity\", {\n        get: function () {\n            return -1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMsqrt.prototype, \"linebreakContainer\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMsqrt.prototype.setTeXclass = function (prev) {\n        this.getPrevClass(prev);\n        this.childNodes[0].setTeXclass(null);\n        return this;\n    };\n    MmlMsqrt.prototype.setChildInheritedAttributes = function (attributes, display, level, _prime) {\n        this.childNodes[0].setInheritedAttributes(attributes, display, level, true);\n    };\n    MmlMsqrt.defaults = __assign({}, MmlNode_js_1.AbstractMmlNode.defaults);\n    return MmlMsqrt;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMsqrt = MmlMsqrt;\n//# sourceMappingURL=msqrt.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMsup = exports.MmlMsub = exports.MmlMsubsup = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMsubsup = (function (_super) {\n    __extends(MmlMsubsup, _super);\n    function MmlMsubsup() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMsubsup.prototype, \"kind\", {\n        get: function () {\n            return 'msubsup';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMsubsup.prototype, \"arity\", {\n        get: function () {\n            return 3;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMsubsup.prototype, \"base\", {\n        get: function () {\n            return 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMsubsup.prototype, \"sub\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMsubsup.prototype, \"sup\", {\n        get: function () {\n            return 2;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMsubsup.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        var nodes = this.childNodes;\n        nodes[0].setInheritedAttributes(attributes, display, level, prime);\n        nodes[1].setInheritedAttributes(attributes, false, level + 1, prime || this.sub === 1);\n        if (!nodes[2]) {\n            return;\n        }\n        nodes[2].setInheritedAttributes(attributes, false, level + 1, prime || this.sub === 2);\n    };\n    MmlMsubsup.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlBaseNode.defaults), { subscriptshift: '', superscriptshift: '' });\n    return MmlMsubsup;\n}(MmlNode_js_1.AbstractMmlBaseNode));\nexports.MmlMsubsup = MmlMsubsup;\nvar MmlMsub = (function (_super) {\n    __extends(MmlMsub, _super);\n    function MmlMsub() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMsub.prototype, \"kind\", {\n        get: function () {\n            return 'msub';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMsub.prototype, \"arity\", {\n        get: function () {\n            return 2;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMsub.defaults = __assign({}, MmlMsubsup.defaults);\n    return MmlMsub;\n}(MmlMsubsup));\nexports.MmlMsub = MmlMsub;\nvar MmlMsup = (function (_super) {\n    __extends(MmlMsup, _super);\n    function MmlMsup() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMsup.prototype, \"kind\", {\n        get: function () {\n            return 'msup';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMsup.prototype, \"arity\", {\n        get: function () {\n            return 2;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMsup.prototype, \"sup\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMsup.prototype, \"sub\", {\n        get: function () {\n            return 2;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMsup.defaults = __assign({}, MmlMsubsup.defaults);\n    return MmlMsup;\n}(MmlMsubsup));\nexports.MmlMsup = MmlMsup;\n//# sourceMappingURL=msubsup.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMtable = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar string_js_1 = require(\"../../../util/string.js\");\nvar MmlMtable = (function (_super) {\n    __extends(MmlMtable, _super);\n    function MmlMtable() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.properties = {\n            useHeight: true\n        };\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMtable.prototype, \"kind\", {\n        get: function () {\n            return 'mtable';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMtable.prototype, \"linebreakContainer\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMtable.prototype.setInheritedAttributes = function (attributes, display, level, prime) {\n        var e_1, _a;\n        try {\n            for (var indentAttributes_1 = __values(MmlNode_js_1.indentAttributes), indentAttributes_1_1 = indentAttributes_1.next(); !indentAttributes_1_1.done; indentAttributes_1_1 = indentAttributes_1.next()) {\n                var name_1 = indentAttributes_1_1.value;\n                if (attributes[name_1]) {\n                    this.attributes.setInherited(name_1, attributes[name_1][1]);\n                }\n                if (this.attributes.getExplicit(name_1) !== undefined) {\n                    delete (this.attributes.getAllAttributes())[name_1];\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (indentAttributes_1_1 && !indentAttributes_1_1.done && (_a = indentAttributes_1.return)) _a.call(indentAttributes_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        _super.prototype.setInheritedAttributes.call(this, attributes, display, level, prime);\n    };\n    MmlMtable.prototype.setChildInheritedAttributes = function (attributes, display, level, _prime) {\n        var e_2, _a, e_3, _b;\n        try {\n            for (var _c = __values(this.childNodes), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var child = _d.value;\n                if (!child.isKind('mtr')) {\n                    this.replaceChild(this.factory.create('mtr'), child)\n                        .appendChild(child);\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        level = this.getProperty('scriptlevel') || level;\n        display = !!(this.attributes.getExplicit('displaystyle') || this.attributes.getDefault('displaystyle'));\n        attributes = this.addInheritedAttributes(attributes, {\n            columnalign: this.attributes.get('columnalign'),\n            rowalign: 'center'\n        });\n        var cramped = this.attributes.getExplicit('data-cramped');\n        var ralign = (0, string_js_1.split)(this.attributes.get('rowalign'));\n        try {\n            for (var _e = __values(this.childNodes), _f = _e.next(); !_f.done; _f = _e.next()) {\n                var child = _f.value;\n                attributes.rowalign[1] = ralign.shift() || attributes.rowalign[1];\n                child.setInheritedAttributes(attributes, display, level, !!cramped);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    MmlMtable.prototype.verifyChildren = function (options) {\n        var mtr = null;\n        var factory = this.factory;\n        for (var i = 0; i < this.childNodes.length; i++) {\n            var child = this.childNodes[i];\n            if (child.isKind('mtr')) {\n                mtr = null;\n            }\n            else {\n                var isMtd = child.isKind('mtd');\n                if (mtr) {\n                    this.removeChild(child);\n                    i--;\n                }\n                else {\n                    mtr = this.replaceChild(factory.create('mtr'), child);\n                }\n                mtr.appendChild(isMtd ? child : factory.create('mtd', {}, [child]));\n                if (!options['fixMtables']) {\n                    child.parent.removeChild(child);\n                    child.parent = this;\n                    isMtd && mtr.appendChild(factory.create('mtd'));\n                    var merror = child.mError('Children of ' + this.kind + ' must be mtr or mlabeledtr', options, isMtd);\n                    mtr.childNodes[mtr.childNodes.length - 1].appendChild(merror);\n                }\n            }\n        }\n        _super.prototype.verifyChildren.call(this, options);\n    };\n    MmlMtable.prototype.setTeXclass = function (prev) {\n        var e_4, _a;\n        this.getPrevClass(prev);\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                child.setTeXclass(null);\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        return this;\n    };\n    MmlMtable.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlNode.defaults), { align: 'axis', rowalign: 'baseline', columnalign: 'center', groupalign: '{left}', alignmentscope: true, columnwidth: 'auto', width: 'auto', rowspacing: '1ex', columnspacing: '.8em', rowlines: 'none', columnlines: 'none', frame: 'none', framespacing: '0.4em 0.5ex', equalrows: false, equalcolumns: false, displaystyle: false, side: 'right', minlabelspacing: '0.8em' });\n    return MmlMtable;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMtable = MmlMtable;\n//# sourceMappingURL=mtable.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMtd = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar Attributes_js_1 = require(\"../Attributes.js\");\nvar MmlMtd = (function (_super) {\n    __extends(MmlMtd, _super);\n    function MmlMtd() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMtd.prototype, \"kind\", {\n        get: function () {\n            return 'mtd';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMtd.prototype, \"arity\", {\n        get: function () {\n            return -1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMtd.prototype, \"linebreakContainer\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMtd.prototype.verifyChildren = function (options) {\n        if (this.parent && !this.parent.isKind('mtr')) {\n            this.mError(this.kind + ' can only be a child of an mtr or mlabeledtr', options, true);\n            return;\n        }\n        _super.prototype.verifyChildren.call(this, options);\n    };\n    MmlMtd.prototype.setTeXclass = function (prev) {\n        this.getPrevClass(prev);\n        this.childNodes[0].setTeXclass(null);\n        return this;\n    };\n    MmlMtd.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlBaseNode.defaults), { rowspan: 1, columnspan: 1, rowalign: Attributes_js_1.INHERIT, columnalign: Attributes_js_1.INHERIT, groupalign: Attributes_js_1.INHERIT });\n    return MmlMtd;\n}(MmlNode_js_1.AbstractMmlBaseNode));\nexports.MmlMtd = MmlMtd;\n//# sourceMappingURL=mtd.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMtext = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMtext = (function (_super) {\n    __extends(MmlMtext, _super);\n    function MmlMtext() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.texclass = MmlNode_js_1.TEXCLASS.ORD;\n        return _this;\n    }\n    Object.defineProperty(MmlMtext.prototype, \"kind\", {\n        get: function () {\n            return 'mtext';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMtext.prototype, \"isSpacelike\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMtext.defaults = __assign({}, MmlNode_js_1.AbstractMmlTokenNode.defaults);\n    return MmlMtext;\n}(MmlNode_js_1.AbstractMmlTokenNode));\nexports.MmlMtext = MmlMtext;\n//# sourceMappingURL=mtext.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMlabeledtr = exports.MmlMtr = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar Attributes_js_1 = require(\"../Attributes.js\");\nvar string_js_1 = require(\"../../../util/string.js\");\nvar MmlMtr = (function (_super) {\n    __extends(MmlMtr, _super);\n    function MmlMtr() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMtr.prototype, \"kind\", {\n        get: function () {\n            return 'mtr';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMtr.prototype, \"linebreakContainer\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMtr.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        var e_1, _a, e_2, _b;\n        try {\n            for (var _c = __values(this.childNodes), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var child = _d.value;\n                if (!child.isKind('mtd')) {\n                    this.replaceChild(this.factory.create('mtd'), child)\n                        .appendChild(child);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var calign = (0, string_js_1.split)(this.attributes.get('columnalign'));\n        if (this.arity === 1) {\n            calign.unshift(this.parent.attributes.get('side'));\n        }\n        attributes = this.addInheritedAttributes(attributes, {\n            rowalign: this.attributes.get('rowalign'),\n            columnalign: 'center'\n        });\n        try {\n            for (var _e = __values(this.childNodes), _f = _e.next(); !_f.done; _f = _e.next()) {\n                var child = _f.value;\n                attributes.columnalign[1] = calign.shift() || attributes.columnalign[1];\n                child.setInheritedAttributes(attributes, display, level, prime);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    MmlMtr.prototype.verifyChildren = function (options) {\n        var e_3, _a;\n        if (this.parent && !this.parent.isKind('mtable')) {\n            this.mError(this.kind + ' can only be a child of an mtable', options, true);\n            return;\n        }\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                if (!child.isKind('mtd')) {\n                    var mtd = this.replaceChild(this.factory.create('mtd'), child);\n                    mtd.appendChild(child);\n                    if (!options['fixMtables']) {\n                        child.mError('Children of ' + this.kind + ' must be mtd', options);\n                    }\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        _super.prototype.verifyChildren.call(this, options);\n    };\n    MmlMtr.prototype.setTeXclass = function (prev) {\n        var e_4, _a;\n        this.getPrevClass(prev);\n        try {\n            for (var _b = __values(this.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                child.setTeXclass(null);\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        return this;\n    };\n    MmlMtr.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlNode.defaults), { rowalign: Attributes_js_1.INHERIT, columnalign: Attributes_js_1.INHERIT, groupalign: Attributes_js_1.INHERIT });\n    return MmlMtr;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlMtr = MmlMtr;\nvar MmlMlabeledtr = (function (_super) {\n    __extends(MmlMlabeledtr, _super);\n    function MmlMlabeledtr() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMlabeledtr.prototype, \"kind\", {\n        get: function () {\n            return 'mlabeledtr';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMlabeledtr.prototype, \"arity\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return MmlMlabeledtr;\n}(MmlMtr));\nexports.MmlMlabeledtr = MmlMlabeledtr;\n//# sourceMappingURL=mtr.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlMover = exports.MmlMunder = exports.MmlMunderover = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlMunderover = (function (_super) {\n    __extends(MmlMunderover, _super);\n    function MmlMunderover() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMunderover.prototype, \"kind\", {\n        get: function () {\n            return 'munderover';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMunderover.prototype, \"arity\", {\n        get: function () {\n            return 3;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMunderover.prototype, \"base\", {\n        get: function () {\n            return 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMunderover.prototype, \"under\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMunderover.prototype, \"over\", {\n        get: function () {\n            return 2;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMunderover.prototype, \"linebreakContainer\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMunderover.prototype.setChildInheritedAttributes = function (attributes, display, level, prime) {\n        var nodes = this.childNodes;\n        nodes[0].setInheritedAttributes(attributes, display, level, prime || !!nodes[this.over]);\n        var force = !!(!display && nodes[0].coreMO().attributes.get('movablelimits'));\n        var ACCENTS = this.constructor.ACCENTS;\n        nodes[1].setInheritedAttributes(attributes, false, this.getScriptlevel(ACCENTS[1], force, level), prime || this.under === 1);\n        this.setInheritedAccent(1, ACCENTS[1], display, level, prime, force);\n        if (!nodes[2]) {\n            return;\n        }\n        nodes[2].setInheritedAttributes(attributes, false, this.getScriptlevel(ACCENTS[2], force, level), prime || this.under === 2);\n        this.setInheritedAccent(2, ACCENTS[2], display, level, prime, force);\n    };\n    MmlMunderover.prototype.getScriptlevel = function (accent, force, level) {\n        if (force || !this.attributes.get(accent)) {\n            level++;\n        }\n        return level;\n    };\n    MmlMunderover.prototype.setInheritedAccent = function (n, accent, display, level, prime, force) {\n        var node = this.childNodes[n];\n        if (this.attributes.getExplicit(accent) == null && node.isEmbellished) {\n            var value = node.coreMO().attributes.get('accent');\n            this.attributes.setInherited(accent, value);\n            if (value !== this.attributes.getDefault(accent)) {\n                node.setInheritedAttributes({}, display, this.getScriptlevel(accent, force, level), prime);\n            }\n        }\n    };\n    MmlMunderover.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlBaseNode.defaults), { accent: false, accentunder: false, align: 'center' });\n    MmlMunderover.ACCENTS = ['', 'accentunder', 'accent'];\n    return MmlMunderover;\n}(MmlNode_js_1.AbstractMmlBaseNode));\nexports.MmlMunderover = MmlMunderover;\nvar MmlMunder = (function (_super) {\n    __extends(MmlMunder, _super);\n    function MmlMunder() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMunder.prototype, \"kind\", {\n        get: function () {\n            return 'munder';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMunder.prototype, \"arity\", {\n        get: function () {\n            return 2;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMunder.defaults = __assign({}, MmlMunderover.defaults);\n    return MmlMunder;\n}(MmlMunderover));\nexports.MmlMunder = MmlMunder;\nvar MmlMover = (function (_super) {\n    __extends(MmlMover, _super);\n    function MmlMover() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlMover.prototype, \"kind\", {\n        get: function () {\n            return 'mover';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMover.prototype, \"arity\", {\n        get: function () {\n            return 2;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMover.prototype, \"over\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlMover.prototype, \"under\", {\n        get: function () {\n            return 2;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlMover.defaults = __assign({}, MmlMunderover.defaults);\n    MmlMover.ACCENTS = ['', 'accent', 'accentunder'];\n    return MmlMover;\n}(MmlMunderover));\nexports.MmlMover = MmlMover;\n//# sourceMappingURL=munderover.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlAnnotation = exports.MmlAnnotationXML = exports.MmlSemantics = void 0;\nvar MmlNode_js_1 = require(\"../MmlNode.js\");\nvar MmlSemantics = (function (_super) {\n    __extends(MmlSemantics, _super);\n    function MmlSemantics() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlSemantics.prototype, \"kind\", {\n        get: function () {\n            return 'semantics';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlSemantics.prototype, \"arity\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MmlSemantics.prototype, \"notParent\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlSemantics.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlBaseNode.defaults), { definitionUrl: null, encoding: null });\n    return MmlSemantics;\n}(MmlNode_js_1.AbstractMmlBaseNode));\nexports.MmlSemantics = MmlSemantics;\nvar MmlAnnotationXML = (function (_super) {\n    __extends(MmlAnnotationXML, _super);\n    function MmlAnnotationXML() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(MmlAnnotationXML.prototype, \"kind\", {\n        get: function () {\n            return 'annotation-xml';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlAnnotationXML.prototype.setChildInheritedAttributes = function () { };\n    MmlAnnotationXML.defaults = __assign(__assign({}, MmlNode_js_1.AbstractMmlNode.defaults), { definitionUrl: null, encoding: null, cd: 'mathmlkeys', name: '', src: null });\n    return MmlAnnotationXML;\n}(MmlNode_js_1.AbstractMmlNode));\nexports.MmlAnnotationXML = MmlAnnotationXML;\nvar MmlAnnotation = (function (_super) {\n    __extends(MmlAnnotation, _super);\n    function MmlAnnotation() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.properties = {\n            isChars: true\n        };\n        return _this;\n    }\n    Object.defineProperty(MmlAnnotation.prototype, \"kind\", {\n        get: function () {\n            return 'annotation';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MmlAnnotation.defaults = __assign({}, MmlAnnotationXML.defaults);\n    return MmlAnnotation;\n}(MmlAnnotationXML));\nexports.MmlAnnotation = MmlAnnotation;\n//# sourceMappingURL=semantics.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractFactory = void 0;\nvar AbstractFactory = (function () {\n    function AbstractFactory(nodes) {\n        var e_1, _a;\n        if (nodes === void 0) { nodes = null; }\n        this.defaultKind = 'unknown';\n        this.nodeMap = new Map();\n        this.node = {};\n        if (nodes === null) {\n            nodes = this.constructor.defaultNodes;\n        }\n        try {\n            for (var _b = __values(Object.keys(nodes)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var kind = _c.value;\n                this.setNodeClass(kind, nodes[kind]);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    }\n    AbstractFactory.prototype.create = function (kind) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        return (this.node[kind] || this.node[this.defaultKind]).apply(void 0, __spreadArray([], __read(args), false));\n    };\n    AbstractFactory.prototype.setNodeClass = function (kind, nodeClass) {\n        this.nodeMap.set(kind, nodeClass);\n        var THIS = this;\n        var KIND = this.nodeMap.get(kind);\n        this.node[kind] = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new (KIND.bind.apply(KIND, __spreadArray([void 0, THIS], __read(args), false)))();\n        };\n    };\n    AbstractFactory.prototype.getNodeClass = function (kind) {\n        return this.nodeMap.get(kind);\n    };\n    AbstractFactory.prototype.deleteNodeClass = function (kind) {\n        this.nodeMap.delete(kind);\n        delete this.node[kind];\n    };\n    AbstractFactory.prototype.nodeIsKind = function (node, kind) {\n        return (node instanceof this.getNodeClass(kind));\n    };\n    AbstractFactory.prototype.getKinds = function () {\n        return Array.from(this.nodeMap.keys());\n    };\n    AbstractFactory.defaultNodes = {};\n    return AbstractFactory;\n}());\nexports.AbstractFactory = AbstractFactory;\n//# sourceMappingURL=Factory.js.map"], "names": [], "sourceRoot": ""}