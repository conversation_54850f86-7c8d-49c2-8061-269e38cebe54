{"flatmate/tests/Archived/test_module.py": true, "flatmate/tests/My_Test_File_Handling/Test_formatting.py": true, "flatmate/tests/My_Test_File_Handling/test_config.py": true, "flatmate/tests/test_data_pipeline.py": true, "flatmate/tests/test_format_detection.py": true, "flatmate/tests/test_format_detector.py": true, "flatmate/tests/test_home/test_home_presenter.py": true, "flatmate/tests/test_home/test_home_view.py": true, "flatmate/tests/test_home.py": true, "flatmate/tests/test_infobar.py": true, "flatmate/tests/test_test_data_manager.py": true, "flatmate/tests/test_ud_view.py": true, "flatmate/tests/test_ud_view_simple.py": true, "flatmate/tests/test_update_data.py": true, "flatmate/tests/test_view.py": true, "tests/test_real_csvs.py::test_handler": true, "flatmate/src/fm/core/data_services/standards/z_archive/Proposed_Enhanced_columns_system/test_enhanced_columns.py": true, "flatmate/src/fm/gui/_shared_components/table_view_v2/test_table_v2.py": true, "flatmate/src/fm/modules/update_data/utils/z_archive/run_pipeline_test.py": true, "flatmate/src/fm/modules/update_data/utils/z_archive/test_statement_validation.py": true, "test_handler.py::test_handlers": true, "flatmate/test_pipeline.py::test_run_pipeline": true, "tests/test_auto_import_manager.py::TestAutoImportManager::test_configuration_loading": true, "tests/test_auto_import_manager.py::TestAutoImportManager::test_disabled_configuration": true, "tests/test_auto_import_manager.py::TestAutoImportManager::test_move_to_archive": true, "tests/test_auto_import_manager.py::TestAutoImportManager::test_move_to_failed": true, "tests/test_auto_import_manager.py::TestAutoImportManager::test_process_single_file_failure": true, "tests/test_auto_import_manager.py::TestAutoImportManager::test_process_single_file_success": true, "tests/test_auto_import_manager.py::TestAutoImportManager::test_queue_file_for_processing": true, "tests/test_auto_import_manager.py::TestAutoImportManager::test_queue_full_handling": true, "tests/test_auto_import_manager.py::TestAutoImportManager::test_start_monitoring": true}